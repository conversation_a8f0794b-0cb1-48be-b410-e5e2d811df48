"""
Tests for LLM Provider Core Plugin.
"""

from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from plugginger.core.exceptions import PluggingerConfigurationError, PluggingerValidationError
from plugginger.plugins.core.llm_provider.llm_provider_plugin import LLMProviderPlugin


class TestLLMProviderPlugin:
    """Tests for LLMProviderPlugin class."""

    def test_initialization(self) -> None:
        """Test plugin initialization."""
        mock_app = Mock()
        plugin = LLMProviderPlugin(app=mock_app)

        assert plugin.validation_service is not None
        assert plugin.default_temperature == 0.1
        assert plugin.default_max_retries == 3
        assert plugin.default_timeout_seconds == 30.0
        assert plugin.provider is None

    @pytest.mark.asyncio
    async def test_setup_with_config(self) -> None:
        """Test plugin setup with configuration."""
        mock_app = Mock()
        plugin = LLMProviderPlugin(app=mock_app)

        # Mock config
        mock_config = Mock()
        mock_config.provider = "openai"
        mock_config.api_key = "test-key"
        mock_config.model = "gpt-4"
        mock_config.default_temperature = 0.2
        # Explicitly set base_url to None to avoid Mock object
        mock_config.base_url = None

        with patch('plugginger.plugins.core.llm_provider.llm_provider_plugin.ProviderFactory') as mock_factory:
            mock_provider = Mock()
            mock_provider.provider_name = "openai"
            mock_factory.create_provider.return_value = mock_provider

            await plugin.setup(mock_config)

            assert plugin.default_temperature == 0.2
            assert plugin.provider == mock_provider
            mock_factory.create_provider.assert_called_once_with(
                provider_type="openai",
                api_key="test-key",
                model="gpt-4",
                base_url=None
            )

    @pytest.mark.asyncio
    async def test_setup_from_env(self) -> None:
        """Test plugin setup from environment variables."""
        mock_app = Mock()
        plugin = LLMProviderPlugin(app=mock_app)

        with patch('plugginger.plugins.core.llm_provider.llm_provider_plugin.ProviderFactory') as mock_factory:
            mock_provider = Mock()
            mock_provider.provider_name = "local"
            mock_factory.create_from_env.return_value = mock_provider

            await plugin.setup()

            assert plugin.provider == mock_provider
            mock_factory.create_from_env.assert_called_once()

    @pytest.mark.asyncio
    async def test_setup_no_provider(self) -> None:
        """Test plugin setup without provider configuration."""
        mock_app = Mock()
        plugin = LLMProviderPlugin(app=mock_app)

        with patch('plugginger.plugins.core.llm_provider.llm_provider_plugin.ProviderFactory') as mock_factory:
            mock_factory.create_from_env.side_effect = ValueError("No provider configured")

            await plugin.setup()

            assert plugin.provider is None

    @pytest.mark.asyncio
    async def test_teardown(self) -> None:
        """Test plugin teardown."""
        mock_app = Mock()
        plugin = LLMProviderPlugin(app=mock_app)

        # Should not raise any exceptions
        await plugin.teardown()

    @pytest.mark.asyncio
    async def test_generate_structured_success(self) -> None:
        """Test successful structured generation."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = LLMProviderPlugin(app=mock_app)

        # Mock provider
        mock_provider = Mock()
        mock_provider.provider_name = "openai"
        mock_provider.model = "gpt-4"
        mock_provider.generate_structured = AsyncMock(return_value={
            "content": '{"name": "test", "age": 25}',
            "model": "gpt-4",
            "provider": "openai",
            "tokens_used": 100,
            "success": True
        })
        plugin.provider = mock_provider

        # Mock validation service
        with patch.object(plugin.validation_service, 'validate_ebnf_response', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = {
                "valid": True,
                "data": {"name": "test", "age": 25},
                "errors": []
            }

            result = await plugin.generate_structured(
                system_message="You are a helpful assistant",
                user_message="Generate a person object",
                ebnf_grammar='person ::= \'{"name":\' string \',"age":\' number \'}\''
            )

            assert result["success"] is True
            assert result["content"] == '{"name": "test", "age": 25}'
            assert result["validation"]["valid"] is True
            mock_app.emit_event.assert_any_call("llm.request.started", {
                "provider": "openai",
                "model": "gpt-4",
                "type": "structured"
            })

    @pytest.mark.asyncio
    async def test_generate_structured_no_provider(self) -> None:
        """Test structured generation without provider."""
        mock_app = Mock()
        plugin = LLMProviderPlugin(app=mock_app)

        with pytest.raises(PluggingerConfigurationError) as exc_info:
            await plugin.generate_structured(
                system_message="Test",
                user_message="Test",
                ebnf_grammar="test"
            )

        assert "No LLM provider configured" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_generate_text_success(self) -> None:
        """Test successful text generation."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = LLMProviderPlugin(app=mock_app)

        # Mock provider
        mock_provider = Mock()
        mock_provider.provider_name = "anthropic"
        mock_provider.model = "claude-3"
        mock_provider.generate_text = AsyncMock(return_value={
            "content": "This is a test response from the LLM.",
            "model": "claude-3",
            "provider": "anthropic",
            "tokens_used": 50,
            "success": True
        })
        plugin.provider = mock_provider

        # Mock validation service
        with patch.object(plugin.validation_service, 'validate_text_response', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = {
                "valid": True,
                "data": "This is a test response from the LLM.",
                "errors": []
            }

            result = await plugin.generate_text(
                prompt="Explain quantum computing",
                max_tokens=500
            )

            assert result["success"] is True
            assert result["content"] == "This is a test response from the LLM."
            assert result["validation"]["valid"] is True
            mock_app.emit_event.assert_any_call("llm.request.completed", {
                "provider": "anthropic",
                "success": True,
                "tokens_used": 50
            })

    @pytest.mark.asyncio
    async def test_validate_response_success(self) -> None:
        """Test successful response validation."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = LLMProviderPlugin(app=mock_app)

        with patch.object(plugin.validation_service, 'validate_response_format', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = {
                "valid": True,
                "data": {"name": "John", "age": 30},
                "errors": []
            }

            with patch.object(plugin.validation_service, 'suggest_fixes', new_callable=AsyncMock) as mock_suggest:
                mock_suggest.return_value = []

                result = await plugin.validate_response(
                    response_content='{"name": "John", "age": 30}',
                    expected_format="json"
                )

                assert result["valid"] is True
                assert result["suggestions"] == []
                mock_app.emit_event.assert_called_once_with("llm.response.validated", {
                    "valid": True,
                    "format": "json",
                    "error_count": 0
                })

    @pytest.mark.asyncio
    async def test_get_provider_info_with_provider(self) -> None:
        """Test getting provider info when provider is configured."""
        mock_app = Mock()
        plugin = LLMProviderPlugin(app=mock_app)

        # Mock provider
        mock_provider = Mock()
        mock_provider.get_info.return_value = {
            "provider": "openai",
            "model": "gpt-4",
            "base_url": "https://api.openai.com/v1",
            "has_api_key": True
        }
        plugin.provider = mock_provider

        result = await plugin.get_provider_info()

        assert result["configured"] is True
        assert result["provider"] == "openai"
        assert result["model"] == "gpt-4"
        assert result["default_temperature"] == 0.1

    @pytest.mark.asyncio
    async def test_get_provider_info_no_provider(self) -> None:
        """Test getting provider info when no provider is configured."""
        mock_app = Mock()
        plugin = LLMProviderPlugin(app=mock_app)

        result = await plugin.get_provider_info()

        assert result["configured"] is False
        assert result["provider"] is None
        assert "No LLM provider configured" in result["error"]

    @pytest.mark.asyncio
    async def test_generate_structured_error_handling(self) -> None:
        """Test error handling in structured generation."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = LLMProviderPlugin(app=mock_app)

        # Mock provider that raises exception
        mock_provider = Mock()
        mock_provider.provider_name = "openai"
        mock_provider.generate_structured = AsyncMock(side_effect=Exception("API Error"))
        plugin.provider = mock_provider

        with pytest.raises(PluggingerValidationError) as exc_info:
            await plugin.generate_structured(
                system_message="Test",
                user_message="Test",
                ebnf_grammar="test"
            )

        assert "Structured generation failed" in str(exc_info.value)
        mock_app.emit_event.assert_any_call("llm.request.failed", {
            "provider": "openai",
            "error": "API Error"
        })

    @pytest.mark.asyncio
    async def test_generate_text_error_handling(self) -> None:
        """Test error handling in text generation."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = LLMProviderPlugin(app=mock_app)

        # Mock provider that raises exception
        mock_provider = Mock()
        mock_provider.provider_name = "anthropic"
        mock_provider.generate_text = AsyncMock(side_effect=Exception("API Error"))
        plugin.provider = mock_provider

        with pytest.raises(PluggingerValidationError) as exc_info:
            await plugin.generate_text(prompt="Test prompt")

        assert "Text generation failed" in str(exc_info.value)
        mock_app.emit_event.assert_any_call("llm.request.failed", {
            "provider": "anthropic",
            "error": "API Error"
        })
