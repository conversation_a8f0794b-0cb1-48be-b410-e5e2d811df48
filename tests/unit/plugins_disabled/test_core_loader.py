"""
Tests for core plugin loading infrastructure.
"""

from pathlib import Path
from unittest.mock import Mock, patch

import pytest

from plugginger.api.builder import PluggingerAppBuilder
from plugginger.core.exceptions import PluggingerConfigurationError
from plugginger.plugins.core_loader import (
    CorePluginLoader,
    get_available_core_plugins,
    get_core_loader,
    load_core_plugins,
)


class TestCorePluginLoader:
    """Tests for CorePluginLoader class."""

    def test_initialization(self) -> None:
        """Test loader initialization."""
        with patch.object(CorePluginLoader, '_discover_core_plugins'):
            loader = CorePluginLoader()
            assert loader.core_plugins_path.name == "core"
            assert isinstance(loader.available_plugins, dict)

    @patch('pathlib.Path.exists')
    @patch('pathlib.Path.iterdir')
    def test_discover_core_plugins_success(self, mock_iterdir: Mock, mock_exists: Mock) -> None:
        """Test successful plugin discovery."""
        # Setup mock directory structure
        mock_plugin_dir = Mock()
        mock_plugin_dir.is_dir.return_value = True
        mock_plugin_dir.name = "test-plugin"

        mock_manifest = Mock()
        mock_manifest.exists.return_value = True
        mock_plugin_dir.__truediv__ = Mock(return_value=mock_manifest)

        mock_iterdir.return_value = [mock_plugin_dir]
        mock_exists.return_value = True

        loader = CorePluginLoader()

        assert "test-plugin" in loader.available_plugins
        assert loader.available_plugins["test-plugin"] == mock_plugin_dir

    @patch('pathlib.Path.exists')
    def test_discover_core_plugins_no_directory(self, mock_exists: Mock) -> None:
        """Test plugin discovery when directory doesn't exist."""
        mock_exists.return_value = False

        loader = CorePluginLoader()

        assert len(loader.available_plugins) == 0

    @patch('pathlib.Path.exists')
    @patch('pathlib.Path.iterdir')
    def test_discover_core_plugins_no_manifest(self, mock_iterdir: Mock, mock_exists: Mock) -> None:
        """Test plugin discovery skips directories without manifest."""
        # Setup mock directory without manifest
        mock_plugin_dir = Mock()
        mock_plugin_dir.is_dir.return_value = True
        mock_plugin_dir.name = "invalid-plugin"

        mock_manifest = Mock()
        mock_manifest.exists.return_value = False
        mock_plugin_dir.__truediv__ = Mock(return_value=mock_manifest)

        mock_iterdir.return_value = [mock_plugin_dir]
        mock_exists.return_value = True

        loader = CorePluginLoader()

        assert "invalid-plugin" not in loader.available_plugins

    def test_get_available_plugins(self) -> None:
        """Test getting available plugin names."""
        with patch.object(CorePluginLoader, '_discover_core_plugins'):
            loader = CorePluginLoader()
            loader.available_plugins = {"plugin1": Path("/path1"), "plugin2": Path("/path2")}

            available = loader.get_available_plugins()

            assert set(available) == {"plugin1", "plugin2"}

    def test_load_core_plugin_success(self) -> None:
        """Test successful core plugin loading."""
        with patch.object(CorePluginLoader, '_discover_core_plugins'):
            loader = CorePluginLoader()
            loader.available_plugins = {"test-plugin": Path("/test/path")}

            mock_builder = Mock(spec=PluggingerAppBuilder)
            mock_result = Mock()
            mock_result.successful_discoveries = 1
            mock_builder.discover_and_include_plugins.return_value = mock_result

            loader.load_core_plugin("test-plugin", mock_builder)

            mock_builder.discover_and_include_plugins.assert_called_once_with(
                directory=Path("/test/path"),
                recursive=False,
                pattern="manifest.yaml"
            )

    def test_load_core_plugin_not_found(self) -> None:
        """Test loading non-existent core plugin."""
        with patch.object(CorePluginLoader, '_discover_core_plugins'):
            loader = CorePluginLoader()
            loader.available_plugins = {"other-plugin": Path("/other/path")}

            mock_builder = Mock(spec=PluggingerAppBuilder)

            with pytest.raises(PluggingerConfigurationError) as exc_info:
                loader.load_core_plugin("missing-plugin", mock_builder)

            assert "Core plugin 'missing-plugin' not found" in str(exc_info.value)
            assert "Available core plugins: other-plugin" in str(exc_info.value)

    def test_load_core_plugin_loading_error(self) -> None:
        """Test core plugin loading error handling."""
        with patch.object(CorePluginLoader, '_discover_core_plugins'):
            loader = CorePluginLoader()
            loader.available_plugins = {"test-plugin": Path("/test/path")}

            mock_builder = Mock(spec=PluggingerAppBuilder)
            mock_builder.discover_and_include_plugins.side_effect = Exception("Loading failed")

            with pytest.raises(PluggingerConfigurationError) as exc_info:
                loader.load_core_plugin("test-plugin", mock_builder)

            assert "Failed to load core plugin 'test-plugin'" in str(exc_info.value)

    def test_load_core_plugins_multiple(self) -> None:
        """Test loading multiple core plugins."""
        with patch.object(CorePluginLoader, '_discover_core_plugins'):
            loader = CorePluginLoader()
            loader.available_plugins = {
                "plugin1": Path("/path1"),
                "plugin2": Path("/path2")
            }

            mock_builder = Mock(spec=PluggingerAppBuilder)
            mock_result = Mock()
            mock_result.successful_discoveries = 1
            mock_builder.discover_and_include_plugins.return_value = mock_result

            loader.load_core_plugins(["plugin1", "plugin2"], mock_builder)

            assert mock_builder.discover_and_include_plugins.call_count == 2

    def test_load_all_core_plugins(self) -> None:
        """Test loading all available core plugins."""
        with patch.object(CorePluginLoader, '_discover_core_plugins'):
            loader = CorePluginLoader()
            loader.available_plugins = {
                "plugin1": Path("/path1"),
                "plugin2": Path("/path2")
            }

            mock_builder = Mock(spec=PluggingerAppBuilder)
            mock_result = Mock()
            mock_result.successful_discoveries = 1
            mock_builder.discover_and_include_plugins.return_value = mock_result

            loader.load_all_core_plugins(mock_builder)

            assert mock_builder.discover_and_include_plugins.call_count == 2

    def test_load_all_core_plugins_empty(self) -> None:
        """Test loading all core plugins when none available."""
        with patch.object(CorePluginLoader, '_discover_core_plugins'):
            loader = CorePluginLoader()
            loader.available_plugins = {}

            mock_builder = Mock(spec=PluggingerAppBuilder)

            loader.load_all_core_plugins(mock_builder)

            mock_builder.discover_and_include_plugins.assert_not_called()


class TestCoreLoaderFunctions:
    """Tests for module-level functions."""

    @patch('plugginger.plugins.core_loader.CorePluginLoader')
    def test_get_core_loader_singleton(self, mock_loader_class: Mock) -> None:
        """Test core loader singleton behavior."""
        mock_instance = Mock()
        mock_loader_class.return_value = mock_instance

        # Clear global instance
        import plugginger.plugins.core_loader
        plugginger.plugins.core_loader._core_loader = None

        # First call creates instance
        loader1 = get_core_loader()
        assert loader1 == mock_instance
        mock_loader_class.assert_called_once()

        # Second call returns same instance
        loader2 = get_core_loader()
        assert loader2 == mock_instance
        assert loader1 is loader2
        mock_loader_class.assert_called_once()  # Still only called once

    @patch('plugginger.plugins.core_loader.get_core_loader')
    def test_load_core_plugins_function(self, mock_get_loader: Mock) -> None:
        """Test load_core_plugins convenience function."""
        mock_loader = Mock()
        mock_get_loader.return_value = mock_loader
        mock_builder = Mock()

        load_core_plugins(["plugin1", "plugin2"], mock_builder)

        mock_loader.load_core_plugins.assert_called_once_with(["plugin1", "plugin2"], mock_builder)

    @patch('plugginger.plugins.core_loader.get_core_loader')
    def test_get_available_core_plugins_function(self, mock_get_loader: Mock) -> None:
        """Test get_available_core_plugins convenience function."""
        mock_loader = Mock()
        mock_loader.get_available_plugins.return_value = ["plugin1", "plugin2"]
        mock_get_loader.return_value = mock_loader

        result = get_available_core_plugins()

        assert result == ["plugin1", "plugin2"]
        mock_loader.get_available_plugins.assert_called_once()


class TestBuilderIntegrationFunctions:
    """Tests for builder integration functions."""

    @patch('plugginger.plugins.core_loader.load_core_plugins')
    def test_include_core_plugins_in_builder(self, mock_load: Mock) -> None:
        """Test include_core_plugins_in_builder function."""
        from plugginger.plugins.core_loader import include_core_plugins_in_builder

        mock_builder = Mock(spec=PluggingerAppBuilder)

        result = include_core_plugins_in_builder(mock_builder, ["plugin1", "plugin2"])

        mock_load.assert_called_once_with(["plugin1", "plugin2"], mock_builder)
        assert result is mock_builder  # Should return builder for chaining

    @patch('plugginger.plugins.core_loader.get_core_loader')
    def test_include_all_core_plugins_in_builder(self, mock_get_loader: Mock) -> None:
        """Test include_all_core_plugins_in_builder function."""
        from plugginger.plugins.core_loader import include_all_core_plugins_in_builder

        mock_loader = Mock()
        mock_get_loader.return_value = mock_loader
        mock_builder = Mock(spec=PluggingerAppBuilder)

        result = include_all_core_plugins_in_builder(mock_builder)

        mock_loader.load_all_core_plugins.assert_called_once_with(mock_builder)
        assert result is mock_builder  # Should return builder for chaining
