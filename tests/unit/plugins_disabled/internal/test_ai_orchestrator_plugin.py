"""
Tests for AI Orchestrator Internal Plugin.
"""

from unittest.mock import As<PERSON><PERSON><PERSON>, Mock, patch

import pytest

from plugginger.core.exceptions import PluggingerValidationError
from plugginger.plugins.internal.ai_orchestrator.ai_orchestrator_plugin import AIOrchestratorPlugin


class TestAIOrchestratorPlugin:
    """Tests for AIOrchestratorPlugin class."""

    def test_initialization(self) -> None:
        """Test plugin initialization."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        assert plugin.orchestration_service is not None
        assert plugin.validation_service is not None
        assert plugin.max_retry_attempts == 3
        assert plugin.default_confidence_threshold == 0.7
        assert plugin.enable_parallel_processing is True
        assert plugin.workflow_timeout_seconds == 180.0
        assert plugin.enable_caching is True
        assert plugin.cache_ttl_seconds == 3600

    @pytest.mark.asyncio
    async def test_setup_with_config(self) -> None:
        """Test plugin setup with configuration."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        # Mock config
        mock_config = Mock()
        mock_config.max_retry_attempts = 5
        mock_config.default_confidence_threshold = 0.8
        mock_config.enable_parallel_processing = False
        mock_config.workflow_timeout_seconds = 300.0
        mock_config.enable_caching = False
        mock_config.cache_ttl_seconds = 7200

        await plugin.setup(mock_config)

        assert plugin.max_retry_attempts == 5
        assert plugin.default_confidence_threshold == 0.8
        assert plugin.enable_parallel_processing is False
        assert plugin.workflow_timeout_seconds == 300.0
        assert plugin.enable_caching is False
        assert plugin.cache_ttl_seconds == 7200

    @pytest.mark.asyncio
    async def test_setup_without_config(self) -> None:
        """Test plugin setup without configuration."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        await plugin.setup()

        # Should keep default values
        assert plugin.max_retry_attempts == 3
        assert plugin.default_confidence_threshold == 0.7

    @pytest.mark.asyncio
    async def test_teardown(self) -> None:
        """Test plugin teardown."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        # Should not raise any exceptions
        await plugin.teardown()

    @pytest.mark.asyncio
    async def test_orchestrate_plugin_generation_success(self) -> None:
        """Test successful plugin generation orchestration."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        plugin_request = {"name": "test_plugin", "description": "Test plugin"}
        app_context = {"app_path": "test.app:create_app"}
        mock_result = {
            "success": True,
            "workflow_id": "plugin_gen_test_plugin",
            "plugin_name": "test_plugin",
            "specification": {"name": "test_plugin", "version": "1.0.0"}
        }

        with patch.object(plugin.orchestration_service, 'orchestrate_plugin_generation', new_callable=AsyncMock) as mock_orchestrate:
            mock_orchestrate.return_value = mock_result

            result = await plugin.orchestrate_plugin_generation(
                plugin_request=plugin_request,
                app_context=app_context,
                max_retry_attempts=5,
                confidence_threshold=0.8
            )

            assert result == mock_result
            mock_orchestrate.assert_called_once_with(
                plugin_request=plugin_request,
                app_context=app_context,
                max_retry_attempts=5,
                confidence_threshold=0.8
            )
            mock_app.emit_event.assert_called_once_with("ai.orchestration.started", {
                "type": "plugin_generation",
                "plugin_name": "test_plugin"
            })

    @pytest.mark.asyncio
    async def test_orchestrate_plugin_generation_default_params(self) -> None:
        """Test plugin generation orchestration with default parameters."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        plugin_request = {"name": "test_plugin"}
        mock_result = {"success": True}

        with patch.object(plugin.orchestration_service, 'orchestrate_plugin_generation', new_callable=AsyncMock) as mock_orchestrate:
            mock_orchestrate.return_value = mock_result

            await plugin.orchestrate_plugin_generation(
                plugin_request=plugin_request
            )

            # Should use default values
            mock_orchestrate.assert_called_once_with(
                plugin_request=plugin_request,
                app_context=None,
                max_retry_attempts=3,  # Default value
                confidence_threshold=0.7  # Default value
            )

    @pytest.mark.asyncio
    async def test_orchestrate_plugin_generation_error(self) -> None:
        """Test plugin generation orchestration error handling."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        with patch.object(plugin.orchestration_service, 'orchestrate_plugin_generation', new_callable=AsyncMock) as mock_orchestrate:
            mock_orchestrate.side_effect = Exception("Orchestration failed")

            with pytest.raises(PluggingerValidationError) as exc_info:
                await plugin.orchestrate_plugin_generation(
                    plugin_request={"name": "test_plugin"}
                )

            assert "Plugin generation orchestration failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_analyze_and_suggest_success(self) -> None:
        """Test successful app analysis and suggestions."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        app_path = "test.app:create_app"
        mock_result = {
            "success": True,
            "workflow_id": "analyze_suggest_test_app_create_app",
            "suggestions": [{"type": "improvement", "title": "Add caching"}]
        }

        with patch.object(plugin.orchestration_service, 'analyze_and_suggest', new_callable=AsyncMock) as mock_analyze:
            mock_analyze.return_value = mock_result

            result = await plugin.analyze_and_suggest(
                app_path=app_path,
                analysis_depth="comprehensive",
                confidence_threshold=0.8
            )

            assert result == mock_result
            mock_analyze.assert_called_once_with(
                app_path=app_path,
                analysis_depth="comprehensive",
                confidence_threshold=0.8
            )

    @pytest.mark.asyncio
    async def test_validate_ai_output_success(self) -> None:
        """Test successful AI output validation."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        content = '{"name": "test", "version": "1.0.0"}'
        content_type = "json"
        validation_rules = {"schema": {"type": "object"}}
        mock_result = {
            "valid": True,
            "validation_id": "ai_validation_json_1234",
            "confidence_score": 0.9
        }

        with patch.object(plugin.validation_service, 'validate_ai_output', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_result

            result = await plugin.validate_ai_output(
                content=content,
                content_type=content_type,
                validation_rules=validation_rules,
                confidence_threshold=0.8
            )

            assert result == mock_result
            mock_validate.assert_called_once_with(
                content=content,
                content_type=content_type,
                validation_rules=validation_rules,
                confidence_threshold=0.8
            )

    @pytest.mark.asyncio
    async def test_coordinate_services_success(self) -> None:
        """Test successful service coordination."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        service_calls = [
            {"service": "llm_provider.generate_text", "parameters": {"prompt": "Hello"}},
            {"service": "json_validator.validate_json", "parameters": {"data": "{}"}}
        ]
        mock_result = {
            "success": True,
            "workflow_id": "coordinate_2_parallel",
            "service_results": [
                {"success": True, "service": "llm_provider.generate_text"},
                {"success": True, "service": "json_validator.validate_json"}
            ]
        }

        with patch.object(plugin.orchestration_service, 'coordinate_services', new_callable=AsyncMock) as mock_coordinate:
            mock_coordinate.return_value = mock_result

            result = await plugin.coordinate_services(
                service_calls=service_calls,
                execution_mode="parallel",
                timeout_seconds=120.0
            )

            assert result == mock_result
            mock_coordinate.assert_called_once_with(
                service_calls=service_calls,
                execution_mode="parallel",
                timeout_seconds=120.0
            )

    @pytest.mark.asyncio
    async def test_coordinate_services_default_params(self) -> None:
        """Test service coordination with default parameters."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        service_calls = [{"service": "test.service", "parameters": {}}]
        mock_result = {"success": True}

        with patch.object(plugin.orchestration_service, 'coordinate_services', new_callable=AsyncMock) as mock_coordinate:
            mock_coordinate.return_value = mock_result

            await plugin.coordinate_services(service_calls=service_calls)

            # Should use default values
            mock_coordinate.assert_called_once_with(
                service_calls=service_calls,
                execution_mode="parallel",  # Default value
                timeout_seconds=180.0  # Default value
            )

    @pytest.mark.asyncio
    async def test_validate_plugin_specification_success(self) -> None:
        """Test successful plugin specification validation."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        plugin_spec = {
            "name": "test_plugin",
            "version": "1.0.0",
            "description": "Test plugin",
            "services": [{"name": "test_service"}]
        }
        mock_result = {
            "valid": True,
            "plugin_name": "test_plugin",
            "errors": [],
            "warnings": []
        }

        with patch.object(plugin.validation_service, 'validate_plugin_specification', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_result

            result = await plugin.validate_plugin_specification(
                plugin_spec=plugin_spec,
                strict_mode=True
            )

            assert result == mock_result
            mock_validate.assert_called_once_with(
                plugin_spec=plugin_spec,
                strict_mode=True
            )

    @pytest.mark.asyncio
    async def test_validate_plugin_specification_error(self) -> None:
        """Test plugin specification validation error handling."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        plugin_spec = {"name": "test_plugin"}

        with patch.object(plugin.validation_service, 'validate_plugin_specification', new_callable=AsyncMock) as mock_validate:
            mock_validate.side_effect = Exception("Validation failed")

            result = await plugin.validate_plugin_specification(
                plugin_spec=plugin_spec
            )

            assert result["valid"] is False
            assert result["plugin_name"] == "test_plugin"
            assert "Validation failed" in result["error"]

    @pytest.mark.asyncio
    async def test_validate_generated_code_success(self) -> None:
        """Test successful generated code validation."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        code = "def hello():\n    return 'Hello, World!'"
        mock_result = {
            "valid": True,
            "language": "python",
            "errors": [],
            "warnings": []
        }

        with patch.object(plugin.validation_service, 'validate_generated_code', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_result

            result = await plugin.validate_generated_code(
                code=code,
                language="python",
                check_syntax=True,
                check_style=True
            )

            assert result == mock_result
            mock_validate.assert_called_once_with(
                code=code,
                language="python",
                check_syntax=True,
                check_style=True
            )

    @pytest.mark.asyncio
    async def test_validate_generated_code_error(self) -> None:
        """Test generated code validation error handling."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        code = "invalid python code"

        with patch.object(plugin.validation_service, 'validate_generated_code', new_callable=AsyncMock) as mock_validate:
            mock_validate.side_effect = Exception("Code validation failed")

            result = await plugin.validate_generated_code(code=code)

            assert result["valid"] is False
            assert result["language"] == "python"
            assert "Code validation failed" in result["error"]

    @pytest.mark.asyncio
    async def test_get_orchestration_status(self) -> None:
        """Test getting orchestration status."""
        mock_app = Mock()
        plugin = AIOrchestratorPlugin(app=mock_app)

        result = await plugin.get_orchestration_status()

        assert result["plugin_name"] == "ai_orchestrator"
        assert result["status"] == "active"
        assert "configuration" in result
        assert "capabilities" in result
        assert "dependencies" in result
        assert result["configuration"]["max_retry_attempts"] == 3
        assert "plugin_generation_orchestration" in result["capabilities"]
        assert "llm_provider" in result["dependencies"]
