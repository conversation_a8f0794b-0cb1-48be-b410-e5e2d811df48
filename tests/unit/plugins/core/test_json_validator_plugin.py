"""
Tests for JSON Validator Core Plugin.
"""

from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from plugginger.core.exceptions import PluggingerValidationError
from plugginger.plugins.core.json_validator.json_validator_plugin import JsonValidatorPlugin


class TestJsonValidatorPlugin:
    """Tests for JsonValidatorPlugin class."""

    def test_initialization(self) -> None:
        """Test plugin initialization."""
        mock_app = Mock()
        plugin = JsonValidatorPlugin(app=mock_app)

        assert plugin.validation_service is not None
        assert plugin.schema_service is not None
        assert plugin.ebnf_service is not None
        assert plugin.default_retry_count == 3
        assert plugin.default_timeout_seconds == 30
        assert plugin.strict_validation is True
        assert plugin.allow_additional_properties is False

    @pytest.mark.asyncio
    async def test_setup(self) -> None:
        """Test plugin setup."""
        # Mock app with config
        mock_app = Mock()
        mock_config = Mock()
        mock_config.json_validator = {
            'default_retry_count': 5,
            'strict_validation': False
        }
        mock_app.config = mock_config

        plugin = JsonValidatorPlugin(app=mock_app)
        await plugin.setup()

        assert plugin.default_retry_count == 5
        assert plugin.strict_validation is False

    @pytest.mark.asyncio
    async def test_setup_no_config(self) -> None:
        """Test plugin setup without config."""
        # Mock app without config
        mock_app = Mock()
        del mock_app.config  # Remove config attribute

        plugin = JsonValidatorPlugin(app=mock_app)
        await plugin.setup()

        # Should keep default values
        assert plugin.default_retry_count == 3
        assert plugin.strict_validation is True

    @pytest.mark.asyncio
    async def test_teardown(self) -> None:
        """Test plugin teardown."""
        mock_app = Mock()
        plugin = JsonValidatorPlugin(app=mock_app)

        # Should not raise any exceptions
        await plugin.teardown()

    @pytest.mark.asyncio
    async def test_validate_json_success(self) -> None:
        """Test successful JSON validation."""
        mock_app = Mock()
        plugin = JsonValidatorPlugin(app=mock_app)

        # Mock validation service
        mock_result = {
            "valid": True,
            "data": {"name": "John", "age": 30}
        }

        with patch.object(plugin.validation_service, 'validate_with_retry', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_result

            result = await plugin.validate_json(
                data='{"name": "John", "age": 30}',
                schema={
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "age": {"type": "integer"}
                    }
                }
            )

            assert result == mock_result
            mock_validate.assert_called_once()

    @pytest.mark.asyncio
    async def test_validate_json_with_custom_params(self) -> None:
        """Test JSON validation with custom parameters."""
        mock_app = Mock()
        plugin = JsonValidatorPlugin(app=mock_app)

        mock_result = {"valid": True}

        with patch.object(plugin.validation_service, 'validate_with_retry', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_result

            await plugin.validate_json(
                data='{"test": true}',
                schema={"type": "object"},
                retry_count=5,
                strict=False
            )

            # Check that custom parameters were passed
            call_args = mock_validate.call_args
            assert call_args[1]["retry_count"] == 5
            assert call_args[1]["strict"] is False

    @pytest.mark.asyncio
    async def test_validate_json_error(self) -> None:
        """Test JSON validation error handling."""
        mock_app = Mock()
        plugin = JsonValidatorPlugin(app=mock_app)

        with patch.object(plugin.validation_service, 'validate_with_retry', new_callable=AsyncMock) as mock_validate:
            mock_validate.side_effect = Exception("Validation failed")

            with pytest.raises(PluggingerValidationError) as exc_info:
                await plugin.validate_json(
                    data='{"invalid": "json"}',
                    schema={"type": "object"}
                )

            assert "JSON validation failed" in str(exc_info.value)
