"""
Tests for Wiring Analyzer Core Plugin.
"""

import pytest
from typing import Any, Dict
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

from plugginger.plugins.core.wiring_analyzer.wiring_analyzer_plugin import WiringAnalyzerPlugin
from plugginger.core.exceptions import PluggingerValidationError


class TestWiringAnalyzerPlugin:
    """Tests for WiringAnalyzerPlugin class."""

    def test_initialization(self) -> None:
        """Test plugin initialization."""
        mock_app = Mock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        assert plugin.analysis_service is not None
        assert plugin.validation_service is not None
        assert plugin.suggestion_service is not None
        assert plugin.analysis_depth == "detailed"
        assert plugin.suggestion_confidence_threshold == 0.6
        assert plugin.max_suggestions == 10
        assert plugin.enable_dependency_validation is True
        assert plugin.enable_event_analysis is True

    @pytest.mark.asyncio
    async def test_setup_with_config(self) -> None:
        """Test plugin setup with configuration."""
        mock_app = Mock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        # Mock config
        mock_config = Mock()
        mock_config.analysis_depth = "comprehensive"
        mock_config.suggestion_confidence_threshold = 0.8
        mock_config.max_suggestions = 15
        mock_config.enable_dependency_validation = False
        mock_config.enable_event_analysis = False

        await plugin.setup(mock_config)

        assert plugin.analysis_depth == "comprehensive"
        assert plugin.suggestion_confidence_threshold == 0.8
        assert plugin.max_suggestions == 15
        assert plugin.enable_dependency_validation is False
        assert plugin.enable_event_analysis is False

    @pytest.mark.asyncio
    async def test_setup_without_config(self) -> None:
        """Test plugin setup without configuration."""
        mock_app = Mock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        await plugin.setup()

        # Should keep default values
        assert plugin.analysis_depth == "detailed"
        assert plugin.suggestion_confidence_threshold == 0.6

    @pytest.mark.asyncio
    async def test_teardown(self) -> None:
        """Test plugin teardown."""
        mock_app = Mock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        # Should not raise any exceptions
        await plugin.teardown()

    @pytest.mark.asyncio
    async def test_analyze_app_context_success(self) -> None:
        """Test successful app context analysis."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        mock_result = {
            "success": True,
            "app_name": "test_app",
            "plugins": [{"name": "test_plugin"}],
            "services": [{"name": "test_service"}],
            "events": ["test.event"],
            "dependency_graph": {}
        }

        with patch.object(plugin.analysis_service, 'analyze_app_structure', new_callable=AsyncMock) as mock_analyze:
            mock_analyze.return_value = mock_result

            result = await plugin.analyze_app_context(
                app_path="test.app:create_app",
                analysis_depth="detailed"
            )

            assert result == mock_result
            mock_analyze.assert_called_once_with(
                app_path="test.app:create_app",
                analysis_depth="detailed"
            )
            mock_app.emit_event.assert_any_call("wiring.analysis.started", {
                "app_path": "test.app:create_app",
                "analysis_depth": "detailed"
            })

    @pytest.mark.asyncio
    async def test_analyze_app_context_error(self) -> None:
        """Test app context analysis error handling."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        with patch.object(plugin.analysis_service, 'analyze_app_structure', new_callable=AsyncMock) as mock_analyze:
            mock_analyze.side_effect = Exception("Analysis failed")

            with pytest.raises(PluggingerValidationError) as exc_info:
                await plugin.analyze_app_context(app_path="invalid.path")

            assert "App analysis failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_plugin_compatibility_success(self) -> None:
        """Test successful plugin compatibility validation."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        plugin_spec = {"name": "test_plugin", "dependencies": []}
        app_context = {"plugins": [], "services": []}
        mock_result = {
            "valid": True,
            "plugin_name": "test_plugin",
            "errors": [],
            "warnings": [],
            "suggestions": []
        }

        with patch.object(plugin.validation_service, 'validate_plugin_compatibility', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_result

            result = await plugin.validate_plugin_compatibility(
                plugin_spec=plugin_spec,
                app_context=app_context
            )

            assert result == mock_result
            mock_validate.assert_called_once_with(
                plugin_spec=plugin_spec,
                app_context=app_context,
                enable_dependency_validation=True,
                enable_event_analysis=True
            )
            mock_app.emit_event.assert_called_once_with("wiring.validation.performed", {
                "plugin_name": "test_plugin",
                "valid": True,
                "error_count": 0,
                "warning_count": 0
            })

    @pytest.mark.asyncio
    async def test_validate_plugin_compatibility_with_errors(self) -> None:
        """Test plugin compatibility validation with errors."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        plugin_spec = {"name": "test_plugin", "dependencies": []}
        app_context = {"plugins": [], "services": []}
        mock_result = {
            "valid": False,
            "plugin_name": "test_plugin",
            "errors": ["Dependency not found"],
            "warnings": ["Event pattern may not match"],
            "suggestions": []
        }

        with patch.object(plugin.validation_service, 'validate_plugin_compatibility', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_result

            result = await plugin.validate_plugin_compatibility(
                plugin_spec=plugin_spec,
                app_context=app_context
            )

            assert result["valid"] is False
            assert len(result["errors"]) == 1
            assert len(result["warnings"]) == 1

    @pytest.mark.asyncio
    async def test_suggest_wiring_improvements_success(self) -> None:
        """Test successful wiring improvements suggestions."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        plugin_spec = {"name": "test_plugin", "description": "Test plugin"}
        app_context = {"services": [], "events": []}
        mock_result = {
            "plugin_name": "test_plugin",
            "suggestions": [
                {
                    "type": "service_integration",
                    "title": "Use auth service",
                    "confidence": 0.8
                }
            ],
            "total_suggestions": 1
        }

        with patch.object(plugin.suggestion_service, 'suggest_wiring_improvements', new_callable=AsyncMock) as mock_suggest:
            mock_suggest.return_value = mock_result

            result = await plugin.suggest_wiring_improvements(
                plugin_spec=plugin_spec,
                app_context=app_context,
                confidence_threshold=0.7,
                max_suggestions=5
            )

            assert result == mock_result
            mock_suggest.assert_called_once_with(
                plugin_spec=plugin_spec,
                app_context=app_context,
                confidence_threshold=0.7,
                max_suggestions=5
            )
            mock_app.emit_event.assert_called_once_with("wiring.suggestions.generated", {
                "plugin_name": "test_plugin",
                "suggestion_count": 1,
                "confidence_threshold": 0.7
            })

    @pytest.mark.asyncio
    async def test_suggest_wiring_improvements_default_params(self) -> None:
        """Test wiring improvements suggestions with default parameters."""
        mock_app = Mock()
        mock_app.emit_event = AsyncMock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        plugin_spec = {"name": "test_plugin"}
        app_context = {"services": []}
        mock_result = {"suggestions": []}

        with patch.object(plugin.suggestion_service, 'suggest_wiring_improvements', new_callable=AsyncMock) as mock_suggest:
            mock_suggest.return_value = mock_result

            await plugin.suggest_wiring_improvements(
                plugin_spec=plugin_spec,
                app_context=app_context
            )

            # Should use default values
            mock_suggest.assert_called_once_with(
                plugin_spec=plugin_spec,
                app_context=app_context,
                confidence_threshold=0.6,  # Default value
                max_suggestions=10  # Default value
            )

    @pytest.mark.asyncio
    async def test_generate_integration_code_success(self) -> None:
        """Test successful integration code generation."""
        mock_app = Mock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        plugin_spec = {"name": "test_plugin"}
        selected_suggestions = [
            {
                "type": "service_integration",
                "code_example": "result = await self.auth.authenticate(token)"
            }
        ]
        mock_result = {
            "plugin_name": "test_plugin",
            "code_sections": {
                "services": ["result = await self.auth.authenticate(token)"]
            },
            "integration_instructions": ["Step 1", "Step 2"]
        }

        with patch.object(plugin.suggestion_service, 'generate_integration_code', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = mock_result

            result = await plugin.generate_integration_code(
                plugin_spec=plugin_spec,
                selected_suggestions=selected_suggestions
            )

            assert result == mock_result
            mock_generate.assert_called_once_with(
                plugin_spec=plugin_spec,
                selected_suggestions=selected_suggestions
            )

    @pytest.mark.asyncio
    async def test_generate_integration_code_error(self) -> None:
        """Test integration code generation error handling."""
        mock_app = Mock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        with patch.object(plugin.suggestion_service, 'generate_integration_code', new_callable=AsyncMock) as mock_generate:
            mock_generate.side_effect = Exception("Code generation failed")

            with pytest.raises(PluggingerValidationError) as exc_info:
                await plugin.generate_integration_code(
                    plugin_spec={"name": "test_plugin"},
                    selected_suggestions=[]
                )

            assert "Integration code generation failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_analyze_service_dependencies(self) -> None:
        """Test service dependencies analysis."""
        mock_app = Mock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        plugin_spec = {
            "name": "test_plugin",
            "services": [
                {"name": "test_service", "signature": "test() -> str"}
            ]
        }
        app_context = {
            "services": [
                {"name": "existing_service", "plugin": "existing_plugin"}
            ]
        }

        mock_service_analysis = {
            "service_name": "test_service",
            "has_conflicts": False,
            "conflicts": [],
            "compatible_services": [],
            "recommendation": "Service appears to be unique and safe to add"
        }

        with patch.object(plugin.validation_service, 'validate_service_signature_compatibility', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_service_analysis

            result = await plugin.analyze_service_dependencies(
                plugin_spec=plugin_spec,
                app_context=app_context
            )

            assert result["plugin_name"] == "test_plugin"
            assert result["total_services"] == 1
            assert result["conflict_count"] == 0
            assert len(result["service_analyses"]) == 1
            assert "compatibility_summary" in result

    def test_generate_compatibility_summary(self) -> None:
        """Test compatibility summary generation."""
        mock_app = Mock()
        plugin = WiringAnalyzerPlugin(app=mock_app)

        analyses = [
            {
                "has_conflicts": False,
                "compatible_services": [],
                "recommendation": "Safe to add"
            },
            {
                "has_conflicts": True,
                "compatible_services": [{"service": "existing"}],
                "recommendation": "Consider renaming"
            }
        ]

        summary = plugin._generate_compatibility_summary(analyses)

        assert summary["total_services"] == 2
        assert summary["conflicted_services"] == 1
        assert summary["compatible_services"] == 1
        assert summary["compatibility_score"] == 0.5  # (2-1)/2
        assert len(summary["recommendations"]) == 2
