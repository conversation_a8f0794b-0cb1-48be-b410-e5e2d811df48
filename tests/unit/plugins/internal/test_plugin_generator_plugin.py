"""
Tests for Plugin Generator Internal Plugin.
"""

from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from plugginger.core.exceptions import PluggingerValidationError
from plugginger.plugins.internal.plugin_generator.plugin_generator_plugin import (
    PluginGeneratorPlugin,
)


class TestPluginGeneratorPlugin:
    """Tests for PluginGeneratorPlugin class."""

    def test_initialization(self) -> None:
        """Test plugin initialization."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        assert plugin.generation_service is not None
        assert plugin.default_template == "basic"
        assert plugin.output_directory == "./plugins"
        assert plugin.enable_ai_generation is True
        assert plugin.enable_validation is True
        assert plugin.include_tests is True
        assert plugin.include_documentation is True
        assert plugin.code_style == "pep8"
        assert plugin.max_generation_time == 300.0

    @pytest.mark.asyncio
    async def test_setup_with_config(self) -> None:
        """Test plugin setup with configuration."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        # Mock config
        mock_config = Mock()
        mock_config.default_template = "full"
        mock_config.output_directory = "./custom_plugins"
        mock_config.enable_ai_generation = False
        mock_config.enable_validation = False
        mock_config.include_tests = False
        mock_config.include_documentation = False
        mock_config.code_style = "black"
        mock_config.max_generation_time = 600.0

        await plugin.setup(mock_config)

        assert plugin.default_template == "full"
        assert plugin.output_directory == "./custom_plugins"
        assert plugin.enable_ai_generation is False
        assert plugin.enable_validation is False
        assert plugin.include_tests is False
        assert plugin.include_documentation is False
        assert plugin.code_style == "black"
        assert plugin.max_generation_time == 600.0

    @pytest.mark.asyncio
    async def test_setup_without_config(self) -> None:
        """Test plugin setup without configuration."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        await plugin.setup()

        # Should keep default values
        assert plugin.default_template == "basic"
        assert plugin.enable_ai_generation is True

    @pytest.mark.asyncio
    async def test_teardown(self) -> None:
        """Test plugin teardown."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        # Should not raise any exceptions
        await plugin.teardown()

    @pytest.mark.asyncio
    async def test_generate_plugin_success(self) -> None:
        """Test successful plugin generation."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        plugin_spec = {
            "name": "test_plugin",
            "version": "1.0.0",
            "description": "Test plugin",
            "services": [{"name": "test_service"}]
        }
        mock_result = {
            "success": True,
            "generation_id": "plugin_gen_test_plugin",
            "plugin_name": "test_plugin",
            "plugin_path": "./plugins/test_plugin"
        }

        with patch.object(plugin.generation_service, 'generate_plugin', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = mock_result

            result = await plugin.generate_plugin(
                plugin_spec=plugin_spec,
                output_directory="./custom_plugins",
                template="service",
                enable_ai_generation=False,
                include_tests=False,
                include_documentation=False
            )

            assert result == mock_result
            mock_generate.assert_called_once_with(
                plugin_spec=plugin_spec,
                output_directory="./custom_plugins",
                template="service",
                enable_ai_generation=False,
                include_tests=False,
                include_documentation=False
            )

    @pytest.mark.asyncio
    async def test_generate_plugin_default_params(self) -> None:
        """Test plugin generation with default parameters."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        plugin_spec = {"name": "test_plugin"}
        mock_result = {"success": True}

        with patch.object(plugin.generation_service, 'generate_plugin', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = mock_result

            await plugin.generate_plugin(plugin_spec=plugin_spec)

            # Should use default values
            mock_generate.assert_called_once_with(
                plugin_spec=plugin_spec,
                output_directory="./plugins",  # Default value
                template="basic",  # Default value
                enable_ai_generation=True,  # Default value
                include_tests=True,  # Default value
                include_documentation=True  # Default value
            )

    @pytest.mark.asyncio
    async def test_generate_plugin_error(self) -> None:
        """Test plugin generation error handling."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        with patch.object(plugin.generation_service, 'generate_plugin', new_callable=AsyncMock) as mock_generate:
            mock_generate.side_effect = Exception("Generation failed")

            with pytest.raises(PluggingerValidationError) as exc_info:
                await plugin.generate_plugin(plugin_spec={"name": "test_plugin"})

            assert "Plugin generation failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_scaffold_plugin_structure_success(self) -> None:
        """Test successful plugin structure scaffolding."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        mock_result = {
            "success": True,
            "plugin_name": "test_plugin",
            "plugin_path": "./plugins/test_plugin",
            "template": "service"
        }

        with patch.object(plugin.generation_service, 'scaffold_plugin_structure', new_callable=AsyncMock) as mock_scaffold:
            mock_scaffold.return_value = mock_result

            result = await plugin.scaffold_plugin_structure(
                plugin_name="test_plugin",
                output_directory="./custom_plugins",
                template="service"
            )

            assert result == mock_result
            mock_scaffold.assert_called_once_with(
                plugin_name="test_plugin",
                output_directory="./custom_plugins",
                template="service"
            )

    @pytest.mark.asyncio
    async def test_scaffold_plugin_structure_default_params(self) -> None:
        """Test plugin structure scaffolding with default parameters."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        mock_result = {"success": True}

        with patch.object(plugin.generation_service, 'scaffold_plugin_structure', new_callable=AsyncMock) as mock_scaffold:
            mock_scaffold.return_value = mock_result

            await plugin.scaffold_plugin_structure(plugin_name="test_plugin")

            # Should use default values
            mock_scaffold.assert_called_once_with(
                plugin_name="test_plugin",
                output_directory="./plugins",  # Default value
                template="basic"  # Default value
            )

    @pytest.mark.asyncio
    async def test_generate_plugin_code_success(self) -> None:
        """Test successful plugin code generation."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        plugin_spec = {"name": "test_plugin", "services": []}
        plugin_path = "./plugins/test_plugin"
        mock_result = {
            "success": True,
            "ai_generated": True,
            "files_written": ["./plugins/test_plugin/test_plugin_plugin.py"]
        }

        with patch.object(plugin.generation_service, '_generate_ai_plugin_code', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = mock_result

            result = await plugin.generate_plugin_code(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path,
                enable_ai_generation=True
            )

            assert result == mock_result
            mock_generate.assert_called_once_with(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path,
                template="basic"
            )

    @pytest.mark.asyncio
    async def test_generate_plugin_code_template_mode(self) -> None:
        """Test plugin code generation in template mode."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        plugin_spec = {"name": "test_plugin"}
        plugin_path = "./plugins/test_plugin"
        mock_result = {
            "success": True,
            "ai_generated": False,
            "template_used": "basic"
        }

        with patch.object(plugin.generation_service, '_generate_template_plugin_code', new_callable=AsyncMock) as mock_generate:
            mock_generate.return_value = mock_result

            result = await plugin.generate_plugin_code(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path,
                enable_ai_generation=False
            )

            assert result == mock_result
            mock_generate.assert_called_once_with(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path,
                template="basic"
            )

    @pytest.mark.asyncio
    async def test_validate_plugin_structure_success(self) -> None:
        """Test successful plugin structure validation."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        plugin_path = "./plugins/test_plugin"
        mock_result = {
            "success": True,
            "plugin_path": plugin_path,
            "valid": True,
            "errors": [],
            "warnings": []
        }

        with patch.object(plugin.generation_service, 'validate_plugin_structure', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_result

            result = await plugin.validate_plugin_structure(plugin_path=plugin_path)

            assert result == mock_result
            mock_validate.assert_called_once_with(plugin_path=plugin_path)

    @pytest.mark.asyncio
    async def test_validate_plugin_structure_with_errors(self) -> None:
        """Test plugin structure validation with errors."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        plugin_path = "./plugins/test_plugin"
        mock_result = {
            "success": True,
            "plugin_path": plugin_path,
            "valid": False,
            "errors": ["Missing required file: manifest.yaml"],
            "warnings": []
        }

        with patch.object(plugin.generation_service, 'validate_plugin_structure', new_callable=AsyncMock) as mock_validate:
            mock_validate.return_value = mock_result

            result = await plugin.validate_plugin_structure(plugin_path=plugin_path)

            assert result["valid"] is False
            assert len(result["errors"]) == 1

    @pytest.mark.asyncio
    async def test_create_plugin_manifest_success(self) -> None:
        """Test successful plugin manifest creation."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        plugin_spec = {
            "name": "test_plugin",
            "version": "1.0.0",
            "description": "Test plugin"
        }
        plugin_path = "./plugins/test_plugin"
        mock_result = {
            "success": True,
            "plugin_name": "test_plugin",
            "manifest_path": "./plugins/test_plugin/manifest.yaml"
        }

        with patch.object(plugin.generation_service, 'create_plugin_manifest', new_callable=AsyncMock) as mock_create:
            mock_create.return_value = mock_result

            result = await plugin.create_plugin_manifest(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path
            )

            assert result == mock_result
            mock_create.assert_called_once_with(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path
            )

    @pytest.mark.asyncio
    async def test_create_plugin_manifest_error(self) -> None:
        """Test plugin manifest creation error handling."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        with patch.object(plugin.generation_service, 'create_plugin_manifest', new_callable=AsyncMock) as mock_create:
            mock_create.side_effect = Exception("Manifest creation failed")

            with pytest.raises(PluggingerValidationError) as exc_info:
                await plugin.create_plugin_manifest(
                    plugin_spec={"name": "test_plugin"},
                    plugin_path="./plugins/test_plugin"
                )

            assert "Plugin manifest creation failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_available_templates(self) -> None:
        """Test getting available templates."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        result = await plugin.get_available_templates()

        assert "templates" in result
        assert "default_template" in result
        assert "total_templates" in result
        assert result["default_template"] == "basic"
        assert result["total_templates"] == 4
        assert "basic" in result["templates"]
        assert "service" in result["templates"]
        assert "event" in result["templates"]
        assert "full" in result["templates"]

    @pytest.mark.asyncio
    async def test_get_generation_status(self) -> None:
        """Test getting generation status."""
        mock_app = Mock()
        plugin = PluginGeneratorPlugin(app=mock_app)

        result = await plugin.get_generation_status()

        assert result["plugin_name"] == "plugin_generator"
        assert result["status"] == "active"
        assert "configuration" in result
        assert "capabilities" in result
        assert "dependencies" in result
        assert result["configuration"]["default_template"] == "basic"
        assert "plugin_generation" in result["capabilities"]
        assert "ai_orchestrator" in result["dependencies"]
