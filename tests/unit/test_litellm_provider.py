"""
Tests for LiteLLM provider implementation.

This module tests the new LiteLLM-based provider that replaces
the legacy provider-specific implementations.
"""

import json
import os
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from plugginger.core.exceptions import PluggingerConfigurationError
from plugginger.plugins.core.llm_provider.services.litellm_provider import Lite<PERSON><PERSON>rovider


class TestLiteLLMProvider:
    """Test LiteLLM provider functionality."""

    def test_initialization_without_litellm(self) -> None:
        """Test provider initialization when LiteLLM is not available."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            with pytest.raises(PluggingerConfigurationError, match="LiteLLM is not available"):
                LiteLLMProvider(provider="openai", model="gpt-4")

    def test_initialization_with_litellm(self) -> None:
        """Test provider initialization when LiteLLM is available."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider(
                provider="openai",
                model="gpt-4",
                api_key="test-key",
                base_url="https://api.openai.com/v1"
            )
            
            assert provider.provider == "openai"
            assert provider.model == "gpt-4"
            assert provider.api_key == "test-key"
            assert provider.base_url == "https://api.openai.com/v1"

    def test_env_key_mapping(self) -> None:
        """Test environment variable key mapping for different providers."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider()
            
            # Test known providers
            assert provider._get_env_key_name("openai") == "OPENAI_API_KEY"
            assert provider._get_env_key_name("anthropic") == "ANTHROPIC_API_KEY"
            assert provider._get_env_key_name("gemini") == "GOOGLE_API_KEY"
            assert provider._get_env_key_name("groq") == "GROQ_API_KEY"
            
            # Test unknown provider
            assert provider._get_env_key_name("unknown") is None

    def test_base_url_mapping(self) -> None:
        """Test base URL environment variable mapping."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider()
            
            # Test known providers
            assert provider._get_base_url_key("openai") == "OPENAI_API_BASE"
            assert provider._get_base_url_key("anthropic") == "ANTHROPIC_API_BASE"
            assert provider._get_base_url_key("ollama") == "OLLAMA_API_BASE"
            
            # Test unknown provider
            assert provider._get_base_url_key("unknown") is None

    def test_model_name_formatting(self) -> None:
        """Test model name formatting for LiteLLM."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            # Test with provider prefix
            provider = LiteLLMProvider(provider="openai", model="gpt-4")
            assert provider._format_model_name() == "openai/gpt-4"
            
            # Test already formatted model
            provider = LiteLLMProvider(provider="openai", model="openai/gpt-4")
            assert provider._format_model_name() == "openai/gpt-4"
            
            # Test without provider
            provider = LiteLLMProvider(model="gpt-4")
            assert provider._format_model_name() == "gpt-4"
            
            # Test missing model
            provider = LiteLLMProvider(provider="openai")
            with pytest.raises(PluggingerConfigurationError, match="Model name is required"):
                provider._format_model_name()

    @pytest.mark.asyncio
    async def test_generate_text_without_litellm(self) -> None:
        """Test text generation when LiteLLM is not available."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            provider = LiteLLMProvider.__new__(LiteLLMProvider)  # Bypass __init__
            provider.provider = "mock"
            provider.model = "test-model"
            
            result = await provider.generate_text("Test prompt")
            
            assert result["success"] is True
            assert result["provider"] == "mock"
            assert result["model"] == "test-model"
            assert "Mock response" in result["content"]

    @pytest.mark.asyncio
    async def test_generate_text_with_litellm_success(self) -> None:
        """Test successful text generation with LiteLLM."""
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "Generated response"
        mock_response.choices[0].finish_reason = "stop"
        mock_response.model = "gpt-4"
        mock_response.usage.total_tokens = 150
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.completion', new_callable=AsyncMock) as mock_completion:
                mock_completion.return_value = mock_response
                
                provider = LiteLLMProvider(provider="openai", model="gpt-4")
                result = await provider.generate_text("Test prompt")
                
                assert result["success"] is True
                assert result["content"] == "Generated response"
                assert result["model"] == "gpt-4"
                assert result["tokens_used"] == 150
                assert result["finish_reason"] == "stop"

    @pytest.mark.asyncio
    async def test_generate_text_with_litellm_error(self) -> None:
        """Test text generation error handling with LiteLLM."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.completion', new_callable=AsyncMock) as mock_completion:
                mock_completion.side_effect = Exception("API Error")
                
                provider = LiteLLMProvider(provider="openai", model="gpt-4")
                result = await provider.generate_text("Test prompt")
                
                assert result["success"] is False
                assert result["error"] == "API Error"
                assert result["tokens_used"] == 0

    @pytest.mark.asyncio
    async def test_generate_structured_without_litellm(self) -> None:
        """Test structured generation when LiteLLM is not available."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            provider = LiteLLMProvider.__new__(LiteLLMProvider)  # Bypass __init__
            provider.provider = "mock"
            provider.model = "test-model"
            
            result = await provider.generate_structured(
                "System message",
                "User message", 
                "EBNF grammar"
            )
            
            assert result["success"] is True
            assert result["validated"] is True
            assert result["provider"] == "mock"
            assert json.loads(result["content"])  # Valid JSON

    @pytest.mark.asyncio
    async def test_generate_structured_with_valid_json(self) -> None:
        """Test structured generation with valid JSON response."""
        valid_json = '{"plugin_name": "test", "services": []}'
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = valid_json
        mock_response.choices[0].finish_reason = "stop"
        mock_response.model = "gpt-4"
        mock_response.usage.total_tokens = 200
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.completion', new_callable=AsyncMock) as mock_completion:
                mock_completion.return_value = mock_response
                
                provider = LiteLLMProvider(provider="openai", model="gpt-4")
                result = await provider.generate_structured(
                    "System message",
                    "User message",
                    "EBNF grammar"
                )
                
                assert result["success"] is True
                assert result["validated"] is True
                assert result["content"] == valid_json
                assert result["retries_used"] == 0

    @pytest.mark.asyncio
    async def test_generate_structured_with_invalid_json_retry(self) -> None:
        """Test structured generation with invalid JSON and retry logic."""
        invalid_json = "Not valid JSON"
        valid_json = '{"plugin_name": "test", "services": []}'
        
        mock_response_1 = MagicMock()
        mock_response_1.choices = [MagicMock()]
        mock_response_1.choices[0].message.content = invalid_json
        mock_response_1.model = "gpt-4"
        mock_response_1.usage.total_tokens = 100
        
        mock_response_2 = MagicMock()
        mock_response_2.choices = [MagicMock()]
        mock_response_2.choices[0].message.content = valid_json
        mock_response_2.model = "gpt-4"
        mock_response_2.usage.total_tokens = 150
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.completion', new_callable=AsyncMock) as mock_completion:
                mock_completion.side_effect = [mock_response_1, mock_response_2]
                
                provider = LiteLLMProvider(provider="openai", model="gpt-4")
                result = await provider.generate_structured(
                    "System message",
                    "User message",
                    "EBNF grammar",
                    max_retries=3
                )
                
                assert result["success"] is True
                assert result["validated"] is True
                assert result["content"] == valid_json
                assert result["retries_used"] == 1

    @pytest.mark.asyncio
    async def test_generate_structured_max_retries_exceeded(self) -> None:
        """Test structured generation when max retries are exceeded."""
        invalid_json = "Not valid JSON"
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = invalid_json
        mock_response.model = "gpt-4"
        mock_response.usage.total_tokens = 100
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.completion', new_callable=AsyncMock) as mock_completion:
                mock_completion.return_value = mock_response
                
                provider = LiteLLMProvider(provider="openai", model="gpt-4")
                result = await provider.generate_structured(
                    "System message",
                    "User message",
                    "EBNF grammar",
                    max_retries=2
                )
                
                assert result["success"] is False
                assert result["validated"] is False
                assert result["retries_used"] == 2
                assert "Max retries exceeded" in result["error"]

    def test_get_info(self) -> None:
        """Test provider information retrieval."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider(
                provider="openai",
                model="gpt-4",
                api_key="test-key",
                base_url="https://api.openai.com/v1"
            )
            
            info = provider.get_info()
            
            assert info["provider"] == "openai"
            assert info["model"] == "gpt-4"
            assert info["base_url"] == "https://api.openai.com/v1"
            assert info["has_api_key"] is True
            assert info["litellm_available"] is True
            assert "text_generation" in info["supported_features"]
            assert "structured_generation" in info["supported_features"]
            assert "streaming" in info["supported_features"]

    def test_configure_litellm_environment_variables(self) -> None:
        """Test LiteLLM configuration with environment variables."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch.dict(os.environ, {}, clear=True):
                provider = LiteLLMProvider(
                    provider="openai",
                    model="gpt-4",
                    api_key="test-key",
                    base_url="https://api.openai.com/v1"
                )
                
                # Check that environment variables were set
                assert os.environ.get("OPENAI_API_KEY") == "test-key"
                assert os.environ.get("OPENAI_API_BASE") == "https://api.openai.com/v1"


class TestLiteLLMProviderFactory:
    """Test LiteLLM provider factory functionality."""

    def test_create_provider_basic(self) -> None:
        """Test basic provider creation."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProviderFactory.create_provider(
                provider="openai",
                model="gpt-4",
                api_key="test-key"
            )

            assert provider.provider == "openai"
            assert provider.model == "gpt-4"
            assert provider.api_key == "test-key"

    def test_list_supported_providers(self) -> None:
        """Test listing supported providers."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        providers = LiteLLMProviderFactory.list_supported_providers()

        assert "openai" in providers
        assert "anthropic" in providers
        assert "gemini" in providers
        assert "ollama" in providers
        assert len(providers) >= 10  # Should have many providers

    def test_get_provider_info(self) -> None:
        """Test getting provider configuration info."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        openai_info = LiteLLMProviderFactory.get_provider_info("openai")
        assert openai_info is not None
        assert "api_key" in openai_info
        assert "OPENAI_API_KEY" in openai_info["api_key"]
        assert openai_info["default_model"] == "gpt-4o-mini"

        unknown_info = LiteLLMProviderFactory.get_provider_info("unknown")
        assert unknown_info is None

    def test_auto_detect_provider_openai(self) -> None:
        """Test auto-detection of OpenAI provider."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        with patch.dict(os.environ, {"OPENAI_API_KEY": "sk-real-key"}, clear=True):
            detected = LiteLLMProviderFactory._auto_detect_provider()

            assert detected is not None
            assert detected["provider"] == "openai"
            assert detected["api_key"] == "sk-real-key"

    def test_auto_detect_provider_skip_test_keys(self) -> None:
        """Test that auto-detection skips test API keys."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        with patch.dict(os.environ, {"OPENAI_API_KEY": "sk-test-123"}, clear=True):
            detected = LiteLLMProviderFactory._auto_detect_provider()

            # Should skip test key and return None (or detect another provider)
            if detected:
                assert detected["provider"] != "openai"

    def test_auto_detect_provider_ollama(self) -> None:
        """Test auto-detection of Ollama provider."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        with patch.dict(os.environ, {}, clear=True):
            # Ollama doesn't need API key, should be detected if no other providers
            detected = LiteLLMProviderFactory._auto_detect_provider()

            # Should detect Ollama as fallback
            assert detected is not None
            assert detected["provider"] == "ollama"
            assert detected["base_url"] == "http://localhost:11434"

    def test_create_from_env_explicit_provider(self) -> None:
        """Test creating provider from environment with explicit provider type."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch.dict(os.environ, {"OPENAI_API_KEY": "test-key"}, clear=True):
                provider = LiteLLMProviderFactory.create_from_env(provider_type="openai")

                assert provider.provider == "openai"
                assert provider.api_key == "test-key"

    def test_create_from_env_auto_detect(self) -> None:
        """Test creating provider from environment with auto-detection."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch.dict(os.environ, {"GOOGLE_API_KEY": "test-key"}, clear=True):
                provider = LiteLLMProviderFactory.create_from_env()

                assert provider.provider == "gemini"
                assert provider.api_key == "test-key"

    def test_create_from_env_no_provider_found(self) -> None:
        """Test error when no provider configuration is found."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        with patch.dict(os.environ, {}, clear=True):
            with patch.object(LiteLLMProviderFactory, '_auto_detect_provider', return_value=None):
                with pytest.raises(PluggingerConfigurationError, match="No LLM provider configuration found"):
                    LiteLLMProviderFactory.create_from_env()

    def test_get_env_value(self) -> None:
        """Test environment variable value retrieval."""
        from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory

        with patch.dict(os.environ, {"TEST_VAR_1": "value1", "TEST_VAR_2": "value2"}, clear=True):
            # Should return first available value
            value = LiteLLMProviderFactory._get_env_value(["MISSING_VAR", "TEST_VAR_1", "TEST_VAR_2"])
            assert value == "value1"

            # Should return None if no variables found
            value = LiteLLMProviderFactory._get_env_value(["MISSING_VAR_1", "MISSING_VAR_2"])
            assert value is None
