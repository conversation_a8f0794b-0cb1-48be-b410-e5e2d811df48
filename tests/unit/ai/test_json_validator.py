"""
Tests for JSON validation with retry logic.
"""

import json
from unittest.mock import Async<PERSON><PERSON>, <PERSON><PERSON>

import pytest

from plugginger.ai.json_validator import JSONValidator, RetryableJSONProcessor
from plugginger.ai.types import LLMResponse, PluginSpec, ServiceSpec, StructuredPrompt
from plugginger.core.exceptions import PluggingerValidationError


class TestJSONValidator:
    """Tests for JSON validator."""

    def test_valid_json_parsing(self) -> None:
        """Test successful JSON parsing and validation."""
        validator = JSONValidator()

        valid_plugin_data = {
            "name": "test_plugin",
            "version": "1.0.0",
            "description": "Test plugin",
            "class_name": "TestPlugin",
            "services": [],
            "event_listeners": [],
            "dependencies": []
        }

        json_content = json.dumps(valid_plugin_data)
        result = validator.validate_and_parse(json_content, PluginSpec)

        assert result.valid is True
        assert result.parsed_data is not None
        assert result.parsed_data["name"] == "test_plugin"
        assert len(result.validation_errors) == 0

    def test_invalid_json_syntax(self) -> None:
        """Test handling of invalid JSON syntax."""
        validator = JSONValidator()

        invalid_json = '{"name": "test", "invalid": }'
        result = validator.validate_and_parse(invalid_json, PluginSpec)

        assert result.valid is False
        assert result.parsed_data is None
        assert len(result.validation_errors) == 1
        assert result.validation_errors[0].error_type == "json_parse_error"
        assert "Invalid JSON syntax" in result.validation_errors[0].message

    def test_schema_validation_error(self) -> None:
        """Test handling of schema validation errors."""
        validator = JSONValidator()

        # Missing required fields
        invalid_data = {
            "name": "test_plugin",
            # Missing required fields: version, description, class_name
        }

        json_content = json.dumps(invalid_data)
        result = validator.validate_and_parse(json_content, PluginSpec)

        assert result.valid is False
        assert result.parsed_data is None
        assert len(result.validation_errors) > 0

        # Check that we have validation errors for missing fields
        error_types = [error.error_type for error in result.validation_errors]
        assert "schema_validation_error" in error_types

    def test_service_spec_validation(self) -> None:
        """Test validation of service specification."""
        validator = JSONValidator()

        valid_service_data = {
            "name": "test_service",
            "description": "Test service",
            "parameters": [],
            "return_type": "str",
            "async_method": True,
            "timeout_seconds": None
        }

        json_content = json.dumps(valid_service_data)
        result = validator.validate_and_parse(json_content, ServiceSpec)

        assert result.valid is True
        assert result.parsed_data["name"] == "test_service"

    def test_fix_suggestion_generation(self) -> None:
        """Test generation of fix suggestions for validation errors."""
        validator = JSONValidator()

        # Test missing field suggestion
        error = {
            'type': 'missing',
            'loc': ['name'],
            'msg': 'Field required'
        }

        suggestion = validator._generate_fix_suggestion(error)
        assert "Add required field 'name'" in suggestion

        # Test type error suggestion
        error = {
            'type': 'type_error.str',
            'loc': ['version'],
            'msg': 'Input should be a valid string'
        }

        suggestion = validator._generate_fix_suggestion(error)
        assert "Ensure field 'version' has the correct data type" in suggestion


class TestRetryableJSONProcessor:
    """Tests for retryable JSON processor."""

    def test_initialization(self) -> None:
        """Test processor initialization."""
        validator = JSONValidator()
        processor = RetryableJSONProcessor(validator)

        assert processor.validator is validator

    @pytest.mark.asyncio
    async def test_successful_processing_first_attempt(self) -> None:
        """Test successful processing on first attempt."""
        validator = JSONValidator()
        processor = RetryableJSONProcessor(validator)

        # Mock LLM provider
        mock_provider = Mock()
        mock_provider.generate_structured = AsyncMock()

        valid_plugin_data = {
            "name": "test_plugin",
            "version": "1.0.0",
            "description": "Test plugin",
            "class_name": "TestPlugin",
            "services": [],
            "event_listeners": [],
            "dependencies": []
        }

        mock_response = LLMResponse(
            content=json.dumps(valid_plugin_data),
            model="test-model",
            provider="test",
            tokens_used=100,
            success=True
        )
        mock_provider.generate_structured.return_value = mock_response

        prompt = StructuredPrompt(
            system_message="Test system message",
            user_message="Test user message",
            ebnf_grammar="test grammar"
        )

        result = await processor.process_with_retry(
            llm_provider=mock_provider,
            prompt=prompt,
            target_model=PluginSpec,
            max_retries=3
        )

        assert result is not None
        assert isinstance(result, PluginSpec)
        assert result.name == "test_plugin"

        # Should only call LLM once
        assert mock_provider.generate_structured.call_count == 1

    @pytest.mark.asyncio
    async def test_retry_on_validation_failure(self) -> None:
        """Test retry logic when validation fails."""
        validator = JSONValidator()
        processor = RetryableJSONProcessor(validator)

        # Mock LLM provider
        mock_provider = Mock()
        mock_provider.generate_structured = AsyncMock()

        # First response: invalid JSON
        invalid_response = LLMResponse(
            content='{"name": "test", invalid}',
            model="test-model",
            provider="test",
            tokens_used=100,
            success=True
        )

        # Second response: valid JSON
        valid_plugin_data = {
            "name": "test_plugin",
            "version": "1.0.0",
            "description": "Test plugin",
            "class_name": "TestPlugin",
            "services": [],
            "event_listeners": [],
            "dependencies": []
        }
        valid_response = LLMResponse(
            content=json.dumps(valid_plugin_data),
            model="test-model",
            provider="test",
            tokens_used=100,
            success=True
        )

        mock_provider.generate_structured.side_effect = [invalid_response, valid_response]

        prompt = StructuredPrompt(
            system_message="Test system message",
            user_message="Test user message",
            ebnf_grammar="test grammar"
        )

        result = await processor.process_with_retry(
            llm_provider=mock_provider,
            prompt=prompt,
            target_model=PluginSpec,
            max_retries=3
        )

        assert result is not None
        assert isinstance(result, PluginSpec)
        assert result.name == "test_plugin"

        # Should call LLM twice (first failed, second succeeded)
        assert mock_provider.generate_structured.call_count == 2

    @pytest.mark.asyncio
    async def test_max_retries_exceeded(self) -> None:
        """Test behavior when max retries are exceeded."""
        validator = JSONValidator()
        processor = RetryableJSONProcessor(validator)

        # Mock LLM provider that always returns invalid JSON
        mock_provider = Mock()
        mock_provider.generate_structured = AsyncMock()

        invalid_response = LLMResponse(
            content='{"invalid": json}',
            model="test-model",
            provider="test",
            tokens_used=100,
            success=True
        )
        mock_provider.generate_structured.return_value = invalid_response

        prompt = StructuredPrompt(
            system_message="Test system message",
            user_message="Test user message",
            ebnf_grammar="test grammar"
        )

        with pytest.raises(PluggingerValidationError) as exc_info:
            await processor.process_with_retry(
                llm_provider=mock_provider,
                prompt=prompt,
                target_model=PluginSpec,
                max_retries=2
            )

        assert "JSON validation failed after 3 attempts" in str(exc_info.value)

        # Should call LLM max_retries + 1 times
        assert mock_provider.generate_structured.call_count == 3

    @pytest.mark.asyncio
    async def test_llm_generation_failure(self) -> None:
        """Test handling of LLM generation failures."""
        validator = JSONValidator()
        processor = RetryableJSONProcessor(validator)

        # Mock LLM provider that fails
        mock_provider = Mock()
        mock_provider.generate_structured = AsyncMock()

        failed_response = LLMResponse(
            content="",
            model="test-model",
            provider="test",
            tokens_used=0,
            success=False,
            error_message="API rate limit exceeded"
        )
        mock_provider.generate_structured.return_value = failed_response

        prompt = StructuredPrompt(
            system_message="Test system message",
            user_message="Test user message",
            ebnf_grammar="test grammar"
        )

        with pytest.raises(PluggingerValidationError):
            await processor.process_with_retry(
                llm_provider=mock_provider,
                prompt=prompt,
                target_model=PluginSpec,
                max_retries=1
            )

    def test_prompt_enhancement_with_errors(self) -> None:
        """Test enhancement of prompts with error feedback."""
        validator = JSONValidator()
        processor = RetryableJSONProcessor(validator)

        original_prompt = StructuredPrompt(
            system_message="Original system message",
            user_message="Original user message",
            ebnf_grammar="original grammar"
        )

        from plugginger.ai.types import ValidationError as AIValidationError
        errors = [
            AIValidationError(
                error_type="missing_field",
                message="Field 'name' is required",
                suggestion="Add the name field"
            )
        ]

        enhanced_prompt = processor._enhance_prompt_with_errors(original_prompt, errors)

        assert enhanced_prompt.system_message == original_prompt.system_message
        assert enhanced_prompt.ebnf_grammar == original_prompt.ebnf_grammar
        assert "validation errors" in enhanced_prompt.user_message
        assert "missing_field: Field 'name' is required" in enhanced_prompt.user_message
        assert "Add the name field" in enhanced_prompt.user_message
