"""
Tests for wiring analyzer functionality.
"""

from unittest.mock import Mock, patch

import pytest

from plugginger.ai.types import (
    AppWiringContext,
    DependencySpec,
    PluginSpec,
    ServiceSpec,
    WiringValidationResult,
)
from plugginger.ai.wiring_analyzer import WiringAnalyzer
from plugginger.core.exceptions import PluggingerValidationError


class TestWiringAnalyzer:
    """Tests for WiringAnalyzer class."""

    def test_initialization(self) -> None:
        """Test analyzer initialization."""
        analyzer = WiringAnalyzer()
        assert analyzer.logger is not None

    @pytest.mark.asyncio
    async def test_analyze_app_context_from_factory(self) -> None:
        """Test app context analysis from factory function."""
        analyzer = WiringAnalyzer()

        # Mock the dependencies
        mock_app_builder = Mock()
        mock_inspector = Mock()
        mock_analysis_result = {
            "app_info": {"name": "test_app"},
            "plugins": [
                {
                    "registration_name": "test_plugin",
                    "services": [
                        {
                            "name": "test_service",
                            "signature": "async def test_service(self) -> str",
                            "docstring": "Test service",
                            "parameters": [],
                            "return_annotation": "str"
                        }
                    ],
                    "event_listeners": [
                        {
                            "event_pattern": "test.event",
                            "method_name": "on_test_event"
                        }
                    ]
                }
            ],
            "dependency_graph": {
                "nodes": [{"id": "test_plugin"}],
                "edges": []
            }
        }

        with patch('plugginger.ai.wiring_analyzer.resolve_app_factory', return_value=mock_app_builder), \
             patch('plugginger.ai.wiring_analyzer.AppInspector', return_value=mock_inspector):

            mock_inspector.analyze.return_value = mock_analysis_result

            result = await analyzer.analyze_app_context("test_module:create_app")

            assert isinstance(result, AppWiringContext)
            assert result.app_name == "test_app"
            assert len(result.services) == 1
            assert result.services[0]["name"] == "test_service"
            assert "test.event" in result.events

    @pytest.mark.asyncio
    async def test_analyze_app_context_from_manifest(self) -> None:
        """Test app context analysis from manifest file."""
        analyzer = WiringAnalyzer()

        with patch('pathlib.Path.exists', return_value=True):
            result = await analyzer.analyze_app_context("test_app.yaml")

            assert isinstance(result, AppWiringContext)
            assert result.app_name == "test_app"

    @pytest.mark.asyncio
    async def test_analyze_app_context_failure(self) -> None:
        """Test app context analysis failure handling."""
        analyzer = WiringAnalyzer()

        with patch('plugginger.ai.wiring_analyzer.resolve_app_factory', side_effect=Exception("Test error")):
            with pytest.raises(PluggingerValidationError) as exc_info:
                await analyzer.analyze_app_context("invalid:factory")

            assert "Failed to analyze app context" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_validate_plugin_compatibility_success(self) -> None:
        """Test successful plugin compatibility validation."""
        analyzer = WiringAnalyzer()

        plugin_spec = PluginSpec(
            name="test_plugin",
            description="Test plugin",
            class_name="TestPlugin",
            services=[
                ServiceSpec(
                    name="test_service",
                    description="Test service",
                    return_type="str"
                )
            ],
            dependencies=[
                DependencySpec(name="auth_plugin", optional=False)
            ]
        )

        app_context = AppWiringContext(
            app_name="test_app",
            services=[
                {
                    "name": "auth_service",
                    "plugin": "auth_plugin",
                    "signature": "async def auth_service(self) -> bool",
                    "description": "Authentication service"
                }
            ],
            events=["user.login", "user.logout"],
            dependency_graph={"auth_plugin": []}
        )

        result = await analyzer.validate_plugin_compatibility(plugin_spec, app_context)

        assert isinstance(result, WiringValidationResult)
        assert result.valid is True
        assert len(result.errors) == 0

    @pytest.mark.asyncio
    async def test_validate_plugin_compatibility_missing_dependency(self) -> None:
        """Test plugin compatibility validation with missing dependency."""
        analyzer = WiringAnalyzer()

        plugin_spec = PluginSpec(
            name="test_plugin",
            description="Test plugin",
            class_name="TestPlugin",
            dependencies=[
                DependencySpec(name="missing_plugin", optional=False)
            ]
        )

        app_context = AppWiringContext(
            app_name="test_app",
            services=[],
            events=[],
            dependency_graph={}
        )

        result = await analyzer.validate_plugin_compatibility(plugin_spec, app_context)

        assert result.valid is False
        assert len(result.errors) > 0
        assert "missing_plugin" in result.errors[0]

    @pytest.mark.asyncio
    async def test_validate_plugin_compatibility_cycle_detection(self) -> None:
        """Test dependency cycle detection."""
        analyzer = WiringAnalyzer()

        plugin_spec = PluginSpec(
            name="plugin_a",
            description="Plugin A",
            class_name="PluginA",
            dependencies=[
                DependencySpec(name="plugin_b", optional=False)
            ]
        )

        app_context = AppWiringContext(
            app_name="test_app",
            services=[],
            events=[],
            dependency_graph={
                "plugin_b": ["plugin_a"]  # plugin_b depends on plugin_a
            }
        )

        result = await analyzer.validate_plugin_compatibility(plugin_spec, app_context)

        assert result.valid is False
        assert any("cycle detected" in error.lower() for error in result.errors)

    @pytest.mark.asyncio
    async def test_suggest_wiring_improvements(self) -> None:
        """Test wiring improvement suggestions."""
        analyzer = WiringAnalyzer()

        plugin_spec = PluginSpec(
            name="email_plugin",
            description="Email service plugin for sending notifications",
            class_name="EmailPlugin"
        )

        app_context = AppWiringContext(
            app_name="test_app",
            services=[
                {
                    "name": "send_email",
                    "plugin": "email_service",
                    "signature": "async def send_email(self, to: str, subject: str) -> bool",
                    "description": "Send email to user"
                },
                {
                    "name": "authenticate",
                    "plugin": "auth_service",
                    "signature": "async def authenticate(self, token: str) -> bool",
                    "description": "Authenticate user token"
                }
            ],
            events=["user.login", "email.sent"],
            dependency_graph={}
        )

        suggestions = await analyzer.suggest_wiring_improvements(plugin_spec, app_context)

        assert isinstance(suggestions, list)
        assert len(suggestions) > 0

        # Should suggest email service integration
        email_suggestions = [s for s in suggestions if "email" in s.description.lower()]
        assert len(email_suggestions) > 0
        assert email_suggestions[0].confidence > 0.5

    def test_extract_wiring_context(self) -> None:
        """Test wiring context extraction from analysis result."""
        analyzer = WiringAnalyzer()

        analysis_result = {
            "app_info": {"name": "test_app"},
            "plugins": [
                {
                    "registration_name": "plugin1",
                    "services": [
                        {
                            "name": "service1",
                            "signature": "async def service1(self) -> str",
                            "docstring": "Test service 1",
                            "parameters": [],
                            "return_annotation": "str"
                        }
                    ],
                    "event_listeners": [
                        {"event_pattern": "test.event1"}
                    ]
                }
            ],
            "dependency_graph": {
                "nodes": [{"id": "plugin1"}],
                "edges": [{"source": "plugin1", "target": "plugin2"}]
            }
        }

        context = analyzer._extract_wiring_context(analysis_result)

        assert context.app_name == "test_app"
        assert len(context.services) == 1
        assert context.services[0]["name"] == "service1"
        assert "test.event1" in context.events
        assert "plugin1" in context.dependency_graph
        assert "plugin2" in context.dependency_graph["plugin1"]

    def test_has_transitive_dependency(self) -> None:
        """Test transitive dependency detection."""
        analyzer = WiringAnalyzer()

        dependency_graph = {
            "a": ["b"],
            "b": ["c"],
            "c": ["d"],
            "d": []
        }

        # Test direct dependency
        assert analyzer._has_transitive_dependency("a", "b", dependency_graph) is True

        # Test transitive dependency
        assert analyzer._has_transitive_dependency("a", "c", dependency_graph) is True
        assert analyzer._has_transitive_dependency("a", "d", dependency_graph) is True

        # Test no dependency
        assert analyzer._has_transitive_dependency("d", "a", dependency_graph) is False

        # Test cycle detection
        cycle_graph = {
            "a": ["b"],
            "b": ["a"]
        }
        assert analyzer._has_transitive_dependency("a", "a", cycle_graph) is False

    def test_generate_integration_suggestions(self) -> None:
        """Test integration suggestion generation."""
        analyzer = WiringAnalyzer()

        plugin_spec = PluginSpec(
            name="test_plugin",
            description="Test plugin",
            class_name="TestPlugin"
        )

        app_context = AppWiringContext(
            app_name="test_app",
            services=[
                {"name": "auth_service", "plugin": "auth"}
            ],
            events=["user.login"],
            dependency_graph={}
        )

        suggestions = analyzer._generate_integration_suggestions(plugin_spec, app_context)

        assert isinstance(suggestions, list)
        # Should suggest adding services, event listeners, or dependencies
        assert len(suggestions) > 0
