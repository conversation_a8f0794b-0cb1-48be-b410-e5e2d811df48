"""
Tests for LLM provider abstraction and factory.
"""

import os
from unittest.mock import patch

import pytest

from plugginger.ai.llm_provider import (
    AnthropicProvider,
    LLMProviderFactory,
    LocalProvider,
    OpenAIProvider,
)
from plugginger.ai.types import LLMResponse, StructuredPrompt
from plugginger.core.exceptions import PluggingerConfigurationError


class TestLLMProviderFactory:
    """Tests for LLM provider factory."""

    def test_create_openai_provider_success(self) -> None:
        """Test successful OpenAI provider creation."""
        with patch.dict(os.environ, {
            'PLUGGINGER_LLM_PROVIDER': 'openai',
            'PLUGGINGER_LLM_API_KEY': 'test-key'
        }):
            provider = LLMProviderFactory.create_provider()
            assert isinstance(provider, OpenAIProvider)
            assert provider.api_key == 'test-key'
            assert provider.model == 'gpt-4'

    def test_create_anthropic_provider_success(self) -> None:
        """Test successful Anthropic provider creation."""
        with patch.dict(os.environ, {
            'PLUGGINGER_LLM_PROVIDER': 'anthropic',
            'PLUGGINGER_LLM_API_KEY': 'test-key',
            'PLUGGINGER_LLM_MODEL': 'claude-3-opus-20240229'
        }):
            provider = LLMProviderFactory.create_provider()
            assert isinstance(provider, AnthropicProvider)
            assert provider.api_key == 'test-key'
            assert provider.model == 'claude-3-opus-20240229'

    def test_create_local_provider_success(self) -> None:
        """Test successful local provider creation."""
        with patch.dict(os.environ, {
            'PLUGGINGER_LLM_PROVIDER': 'local',
            'PLUGGINGER_LLM_BASE_URL': 'http://localhost:8080'
        }):
            provider = LLMProviderFactory.create_provider()
            assert isinstance(provider, LocalProvider)
            assert provider.base_url == 'http://localhost:8080'

    def test_missing_provider_type_error(self) -> None:
        """Test error when provider type is missing."""
        with patch.dict(os.environ, {}, clear=True):
            with pytest.raises(PluggingerConfigurationError) as exc_info:
                LLMProviderFactory.create_provider()
            assert "PLUGGINGER_LLM_PROVIDER environment variable is required" in str(exc_info.value)

    def test_missing_api_key_error(self) -> None:
        """Test error when API key is missing for non-local provider."""
        with patch.dict(os.environ, {
            'PLUGGINGER_LLM_PROVIDER': 'openai'
        }, clear=True):
            with pytest.raises(PluggingerConfigurationError) as exc_info:
                LLMProviderFactory.create_provider()
            assert "PLUGGINGER_LLM_API_KEY environment variable is required" in str(exc_info.value)

    def test_unsupported_provider_error(self) -> None:
        """Test error for unsupported provider type."""
        with patch.dict(os.environ, {
            'PLUGGINGER_LLM_PROVIDER': 'unsupported',
            'PLUGGINGER_LLM_API_KEY': 'test-key'
        }):
            with pytest.raises(PluggingerConfigurationError) as exc_info:
                LLMProviderFactory.create_provider()
            assert "Unsupported LLM provider: unsupported" in str(exc_info.value)


class TestOpenAIProvider:
    """Tests for OpenAI provider."""

    def test_initialization(self) -> None:
        """Test OpenAI provider initialization."""
        provider = OpenAIProvider(api_key="test-key")
        assert provider.api_key == "test-key"
        assert provider.model == "gpt-4"
        assert provider.base_url == "https://api.openai.com/v1"

    def test_custom_model_and_base_url(self) -> None:
        """Test OpenAI provider with custom model and base URL."""
        provider = OpenAIProvider(
            api_key="test-key",
            model="gpt-3.5-turbo",
            base_url="https://custom.openai.com"
        )
        assert provider.model == "gpt-3.5-turbo"
        assert provider.base_url == "https://custom.openai.com"

    @pytest.mark.asyncio
    async def test_generate_structured_mock_response(self) -> None:
        """Test structured generation with mock response."""
        provider = OpenAIProvider(api_key="test-key")
        prompt = StructuredPrompt(
            system_message="You are a helpful assistant",
            user_message="Generate a plugin",
            ebnf_grammar="root ::= plugin"
        )

        response = await provider.generate_structured(prompt)

        assert isinstance(response, LLMResponse)
        assert response.provider == "openai"
        assert response.model == "gpt-4"
        assert response.success is True
        assert '"plugin_name"' in response.content


class TestAnthropicProvider:
    """Tests for Anthropic provider."""

    def test_initialization(self) -> None:
        """Test Anthropic provider initialization."""
        provider = AnthropicProvider(api_key="test-key")
        assert provider.api_key == "test-key"
        assert provider.model == "claude-3-sonnet-20240229"
        assert provider.base_url == "https://api.anthropic.com"

    @pytest.mark.asyncio
    async def test_generate_structured_mock_response(self) -> None:
        """Test structured generation with mock response."""
        provider = AnthropicProvider(api_key="test-key")
        prompt = StructuredPrompt(
            system_message="You are a helpful assistant",
            user_message="Generate a plugin",
            ebnf_grammar="root ::= plugin"
        )

        response = await provider.generate_structured(prompt)

        assert isinstance(response, LLMResponse)
        assert response.provider == "anthropic"
        assert response.success is True


class TestLocalProvider:
    """Tests for local provider."""

    def test_initialization(self) -> None:
        """Test local provider initialization."""
        provider = LocalProvider(api_key="")
        assert provider.api_key == ""
        assert provider.model == "llama2"
        assert provider.base_url == "http://localhost:8080"

    @pytest.mark.asyncio
    async def test_generate_structured_mock_response(self) -> None:
        """Test structured generation with mock response."""
        provider = LocalProvider(api_key="")
        prompt = StructuredPrompt(
            system_message="You are a helpful assistant",
            user_message="Generate a plugin",
            ebnf_grammar="root ::= plugin"
        )

        response = await provider.generate_structured(prompt)

        assert isinstance(response, LLMResponse)
        assert response.provider == "local"
        assert response.success is True
