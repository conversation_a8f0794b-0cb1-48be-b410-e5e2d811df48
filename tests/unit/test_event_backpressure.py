"""
Tests for event system backpressure mechanisms.

This module tests the semaphore-based backpressure implementation
that replaces naive asyncio.sleep() patterns.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock

from plugginger._internal.runtime.dispatcher import EventDispatcher, ListenerTaskManager
from plugginger.core.types import EventHandlerType


class TestEventBackpressure:
    """Test semaphore-based backpressure in event system."""

    @pytest.fixture
    def mock_fault_handler(self) -> MagicMock:
        """Create mock fault policy handler."""
        handler = MagicMock()
        handler.should_invoke.return_value = True
        handler.handle_error = MagicMock()
        return handler

    @pytest.fixture
    def mock_logger(self) -> MagicMock:
        """Create mock logger."""
        return MagicMock()

    @pytest.fixture
    def event_dispatcher(self, mock_fault_handler: MagicMock, mock_logger: MagicMock) -> EventDispatcher:
        """Create event dispatcher with small concurrency limit for testing."""
        return EventDispatcher(
            fault_handler=mock_fault_handler,
            logger=mock_logger,
            default_listener_timeout=1.0,
            max_concurrent_listener_tasks=2  # Small limit for testing
        )

    @pytest.mark.asyncio
    async def test_semaphore_initialization(self, event_dispatcher: EventDispatcher) -> None:
        """Test that semaphore is properly initialized."""
        # Access private attribute for testing
        assert hasattr(event_dispatcher, '_task_semaphore')
        assert isinstance(event_dispatcher._task_semaphore, asyncio.Semaphore)
        assert event_dispatcher._task_semaphore._value == 2  # Max concurrent tasks

    @pytest.mark.asyncio
    async def test_backpressure_with_task_completion_waiting(
        self, event_dispatcher: EventDispatcher, mock_logger: MagicMock
    ) -> None:
        """Test that backpressure waits for actual task completion."""
        # Create slow listeners to trigger backpressure
        slow_listener_called = asyncio.Event()
        
        async def slow_listener(event_data: dict) -> None:
            slow_listener_called.set()
            await asyncio.sleep(0.2)  # Slow listener
            
        async def fast_listener(event_data: dict) -> None:
            await asyncio.sleep(0.01)  # Fast listener

        # Register listeners
        event_dispatcher.add_listener("test.*", slow_listener)
        event_dispatcher.add_listener("test.*", fast_listener)

        # Emit multiple events to trigger backpressure
        emit_tasks = []
        for i in range(3):  # More than max_concurrent_listener_tasks (2)
            task = asyncio.create_task(
                event_dispatcher.emit_event(f"test.event_{i}", {"data": i})
            )
            emit_tasks.append(task)

        # Wait for all emissions to complete
        await asyncio.gather(*emit_tasks)

        # Verify that slow listener was called (backpressure didn't prevent execution)
        assert slow_listener_called.is_set()

        # Verify backpressure warning was logged
        mock_logger.assert_any_call(
            "[Plugginger] Warning: Max concurrent listener tasks reached (2)"
        )

    @pytest.mark.asyncio
    async def test_semaphore_controlled_listener_execution(self, mock_fault_handler: MagicMock, mock_logger: MagicMock) -> None:
        """Test semaphore-controlled listener execution."""
        task_manager = ListenerTaskManager(mock_fault_handler, mock_logger, 1.0)
        
        # Create semaphore with limit of 1
        semaphore = asyncio.Semaphore(1)
        
        execution_order = []
        
        async def test_listener(event_data: dict) -> None:
            execution_order.append("start")
            await asyncio.sleep(0.1)
            execution_order.append("end")

        # Execute two listeners concurrently with semaphore
        tasks = []
        for i in range(2):
            task = asyncio.create_task(
                task_manager._semaphore_controlled_listener(
                    test_listener, {"data": i}, "test.event", False, semaphore
                )
            )
            tasks.append(task)

        await asyncio.gather(*tasks)

        # With semaphore limit of 1, execution should be sequential
        assert execution_order == ["start", "end", "start", "end"]

    @pytest.mark.asyncio
    async def test_backpressure_fallback_without_semaphore(self, mock_fault_handler: MagicMock, mock_logger: MagicMock) -> None:
        """Test that listeners work without semaphore (fallback mode)."""
        task_manager = ListenerTaskManager(mock_fault_handler, mock_logger, 1.0)
        
        listener_called = False
        
        async def test_listener(event_data: dict) -> None:
            nonlocal listener_called
            listener_called = True

        # Execute listener without semaphore
        await task_manager._semaphore_controlled_listener(
            test_listener, {"data": "test"}, "test.event", False, None
        )

        assert listener_called

    @pytest.mark.asyncio
    async def test_active_tasks_snapshot(self, mock_fault_handler: MagicMock, mock_logger: MagicMock) -> None:
        """Test getting snapshot of active tasks for backpressure handling."""
        task_manager = ListenerTaskManager(mock_fault_handler, mock_logger, 1.0)
        
        # Initially no active tasks
        snapshot = task_manager.get_active_tasks_snapshot()
        assert len(snapshot) == 0
        
        # Create a long-running listener
        async def long_listener(event_data: dict) -> None:
            await asyncio.sleep(0.5)
        
        # Execute listener and get snapshot while it's running
        task = asyncio.create_task(
            task_manager.execute_listeners(
                [("test.*", long_listener)], "test.event", {"data": "test"}
            )
        )
        
        # Give task time to start
        await asyncio.sleep(0.01)
        
        # Get snapshot of active tasks
        snapshot = task_manager.get_active_tasks_snapshot()
        assert len(snapshot) > 0
        
        # Snapshot should be a copy, not the original set
        assert snapshot is not task_manager._active_tasks
        
        # Wait for task to complete
        await task

    @pytest.mark.asyncio
    async def test_backpressure_error_handling(self, event_dispatcher: EventDispatcher, mock_logger: MagicMock) -> None:
        """Test that backpressure errors don't break event emission."""
        # Mock the task manager to raise an error during backpressure handling
        original_get_active_tasks = event_dispatcher._task_manager.get_active_tasks_snapshot
        
        def error_snapshot() -> set:
            raise RuntimeError("Simulated backpressure error")
        
        event_dispatcher._task_manager.get_active_tasks_snapshot = error_snapshot
        
        # Add a listener
        async def test_listener(event_data: dict) -> None:
            pass
            
        event_dispatcher.add_listener("test.*", test_listener)
        
        # Force backpressure by setting task count high
        event_dispatcher._task_manager._active_tasks.add(asyncio.create_task(asyncio.sleep(1)))
        event_dispatcher._task_manager._active_tasks.add(asyncio.create_task(asyncio.sleep(1)))
        
        # Emit event - should not raise error despite backpressure failure
        await event_dispatcher.emit_event("test.event", {"data": "test"})
        
        # Verify error was logged
        mock_logger.assert_any_call(
            "[Plugginger] Backpressure handling error: RuntimeError('Simulated backpressure error')"
        )
        
        # Restore original method
        event_dispatcher._task_manager.get_active_tasks_snapshot = original_get_active_tasks

    @pytest.mark.asyncio
    async def test_no_asyncio_sleep_in_backpressure(self, event_dispatcher: EventDispatcher) -> None:
        """Test that backpressure no longer uses asyncio.sleep()."""
        # This test ensures we've eliminated the naive sleep pattern
        
        # Add listeners to trigger backpressure
        async def test_listener(event_data: dict) -> None:
            await asyncio.sleep(0.1)
            
        event_dispatcher.add_listener("test.*", test_listener)
        
        # Fill up task slots
        for i in range(3):  # More than max_concurrent_listener_tasks (2)
            asyncio.create_task(
                event_dispatcher.emit_event(f"test.event_{i}", {"data": i})
            )
        
        # The key test: backpressure should use asyncio.wait(), not asyncio.sleep()
        # We can't easily test this directly, but we can verify the behavior is correct
        
        # If using proper backpressure, this should complete quickly once tasks finish
        start_time = asyncio.get_event_loop().time()
        await event_dispatcher.emit_event("test.final", {"data": "final"})
        end_time = asyncio.get_event_loop().time()
        
        # Should complete in reasonable time (not blocked by fixed sleep)
        assert end_time - start_time < 1.0  # Much less than old 0.05 * many_iterations
