"""
Tests for advanced LiteLLM features (S5.5).

This module tests the enhanced LiteLLM features including streaming,
multi-provider fallback, cost tracking, and observability.
"""

import json
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory
from plugginger.plugins.core.llm_provider.services.litellm_observability import (
    LiteLLMObservability,
    LLMUsageMetrics,
    observability,
)
from plugginger.plugins.core.llm_provider.services.litellm_provider import LiteLLMProvider


class TestLiteLLMAdvancedFeatures:
    """Test advanced LiteLLM features from S5.5."""

    def test_json_response_format_detection(self) -> None:
        """Test JSON response format detection for different providers."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            # OpenAI should support JSON mode
            provider = LiteLLMProvider(provider="openai", model="gpt-4")
            json_format = provider._get_json_response_format()
            assert json_format == {"type": "json_object"}

            # Anthropic should support JSON mode
            provider = LiteLLMProvider(provider="anthropic", model="claude-3-sonnet")
            json_format = provider._get_json_response_format()
            assert json_format == {"type": "json_object"}

            # Groq should support JSON mode
            provider = LiteLLMProvider(provider="groq", model="llama-3.1-70b")
            json_format = provider._get_json_response_format()
            assert json_format == {"type": "json_object"}

            # Unknown provider should not support JSON mode
            provider = LiteLLMProvider(provider="unknown", model="unknown")
            json_format = provider._get_json_response_format()
            assert json_format is None

    def test_streaming_support_detection(self) -> None:
        """Test streaming support detection for different providers."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            # Major providers should support streaming
            streaming_providers = ["openai", "anthropic", "gemini", "groq", "cohere"]

            for provider_name in streaming_providers:
                provider = LiteLLMProvider(provider=provider_name, model="test-model")
                assert provider._supports_streaming() is True

            # Unknown provider should not support streaming
            provider = LiteLLMProvider(provider="unknown", model="unknown")
            assert provider._supports_streaming() is False

    @pytest.mark.asyncio
    async def test_streaming_response_handling(self) -> None:
        """Test streaming response handling."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider(provider="openai", model="gpt-4")

            # Mock streaming chunks
            mock_chunks = [
                MagicMock(choices=[MagicMock(delta=MagicMock(content="Hello"))]),
                MagicMock(choices=[MagicMock(delta=MagicMock(content=" world"))]),
                MagicMock(choices=[MagicMock(delta=MagicMock(content="!"))])
            ]

            async def mock_completion(**kwargs):
                for chunk in mock_chunks:
                    yield chunk

            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.completion', mock_completion):
                response = await provider._handle_streaming_response({"model": "gpt-4", "stream": True})

                assert response.choices[0].message.content == "Hello world!"
                assert response.model == "gpt-4"

    @pytest.mark.asyncio
    async def test_generate_structured_with_streaming(self) -> None:
        """Test structured generation with streaming enabled."""
        valid_json = '{"plugin_name": "test", "services": []}'

        # Create mock streaming response
        mock_chunk = MagicMock()
        mock_chunk.choices = [MagicMock()]
        mock_chunk.choices[0].delta.content = valid_json
        mock_chunk.model = "gpt-4"
        mock_chunk.usage = MagicMock()
        mock_chunk.usage.total_tokens = 200

        async def mock_stream():
            yield mock_chunk

        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.completion') as mock_completion:
                mock_completion.return_value = mock_stream()

                provider = LiteLLMProvider(provider="openai", model="gpt-4")
                result = await provider.generate_structured(
                    "System message",
                    "User message",
                    "EBNF grammar",
                    enable_streaming=True
                )

                assert result["success"] is True
                assert result["validated"] is True
                assert result["content"] == valid_json

    @pytest.mark.asyncio
    async def test_multi_provider_fallback(self) -> None:
        """Test multi-provider fallback functionality."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            # Mock primary provider failure
            primary_provider = LiteLLMProvider(provider="openai", model="gpt-4")

            # Mock fallback success
            valid_json = '{"plugin_name": "fallback_test", "services": []}'

            with patch.object(primary_provider, 'generate_structured') as mock_primary:
                mock_primary.return_value = {
                    "success": False,
                    "error": "Primary provider failed"
                }

                with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.os.getenv') as mock_getenv:
                    mock_getenv.return_value = "test-key"

                    with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.completion') as mock_completion:
                        mock_response = MagicMock()
                        mock_response.choices = [MagicMock()]
                        mock_response.choices[0].message.content = valid_json
                        mock_response.model = "groq-model"
                        mock_response.usage.total_tokens = 150
                        mock_completion.return_value = mock_response

                        result = await primary_provider.generate_with_fallback(
                            "System message",
                            "User message",
                            "EBNF grammar",
                            fallback_providers=["groq"]
                        )

                        assert result["success"] is True
                        assert result["fallback_used"] is True
                        assert result["provider_used"] == "groq"
                        assert result["primary_provider"] == "openai"

    def test_factory_create_with_fallback(self) -> None:
        """Test factory creation with fallback configuration."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch.dict('os.environ', {"OPENAI_API_KEY": "test-key"}, clear=True):
                provider = LiteLLMProviderFactory.create_with_fallback(
                    primary_provider="openai",
                    fallback_providers=["groq", "anthropic"]
                )

                assert provider.provider == "openai"
                assert hasattr(provider, 'fallback_providers')
                assert provider.fallback_providers == ["groq", "anthropic"]

    def test_factory_get_available_providers(self) -> None:
        """Test getting available providers based on environment."""
        with patch.dict('os.environ', {
            "OPENAI_API_KEY": "sk-real-key",
            "GOOGLE_API_KEY": "google-key",
            "ANTHROPIC_API_KEY": "sk-test-invalid"  # Should be ignored
        }, clear=True):
            availability = LiteLLMProviderFactory.get_available_providers()

            assert availability["openai"] is True  # Real key
            assert availability["gemini"] is True  # Google key
            assert availability["ollama"] is True  # No key needed
            # Anthropic should be False due to test key pattern

    def test_factory_get_optimal_provider(self) -> None:
        """Test optimal provider selection based on requirements."""
        with patch.object(LiteLLMProviderFactory, 'get_available_providers') as mock_available:
            mock_available.return_value = {
                "openai": True,
                "groq": True,
                "anthropic": False,
                "ollama": True
            }

            # Test speed priority
            optimal = LiteLLMProviderFactory.get_optimal_provider({"priority": "speed"})
            assert optimal == "groq"  # Groq is fastest available

            # Test cost priority
            optimal = LiteLLMProviderFactory.get_optimal_provider({"priority": "cost"})
            assert optimal == "ollama"  # Ollama is cheapest (free)

            # Test quality priority
            optimal = LiteLLMProviderFactory.get_optimal_provider({"priority": "quality"})
            assert optimal == "openai"  # OpenAI is highest quality available

    def test_factory_create_load_balanced(self) -> None:
        """Test creating load-balanced providers."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch.dict('os.environ', {
                "OPENAI_API_KEY": "openai-key",
                "GROQ_API_KEY": "groq-key"
            }, clear=True):
                providers = LiteLLMProviderFactory.create_load_balanced(
                    providers=["openai", "groq"],
                    strategy="round_robin"
                )

                assert len(providers) == 2
                assert providers[0].provider == "openai"
                assert providers[1].provider == "groq"


class TestLiteLLMObservability:
    """Test LiteLLM observability and cost tracking."""

    def test_usage_metrics_creation(self) -> None:
        """Test creation of usage metrics."""
        metrics = LLMUsageMetrics(
            provider="openai",
            model="gpt-4",
            prompt_tokens=100,
            completion_tokens=50,
            total_tokens=150,
            response_time_ms=1500.0,
            estimated_cost_usd=0.009
        )

        assert metrics.provider == "openai"
        assert metrics.model == "gpt-4"
        assert metrics.total_tokens == 150
        assert metrics.estimated_cost_usd == 0.009
        assert metrics.success is True

    def test_observability_request_tracking(self) -> None:
        """Test request tracking in observability."""
        obs = LiteLLMObservability()

        # Start tracking
        request_id = obs.start_request("openai", "gpt-4", "completion")
        assert request_id.startswith("openai_gpt-4")
        assert len(obs.metrics) == 1

        # End tracking
        response_data = {
            "tokens_used": 150,
            "success": True
        }
        obs.end_request(request_id, response_data, **********.0, success=True)

        # Check metrics
        metrics = obs.metrics[0]
        assert metrics.provider == "openai"
        assert metrics.model == "gpt-4"
        assert metrics.total_tokens == 150
        assert metrics.success is True

    def test_cost_calculation(self) -> None:
        """Test cost calculation for different providers."""
        obs = LiteLLMObservability()

        # Test OpenAI GPT-4 cost
        cost = obs._calculate_cost("openai", "gpt-4", 1000, 500)
        expected_cost = (1000 / 1000) * 0.03 + (500 / 1000) * 0.06  # $0.06
        assert abs(cost - expected_cost) < 0.001

        # Test Ollama (free)
        cost = obs._calculate_cost("ollama", "llama3.2", 1000, 500)
        assert cost == 0.0

        # Test unknown provider
        cost = obs._calculate_cost("unknown", "unknown", 1000, 500)
        assert cost == 0.0

    def test_session_summary(self) -> None:
        """Test session summary generation."""
        obs = LiteLLMObservability()

        # Add some test metrics
        obs.metrics = [
            LLMUsageMetrics(
                provider="openai", model="gpt-4", total_tokens=150,
                response_time_ms=1500.0, estimated_cost_usd=0.009, success=True
            ),
            LLMUsageMetrics(
                provider="groq", model="llama-3.1-70b", total_tokens=200,
                response_time_ms=800.0, estimated_cost_usd=0.001, success=True
            ),
            LLMUsageMetrics(
                provider="openai", model="gpt-4", total_tokens=100,
                response_time_ms=2000.0, estimated_cost_usd=0.006, success=False
            )
        ]

        summary = obs.get_session_summary()

        assert summary["total_requests"] == 3
        assert summary["successful_requests"] == 2
        assert summary["total_cost_usd"] == 0.016
        assert summary["total_tokens"] == 450
        assert summary["success_rate"] == 66.67
        assert "openai" in summary["providers_used"]
        assert "groq" in summary["providers_used"]

    def test_provider_breakdown(self) -> None:
        """Test provider breakdown metrics."""
        obs = LiteLLMObservability()

        # Add test metrics
        obs.metrics = [
            LLMUsageMetrics(provider="openai", model="gpt-4", total_tokens=150, estimated_cost_usd=0.009, success=True),
            LLMUsageMetrics(provider="openai", model="gpt-4", total_tokens=100, estimated_cost_usd=0.006, success=False),
            LLMUsageMetrics(provider="groq", model="llama-3.1-70b", total_tokens=200, estimated_cost_usd=0.001, success=True)
        ]

        breakdown = obs.get_provider_breakdown()

        assert "openai" in breakdown
        assert "groq" in breakdown

        openai_data = breakdown["openai"]
        assert openai_data["requests"] == 2
        assert openai_data["successful_requests"] == 1
        assert openai_data["success_rate"] == 50.0
        assert openai_data["total_cost_usd"] == 0.015

        groq_data = breakdown["groq"]
        assert groq_data["requests"] == 1
        assert groq_data["successful_requests"] == 1
        assert groq_data["success_rate"] == 100.0

    def test_metrics_export(self) -> None:
        """Test metrics export in different formats."""
        obs = LiteLLMObservability()

        # Add test metric
        obs.metrics = [
            LLMUsageMetrics(
                provider="openai", model="gpt-4", total_tokens=150,
                estimated_cost_usd=0.009, success=True
            )
        ]

        # Test dict export
        dict_export = obs.export_metrics("dict")
        assert len(dict_export) == 1
        assert dict_export[0]["provider"] == "openai"
        assert dict_export[0]["total_tokens"] == 150

        # Test JSON export
        json_export = obs.export_metrics("json")
        parsed = json.loads(json_export)
        assert len(parsed) == 1
        assert parsed[0]["provider"] == "openai"

        # Test CSV export
        csv_export = obs.export_metrics("csv")
        assert "provider,model,request_type" in csv_export
        assert "openai,gpt-4" in csv_export

    def test_global_observability_instance(self) -> None:
        """Test that global observability instance works correctly."""
        # Clear any existing metrics
        observability.clear_metrics()

        # Should start with empty metrics
        assert len(observability.metrics) == 0

        # Should be able to track requests
        observability.start_request("test", "model", "completion")
        assert len(observability.metrics) == 1

        # Clear should work
        observability.clear_metrics()
        assert len(observability.metrics) == 0
