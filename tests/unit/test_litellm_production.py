"""
Tests for LiteLLM production features (S5.6).

This module tests the production-ready features including rate limiting,
circuit breakers, health monitoring, and security features.
"""

import asyncio

import pytest

from plugginger.plugins.core.llm_provider.services.litellm_production import (
    CircuitBreaker,
    CircuitBreakerState,
    ProviderHealthMonitor,
    RateLimiter,
    SecurityManager,
)


class TestRateLimiter:
    """Test rate limiting functionality."""

    @pytest.mark.asyncio
    async def test_rate_limiter_basic_functionality(self) -> None:
        """Test basic rate limiting functionality."""
        # Create rate limiter with 2 requests per minute
        limiter = RateLimiter(requests_per_minute=2)

        # Should allow first request
        assert await limiter.acquire() is True

        # Should allow second request
        assert await limiter.acquire() is True

        # Should deny third request (rate limited)
        assert await limiter.acquire() is False

    @pytest.mark.asyncio
    async def test_rate_limiter_token_refill(self) -> None:
        """Test token refill over time."""
        # Create rate limiter with 120 requests per minute (2 per second)
        limiter = RateLimiter(requests_per_minute=120)

        # Use up tokens
        assert await limiter.acquire() is True
        assert await limiter.acquire() is True

        # Should be rate limited now
        assert await limiter.acquire() is False

        # Wait a bit for token refill (real time)
        import asyncio
        await asyncio.sleep(0.6)  # Wait 0.6 seconds for refill

        # Should have refilled at least one token
        assert await limiter.acquire() is True


class TestCircuitBreaker:
    """Test circuit breaker functionality."""

    @pytest.mark.asyncio
    async def test_circuit_breaker_closed_state(self) -> None:
        """Test circuit breaker in closed state."""
        breaker = CircuitBreaker(failure_threshold=2)

        async def successful_function() -> str:
            return "success"

        # Should allow calls in closed state
        result = await breaker.call(successful_function)
        assert result == "success"
        assert breaker.state == CircuitBreakerState.CLOSED

    @pytest.mark.asyncio
    async def test_circuit_breaker_opens_on_failures(self) -> None:
        """Test circuit breaker opens after threshold failures."""
        breaker = CircuitBreaker(failure_threshold=2)

        async def failing_function() -> None:
            raise Exception("Test failure")

        # First failure
        with pytest.raises(Exception):
            await breaker.call(failing_function)
        assert breaker.state == CircuitBreakerState.CLOSED

        # Second failure should open circuit
        with pytest.raises(Exception):
            await breaker.call(failing_function)
        assert breaker.state == CircuitBreakerState.OPEN

    @pytest.mark.asyncio
    async def test_circuit_breaker_blocks_when_open(self) -> None:
        """Test circuit breaker blocks calls when open."""
        breaker = CircuitBreaker(failure_threshold=1, recovery_timeout=60.0)

        async def failing_function() -> None:
            raise Exception("Test failure")

        # Trigger failure to open circuit
        with pytest.raises(Exception):
            await breaker.call(failing_function)
        assert breaker.state == CircuitBreakerState.OPEN

        # Should block subsequent calls
        with pytest.raises(Exception, match="Circuit breaker is OPEN"):
            await breaker.call(failing_function)

    @pytest.mark.asyncio
    async def test_circuit_breaker_half_open_recovery(self) -> None:
        """Test circuit breaker half-open recovery."""
        breaker = CircuitBreaker(failure_threshold=1, recovery_timeout=0.1, success_threshold=2)

        async def failing_function() -> None:
            raise Exception("Test failure")

        async def successful_function() -> str:
            return "success"

        # Open the circuit
        with pytest.raises(Exception):
            await breaker.call(failing_function)
        assert breaker.state == CircuitBreakerState.OPEN

        # Wait for recovery timeout
        await asyncio.sleep(0.2)

        # Should transition to half-open and allow test call
        result = await breaker.call(successful_function)
        assert result == "success"
        assert breaker.state == CircuitBreakerState.HALF_OPEN

        # Another success should close the circuit
        result = await breaker.call(successful_function)
        assert result == "success"
        assert breaker.state == CircuitBreakerState.CLOSED


class TestProviderHealthMonitor:
    """Test provider health monitoring."""

    def test_provider_health_initialization(self) -> None:
        """Test provider health initialization."""
        monitor = ProviderHealthMonitor()

        health = monitor.get_health("openai", "gpt-4")
        assert health.provider == "openai"
        assert health.model == "gpt-4"
        assert health.is_healthy is True
        assert health.consecutive_failures == 0

    @pytest.mark.asyncio
    async def test_health_monitoring_success(self) -> None:
        """Test health monitoring for successful requests."""
        monitor = ProviderHealthMonitor()

        start_time = await monitor.record_request_start("openai", "gpt-4")
        await asyncio.sleep(0.01)  # Simulate request time
        await monitor.record_request_success("openai", "gpt-4", start_time)

        health = monitor.get_health("openai", "gpt-4")
        assert health.is_healthy is True
        assert health.total_requests == 1
        assert health.successful_requests == 1
        assert health.consecutive_failures == 0
        assert health.avg_response_time_ms > 0

    @pytest.mark.asyncio
    async def test_health_monitoring_failure(self) -> None:
        """Test health monitoring for failed requests."""
        monitor = ProviderHealthMonitor()

        # Record multiple failures
        for _ in range(3):
            await monitor.record_request_start("openai", "gpt-4")
            await monitor.record_request_failure("openai", "gpt-4")

        health = monitor.get_health("openai", "gpt-4")
        assert health.is_healthy is False
        assert health.consecutive_failures == 3
        assert health.last_failure is not None

    def test_get_healthy_providers(self) -> None:
        """Test getting list of healthy providers."""
        monitor = ProviderHealthMonitor()

        # Create healthy provider
        health1 = monitor.get_health("openai", "gpt-4")
        health1.is_healthy = True

        # Create unhealthy provider
        health2 = monitor.get_health("anthropic", "claude-3")
        health2.is_healthy = False

        healthy_providers = monitor.get_healthy_providers()
        assert "openai" in healthy_providers
        assert "anthropic" not in healthy_providers

    def test_provider_stats(self) -> None:
        """Test provider statistics generation."""
        monitor = ProviderHealthMonitor()

        # Set up test data
        health = monitor.get_health("openai", "gpt-4")
        health.total_requests = 10
        health.successful_requests = 8
        health.avg_response_time_ms = 150.5

        stats = monitor.get_provider_stats()

        assert "openai:gpt-4" in stats
        provider_stats = stats["openai:gpt-4"]
        assert provider_stats["success_rate"] == 80.0
        assert provider_stats["avg_response_time_ms"] == 150.5
        assert provider_stats["total_requests"] == 10


class TestSecurityManager:
    """Test security features."""

    def test_api_key_sanitization(self) -> None:
        """Test API key sanitization for logging."""
        # Test normal key
        test_key = "sk-**********abcdef**********abcdef"
        sanitized = SecurityManager.sanitize_api_key(test_key)
        expected = test_key[:4] + "*" * (len(test_key) - 8) + test_key[-4:]
        assert sanitized == expected

        # Test short key
        sanitized = SecurityManager.sanitize_api_key("short")
        assert sanitized == "*****"

        # Test empty key
        sanitized = SecurityManager.sanitize_api_key("")
        assert sanitized == "None"

    def test_api_key_format_validation(self) -> None:
        """Test API key format validation."""
        # Valid OpenAI key
        assert SecurityManager.validate_api_key_format("openai", "sk-**********abcdef**********abcdef") is True

        # Invalid OpenAI key
        assert SecurityManager.validate_api_key_format("openai", "invalid-key") is False

        # Valid Anthropic key
        assert SecurityManager.validate_api_key_format("anthropic", "sk-ant-**********abcdef**********abcdef") is True

        # Valid Groq key
        assert SecurityManager.validate_api_key_format("groq", "gsk_**********abcdef**********abcdef") is True

        # Generic provider with sufficient length
        assert SecurityManager.validate_api_key_format("unknown", "**********abcdef") is True

        # Too short key
        assert SecurityManager.validate_api_key_format("unknown", "short") is False

    def test_test_key_detection(self) -> None:
        """Test detection of test/invalid API keys."""
        # Test keys
        assert SecurityManager.is_test_key("sk-test-**********") is True
        assert SecurityManager.is_test_key("test-key") is True
        assert SecurityManager.is_test_key("demo-key") is True
        assert SecurityManager.is_test_key("your-api-key") is True
        assert SecurityManager.is_test_key("placeholder") is True

        # Valid keys
        assert SecurityManager.is_test_key("sk-**********abcdef") is False
        assert SecurityManager.is_test_key("gsk_real_key_here") is False

        # Empty key
        assert SecurityManager.is_test_key("") is True


class TestProductionIntegration:
    """Test integration of production features."""

    def test_rate_limiter_creation(self) -> None:
        """Test rate limiter creation and caching."""
        monitor = ProviderHealthMonitor()

        # Should create new rate limiter
        limiter1 = monitor.get_rate_limiter("openai", 60)
        assert limiter1.requests_per_minute == 60

        # Should return same instance
        limiter2 = monitor.get_rate_limiter("openai", 120)  # Different rate ignored
        assert limiter1 is limiter2

    def test_circuit_breaker_creation(self) -> None:
        """Test circuit breaker creation and caching."""
        monitor = ProviderHealthMonitor()

        # Should create new circuit breaker
        breaker1 = monitor.get_circuit_breaker("openai", "gpt-4")
        assert breaker1.state == CircuitBreakerState.CLOSED

        # Should return same instance
        breaker2 = monitor.get_circuit_breaker("openai", "gpt-4")
        assert breaker1 is breaker2

        # Different provider/model should create different instance
        breaker3 = monitor.get_circuit_breaker("anthropic", "claude-3")
        assert breaker1 is not breaker3

    def test_provider_key_generation(self) -> None:
        """Test provider key generation for uniqueness."""
        monitor = ProviderHealthMonitor()

        key1 = monitor.get_provider_key("openai", "gpt-4")
        key2 = monitor.get_provider_key("openai", "gpt-3.5")
        key3 = monitor.get_provider_key("anthropic", "claude-3")

        assert key1 == "openai:gpt-4"
        assert key2 == "openai:gpt-3.5"
        assert key3 == "anthropic:claude-3"

        # All should be unique
        assert len({key1, key2, key3}) == 3
