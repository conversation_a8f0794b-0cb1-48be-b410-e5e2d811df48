"""
Integration tests for LiteLLM provider implementation.

These tests verify that the LiteLLM provider works correctly with
real provider configurations and environment variables.
"""

import json
import os
import pytest
from unittest.mock import patch

from plugginger.core.exceptions import PluggingerConfigurationError
from plugginger.plugins.core.llm_provider.services.litellm_provider import Li<PERSON><PERSON><PERSON>rovider
from plugginger.plugins.core.llm_provider.services.litellm_factory import LiteLLMProviderFactory


class TestLiteLLMIntegration:
    """Integration tests for LiteLLM provider."""

    def test_provider_factory_auto_detection_priority(self) -> None:
        """Test that provider auto-detection follows correct priority order."""
        # Clear environment
        with patch.dict(os.environ, {}, clear=True):
            # Set multiple provider keys to test priority
            os.environ["GOOGLE_API_KEY"] = "google-key"
            os.environ["OPENAI_API_KEY"] = "sk-real-openai-key"  # Not a test key
            
            detected = LiteLLMProviderFactory._auto_detect_provider()
            
            # OpenAI should have higher priority than Google
            assert detected is not None
            assert detected["provider"] == "openai"
            assert detected["api_key"] == "sk-real-openai-key"

    def test_provider_factory_test_key_skipping(self) -> None:
        """Test that factory correctly skips test API keys."""
        with patch.dict(os.environ, {}, clear=True):
            # Set test key for OpenAI and real key for Google
            os.environ["OPENAI_API_KEY"] = "sk-test-invalid-key"
            os.environ["GOOGLE_API_KEY"] = "real-google-key"
            
            detected = LiteLLMProviderFactory._auto_detect_provider()
            
            # Should skip OpenAI test key and detect Google
            assert detected is not None
            assert detected["provider"] == "gemini"
            assert detected["api_key"] == "real-google-key"

    def test_provider_factory_ollama_fallback(self) -> None:
        """Test that Ollama is detected as fallback when no API keys available."""
        with patch.dict(os.environ, {}, clear=True):
            detected = LiteLLMProviderFactory._auto_detect_provider()
            
            # Should fallback to Ollama (no API key required)
            assert detected is not None
            assert detected["provider"] == "ollama"
            assert detected["base_url"] == "http://localhost:11434"

    def test_provider_creation_with_all_parameters(self) -> None:
        """Test provider creation with all configuration parameters."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProviderFactory.create_provider(
                provider="openai",
                model="gpt-4",
                api_key="test-api-key",
                base_url="https://custom.openai.com/v1",
                temperature=0.5,
                max_tokens=1000
            )
            
            assert provider.provider == "openai"
            assert provider.model == "gpt-4"
            assert provider.api_key == "test-api-key"
            assert provider.base_url == "https://custom.openai.com/v1"
            assert provider.config["temperature"] == 0.5
            assert provider.config["max_tokens"] == 1000

    def test_provider_info_completeness(self) -> None:
        """Test that provider info contains all required fields."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider(
                provider="openai",
                model="gpt-4",
                api_key="test-key"
            )
            
            info = provider.get_info()
            
            # Check all required fields
            required_fields = [
                "provider", "model", "base_url", "has_api_key", 
                "litellm_available", "supported_features"
            ]
            for field in required_fields:
                assert field in info
            
            # Check supported features
            expected_features = [
                "text_generation", "structured_generation", 
                "streaming", "cost_tracking", "fallback_logic"
            ]
            for feature in expected_features:
                assert feature in info["supported_features"]

    def test_model_name_formatting_edge_cases(self) -> None:
        """Test model name formatting for various edge cases."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            # Test with complex model names
            provider = LiteLLMProvider(provider="huggingface", model="microsoft/DialoGPT-large")
            assert provider._format_model_name() == "huggingface/microsoft/DialoGPT-large"
            
            # Test with already formatted complex name
            provider = LiteLLMProvider(provider="huggingface", model="huggingface/microsoft/DialoGPT-large")
            assert provider._format_model_name() == "huggingface/microsoft/DialoGPT-large"
            
            # Test with provider-specific model format
            provider = LiteLLMProvider(provider="ollama", model="llama3.2:8b")
            assert provider._format_model_name() == "ollama/llama3.2:8b"

    def test_environment_variable_configuration_precedence(self) -> None:
        """Test environment variable configuration precedence."""
        with patch.dict(os.environ, {}, clear=True):
            # Set multiple environment variables for same provider
            os.environ["OPENAI_API_KEY"] = "env-key"
            os.environ["PLUGGINGER_LLM_API_KEY"] = "plugginger-key"
            os.environ["OPENAI_MODEL"] = "env-model"
            os.environ["PLUGGINGER_LLM_MODEL"] = "plugginger-model"
            
            with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
                provider = LiteLLMProviderFactory.create_from_env(provider_type="openai")
                
                # Should use first available environment variable
                assert provider.api_key == "env-key"  # OPENAI_API_KEY has precedence
                # Model should come from environment detection

    def test_provider_factory_unknown_provider_handling(self) -> None:
        """Test factory handling of unknown providers."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            # Should not raise error for unknown provider
            provider = LiteLLMProviderFactory.create_provider(
                provider="unknown_provider",
                model="unknown_model",
                api_key="test-key"
            )
            
            assert provider.provider == "unknown_provider"
            assert provider.model == "unknown_model"
            assert provider.api_key == "test-key"

    def test_multiple_provider_configurations(self) -> None:
        """Test handling multiple provider configurations simultaneously."""
        providers = []
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            # Create multiple providers
            providers.append(LiteLLMProviderFactory.create_provider(
                provider="openai", model="gpt-4", api_key="openai-key"
            ))
            providers.append(LiteLLMProviderFactory.create_provider(
                provider="anthropic", model="claude-3-sonnet", api_key="anthropic-key"
            ))
            providers.append(LiteLLMProviderFactory.create_provider(
                provider="ollama", model="llama3.2", base_url="http://localhost:11434"
            ))
            
            # Verify each provider is configured correctly
            assert providers[0].provider == "openai"
            assert providers[0].model == "gpt-4"
            assert providers[1].provider == "anthropic"
            assert providers[1].model == "claude-3-sonnet"
            assert providers[2].provider == "ollama"
            assert providers[2].model == "llama3.2"

    @pytest.mark.asyncio
    async def test_mock_generation_consistency(self) -> None:
        """Test that mock generation provides consistent responses."""
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            provider = LiteLLMProvider.__new__(LiteLLMProvider)  # Bypass __init__
            provider.provider = "mock"
            provider.model = "test-model"
            
            # Test text generation
            result1 = await provider.generate_text("Test prompt")
            result2 = await provider.generate_text("Test prompt")
            
            assert result1["success"] is True
            assert result2["success"] is True
            assert result1["provider"] == "mock"
            assert result2["provider"] == "mock"
            
            # Test structured generation
            structured_result = await provider.generate_structured(
                "System", "User", "Grammar"
            )
            
            assert structured_result["success"] is True
            assert structured_result["validated"] is True
            # Should be valid JSON
            json.loads(structured_result["content"])

    def test_provider_factory_comprehensive_provider_support(self) -> None:
        """Test that factory supports comprehensive list of providers."""
        supported_providers = LiteLLMProviderFactory.list_supported_providers()
        
        # Check for major providers
        major_providers = [
            "openai", "anthropic", "gemini", "google", "ollama",
            "groq", "cohere", "together", "replicate", "huggingface",
            "deepseek", "mistral", "perplexity"
        ]
        
        for provider in major_providers:
            assert provider in supported_providers, f"Provider {provider} not supported"
        
        # Should have at least 10 providers
        assert len(supported_providers) >= 10

    def test_provider_configuration_validation(self) -> None:
        """Test provider configuration validation."""
        # Test that each supported provider has proper configuration
        for provider_name in LiteLLMProviderFactory.list_supported_providers():
            config = LiteLLMProviderFactory.get_provider_info(provider_name)
            
            assert config is not None, f"No configuration for provider {provider_name}"
            assert "api_key" in config, f"No api_key config for {provider_name}"
            assert "model" in config, f"No model config for {provider_name}"
            assert "default_model" in config, f"No default_model for {provider_name}"
            
            # API key should be a list of environment variable names
            assert isinstance(config["api_key"], list), f"api_key should be list for {provider_name}"
            
            # Default model should be a string
            assert isinstance(config["default_model"], str), f"default_model should be string for {provider_name}"

    def test_backward_compatibility_imports(self) -> None:
        """Test that backward compatibility imports work correctly."""
        # Test that new LiteLLM classes can be imported
        from plugginger.plugins.core.llm_provider.services import LiteLLMProvider, LiteLLMProviderFactory
        
        assert LiteLLMProvider is not None
        assert LiteLLMProviderFactory is not None
        
        # Test that legacy classes can still be imported (if available)
        try:
            from plugginger.plugins.core.llm_provider.services import LLMProvider, ProviderFactory
            # If import succeeds, they should not be None
            if LLMProvider is not None:
                assert hasattr(LLMProvider, '__name__')
            if ProviderFactory is not None:
                assert hasattr(ProviderFactory, '__name__')
        except ImportError:
            # Legacy providers may not be available (removed in S5.6)
            pass

    def test_litellm_availability_detection(self) -> None:
        """Test LiteLLM availability detection."""
        # Test with LiteLLM available
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            provider = LiteLLMProvider(provider="openai", model="gpt-4")
            info = provider.get_info()
            assert info["litellm_available"] is True
        
        # Test with LiteLLM not available
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            with pytest.raises(PluggingerConfigurationError):
                LiteLLMProvider(provider="openai", model="gpt-4")
