"""
Integration tests for LiteLLM migration (S5.6).

This module tests the complete migration from legacy providers to LiteLLM,
including backward compatibility and production readiness.
"""

import pytest
from unittest.mock import patch

from plugginger.core.exceptions import PluggingerConfigurationError
from plugginger.plugins.core.llm_provider.services import LiteLLMProvider


class TestLegacyMigration:
    """Test migration from legacy providers to LiteLLM."""

    def test_legacy_imports_work(self) -> None:
        """Test that legacy imports still work after migration."""
        # Should be able to import legacy names
        from plugginger.plugins.core.llm_provider.services import LLMProvider, ProviderFactory
        
        # Legacy names should point to LiteLLM implementations
        from plugginger.plugins.core.llm_provider.services import LiteLLMProvider, LiteLLMProviderFactory
        
        assert LLMProvider is LiteLLMProvider
        assert ProviderFactory is LiteLLMProviderFactory

    def test_legacy_provider_service_removed(self) -> None:
        """Test that legacy provider_service.py file is removed."""
        # Should not be able to import from the old file directly
        with pytest.raises(ImportError):
            from plugginger.plugins.core.llm_provider.services.provider_service import OpenAIProvider

    def test_backward_compatibility_factory(self) -> None:
        """Test backward compatibility of factory methods."""
        from plugginger.plugins.core.llm_provider.services import ProviderFactory
        
        # Legacy factory methods should work
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            with patch.dict('os.environ', {"OPENAI_API_KEY": "sk-test-key"}, clear=True):
                # Should work with legacy interface
                provider = ProviderFactory.create_from_env()
                assert provider is not None
                assert hasattr(provider, 'provider')
                assert hasattr(provider, 'model')

    def test_enhanced_services_available(self) -> None:
        """Test that enhanced services are available after migration."""
        # Should be able to import all new services
        from plugginger.plugins.core.llm_provider.services import (
            LiteLLMProvider,
            LiteLLMProviderFactory,
            LiteLLMObservability,
            observability,
            ProviderHealthMonitor,
            RateLimiter,
            CircuitBreaker,
            SecurityManager,
            health_monitor,
            security_manager
        )
        
        # All should be available
        assert LiteLLMProvider is not None
        assert LiteLLMProviderFactory is not None
        assert LiteLLMObservability is not None
        assert observability is not None
        assert ProviderHealthMonitor is not None
        assert RateLimiter is not None
        assert CircuitBreaker is not None
        assert SecurityManager is not None
        assert health_monitor is not None
        assert security_manager is not None


class TestProductionReadiness:
    """Test production readiness features."""

    def test_security_integration(self) -> None:
        """Test security features integration."""
        from plugginger.plugins.core.llm_provider.services import security_manager
        
        # Test API key validation
        assert security_manager.validate_api_key_format("openai", "sk-1234567890abcdef1234567890abcdef") is True
        assert security_manager.is_test_key("sk-test-invalid") is True
        
        # Test key sanitization
        sanitized = security_manager.sanitize_api_key("sk-1234567890abcdef1234567890abcdef")
        assert "****" in sanitized
        assert len(sanitized) == len("sk-1234567890abcdef1234567890abcdef")

    def test_health_monitoring_integration(self) -> None:
        """Test health monitoring integration."""
        from plugginger.plugins.core.llm_provider.services import health_monitor
        
        # Should be able to get health status
        health = health_monitor.get_health("openai", "gpt-4")
        assert health.provider == "openai"
        assert health.model == "gpt-4"
        assert health.is_healthy is True
        
        # Should be able to get provider stats
        stats = health_monitor.get_provider_stats()
        assert isinstance(stats, dict)

    @pytest.mark.asyncio
    async def test_rate_limiting_integration(self) -> None:
        """Test rate limiting integration."""
        from plugginger.plugins.core.llm_provider.services import health_monitor
        
        # Should be able to get rate limiter
        rate_limiter = health_monitor.get_rate_limiter("openai", 60)
        
        # Should be able to acquire tokens
        can_proceed = await rate_limiter.acquire()
        assert isinstance(can_proceed, bool)

    def test_observability_integration(self) -> None:
        """Test observability integration."""
        from plugginger.plugins.core.llm_provider.services import observability
        
        # Should be able to start tracking
        request_id = observability.start_request("openai", "gpt-4", "completion")
        assert isinstance(request_id, str)
        assert "openai" in request_id
        
        # Should be able to get session summary
        summary = observability.get_session_summary()
        assert "total_requests" in summary
        assert "total_cost_usd" in summary

    @pytest.mark.asyncio
    async def test_provider_with_production_features(self) -> None:
        """Test provider with all production features enabled."""
        from plugginger.plugins.core.llm_provider.services import LiteLLMProvider
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            # Create provider (will use mock mode)
            provider = LiteLLMProvider.__new__(LiteLLMProvider)
            provider.provider = "openai"
            provider.model = "gpt-4"
            provider.api_key = "sk-test-key"
            provider.base_url = None
            provider.config = {}
            
            # Should work with production features
            result = await provider.generate_text("Test prompt")
            
            assert result["success"] is True
            assert result["provider"] == "openai"


class TestCompleteWorkflow:
    """Test complete workflow with migrated system."""

    @pytest.mark.asyncio
    async def test_end_to_end_text_generation(self) -> None:
        """Test end-to-end text generation workflow."""
        from plugginger.plugins.core.llm_provider.services import LiteLLMProviderFactory
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            # Create provider manually for mock mode
            provider = LiteLLMProvider.__new__(LiteLLMProvider)
            provider.provider = "mock"
            provider.model = "test-model"
            provider.api_key = None
            provider.base_url = None
            provider.config = {}
            provider.fallback_providers = None
            
            # Should be able to generate text
            result = await provider.generate_text("Hello world")
            
            assert result["success"] is True
            assert "content" in result
            assert "provider" in result

    @pytest.mark.asyncio
    async def test_end_to_end_structured_generation(self) -> None:
        """Test end-to-end structured generation workflow."""
        from plugginger.plugins.core.llm_provider.services import LiteLLMProviderFactory
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            # Create provider manually for mock mode
            provider = LiteLLMProvider.__new__(LiteLLMProvider)
            provider.provider = "mock"
            provider.model = "test-model"
            provider.api_key = None
            provider.base_url = None
            provider.config = {}
            provider.fallback_providers = None
            
            # Should be able to generate structured output
            result = await provider.generate_structured(
                system_message="You are a helpful assistant",
                user_message="Generate a plugin structure",
                ebnf_grammar="plugin_structure"
            )
            
            assert result["success"] is True
            assert result["validated"] is True
            assert "content" in result

    def test_provider_feature_matrix(self) -> None:
        """Test that all providers support expected features."""
        from plugginger.plugins.core.llm_provider.services import LiteLLMProvider
        
        test_providers = ["openai", "anthropic", "gemini", "groq", "ollama"]
        
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', True):
            for provider_name in test_providers:
                provider = LiteLLMProvider(provider=provider_name, model="test-model")
                info = provider.get_info()
                
                # All providers should support basic features
                assert "text_generation" in info["supported_features"]
                assert "structured_generation" in info["supported_features"]
                assert "multi_provider_fallback" in info["supported_features"]
                assert "cost_tracking" in info["supported_features"]
                assert "retry_logic" in info["supported_features"]

    def test_migration_metrics(self) -> None:
        """Test migration success metrics."""
        # Count of available providers should be much higher than legacy
        from plugginger.plugins.core.llm_provider.services import LiteLLMProviderFactory
        
        supported_providers = LiteLLMProviderFactory.list_supported_providers()
        
        # Should support many more providers than legacy (3)
        assert len(supported_providers) >= 10
        
        # Should include major providers
        major_providers = ["openai", "anthropic", "gemini", "groq", "ollama"]
        for provider in major_providers:
            assert provider in supported_providers

    def test_code_reduction_achieved(self) -> None:
        """Test that code reduction was achieved."""
        # Legacy provider_service.py should be gone
        import os
        legacy_file = "src/plugginger/plugins/core/llm_provider/services/provider_service.py"
        
        # File should not exist
        assert not os.path.exists(legacy_file)
        
        # New LiteLLM files should exist
        litellm_files = [
            "src/plugginger/plugins/core/llm_provider/services/litellm_provider.py",
            "src/plugginger/plugins/core/llm_provider/services/litellm_factory.py",
            "src/plugginger/plugins/core/llm_provider/services/litellm_observability.py",
            "src/plugginger/plugins/core/llm_provider/services/litellm_production.py"
        ]
        
        for file_path in litellm_files:
            assert os.path.exists(file_path), f"LiteLLM file {file_path} should exist"

    def test_performance_improvements(self) -> None:
        """Test that performance improvements are available."""
        from plugginger.plugins.core.llm_provider.services import (
            LiteLLMProviderFactory, observability, health_monitor
        )
        
        # Should support optimal provider selection
        optimal = LiteLLMProviderFactory.get_optimal_provider({"priority": "speed"})
        assert optimal is not None
        
        # Should support load balancing
        with patch('plugginger.plugins.core.llm_provider.services.litellm_provider.LITELLM_AVAILABLE', False):
            with patch.dict('os.environ', {"OPENAI_API_KEY": "test-key"}, clear=True):
                # Should handle mock mode gracefully
                try:
                    providers = LiteLLMProviderFactory.create_load_balanced(["openai"])
                    assert len(providers) >= 1
                except PluggingerConfigurationError:
                    # Expected in mock mode without LiteLLM
                    pass
        
        # Should have observability
        summary = observability.get_session_summary()
        assert "total_requests" in summary
        
        # Should have health monitoring
        stats = health_monitor.get_provider_stats()
        assert isinstance(stats, dict)

    def test_all_exports_available(self) -> None:
        """Test that all expected exports are available."""
        from plugginger.plugins.core.llm_provider.services import __all__
        
        expected_exports = [
            "LiteLLMProvider",
            "LiteLLMProviderFactory",
            "LiteLLMObservability", 
            "observability",
            "ProviderHealthMonitor",
            "RateLimiter",
            "CircuitBreaker", 
            "SecurityManager",
            "health_monitor",
            "security_manager",
            "ResponseValidationService",
            "LLMProvider",  # Legacy alias
            "ProviderFactory"  # Legacy alias
        ]
        
        for export in expected_exports:
            assert export in __all__, f"Export {export} should be available"
