"""
Tests for intelligent plugin generation services.

Tests the new S4.4 intelligent plugin generation capabilities including
prompt processing, spec generation, code templating, wiring integration,
and quality scoring.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock

from plugginger.plugins.internal.plugin_generator.services.prompt_processor import PromptProcessor
from plugginger.plugins.internal.plugin_generator.services.plugin_spec_generator import PluginSpecGenerator
from plugginger.plugins.internal.plugin_generator.services.code_template_engine import CodeTemplateEngine
from plugginger.plugins.internal.plugin_generator.services.wiring_integration import WiringIntegration
from plugginger.plugins.internal.plugin_generator.services.quality_scoring import QualityScoring


class TestPromptProcessor:
    """Test cases for PromptProcessor service."""

    @pytest.fixture
    def processor(self) -> PromptProcessor:
        """Create PromptProcessor instance for testing."""
        mock_app = MagicMock()
        return PromptProcessor(mock_app)

    @pytest.mark.asyncio
    async def test_process_user_prompt_basic(self, processor: PromptProcessor) -> None:
        """Test basic prompt processing."""
        result = await processor.process_user_prompt(
            "Create an email service with authentication"
        )
        
        assert result["success"] is True
        assert "original_prompt" in result
        assert "prompt_analysis" in result
        assert "structured_prompt" in result

    @pytest.mark.asyncio
    async def test_process_user_prompt_with_context(self, processor: PromptProcessor) -> None:
        """Test prompt processing with application context."""
        app_context = {
            "services": ["logger", "config", "auth"],
            "events": ["app.startup", "user.login"]
        }
        
        result = await processor.process_user_prompt(
            "Create an email notification service",
            app_context=app_context
        )
        
        assert result["success"] is True
        assert "integration_suggestions" in result["enhanced_context"]
        assert len(result["enhanced_context"]["integration_suggestions"]) > 0

    @pytest.mark.asyncio
    async def test_prompt_analysis_extraction(self, processor: PromptProcessor) -> None:
        """Test prompt analysis and requirement extraction."""
        result = await processor.process_user_prompt(
            "Create an authentication service with login, logout, and user management"
        )
        
        analysis = result["prompt_analysis"]
        assert analysis["plugin_type"] == "auth"
        assert "login" in str(analysis["service_requirements"])
        assert analysis["complexity_score"] > 0.0

    def test_extract_plugin_type(self, processor: PromptProcessor) -> None:
        """Test plugin type extraction."""
        assert processor._extract_plugin_type("email service") == "service"
        assert processor._extract_plugin_type("authentication system") == "auth"
        assert processor._extract_plugin_type("cache storage") == "storage"
        assert processor._extract_plugin_type("event handler") == "event"

    def test_extract_service_requirements(self, processor: PromptProcessor) -> None:
        """Test service requirement extraction."""
        services = processor._extract_service_requirements("email sending with authentication")
        assert "send_email" in services
        assert "authenticate" in services

    def test_calculate_complexity_score(self, processor: PromptProcessor) -> None:
        """Test complexity score calculation."""
        score = processor._calculate_complexity_score(
            "Create a complex email service with authentication, caching, and logging",
            ["send_email", "authenticate", "cache_data"],
            ["app.startup", "user.login"]
        )
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # Should be complex


class TestPluginSpecGenerator:
    """Test cases for PluginSpecGenerator service."""

    @pytest.fixture
    def generator(self) -> PluginSpecGenerator:
        """Create PluginSpecGenerator instance for testing."""
        mock_app = MagicMock()
        return PluginSpecGenerator(mock_app)

    @pytest.mark.asyncio
    async def test_generate_plugin_spec_from_json(self, generator: PluginSpecGenerator) -> None:
        """Test plugin spec generation from valid JSON."""
        llm_output = '''
        {
            "name": "email_service",
            "description": "Email handling service",
            "class_name": "EmailServicePlugin",
            "services": [{"name": "send_email", "description": "Send email"}],
            "event_listeners": [],
            "dependencies": ["logger"]
        }
        '''
        
        prompt_context = {"plugin_type": "service"}
        
        result = await generator.generate_plugin_spec(
            llm_output=llm_output,
            prompt_context=prompt_context
        )
        
        assert result["success"] is True
        assert result["plugin_spec"]["name"] == "email_service"
        assert len(result["plugin_spec"]["services"]) == 1

    @pytest.mark.asyncio
    async def test_generate_plugin_spec_fallback(self, generator: PluginSpecGenerator) -> None:
        """Test fallback spec generation from unparseable output."""
        llm_output = "Create an email service that can send emails and handle authentication"
        prompt_context = {"plugin_type": "service"}
        
        result = await generator.generate_plugin_spec(
            llm_output=llm_output,
            prompt_context=prompt_context
        )
        
        assert result["success"] is True
        assert "plugin_spec" in result
        assert result["plugin_spec"]["name"]  # Should have extracted a name

    @pytest.mark.asyncio
    async def test_spec_structure_validation(self, generator: PluginSpecGenerator) -> None:
        """Test plugin specification structure validation."""
        spec = {
            "name": "test_plugin",
            "description": "Test plugin",
            "class_name": "TestPlugin",
            "services": [{"name": "test_service"}],
            "event_listeners": [],
            "dependencies": ["logger"]
        }
        
        validation = await generator._validate_spec_structure(spec)
        assert validation["valid"] is True
        assert len(validation["errors"]) == 0

    def test_normalize_service(self, generator: PluginSpecGenerator) -> None:
        """Test service normalization."""
        # String service
        normalized = generator._normalize_service("send_email")
        assert normalized["name"] == "send_email"
        assert "timeout_seconds" in normalized
        
        # Dict service
        service_dict = {"name": "send_email", "description": "Send email"}
        normalized = generator._normalize_service(service_dict)
        assert normalized["name"] == "send_email"
        assert "timeout_seconds" in normalized

    def test_is_valid_plugin_name(self, generator: PluginSpecGenerator) -> None:
        """Test plugin name validation."""
        assert generator._is_valid_plugin_name("email_service") is True
        assert generator._is_valid_plugin_name("EmailService") is True
        assert generator._is_valid_plugin_name("_invalid") is False
        assert generator._is_valid_plugin_name("") is False


class TestCodeTemplateEngine:
    """Test cases for CodeTemplateEngine service."""

    @pytest.fixture
    def engine(self) -> CodeTemplateEngine:
        """Create CodeTemplateEngine instance for testing."""
        mock_app = MagicMock()
        return CodeTemplateEngine(mock_app)

    @pytest.mark.asyncio
    async def test_generate_plugin_code_basic(self, engine: CodeTemplateEngine) -> None:
        """Test basic plugin code generation."""
        plugin_spec = {
            "name": "test_plugin",
            "description": "Test plugin",
            "class_name": "TestPlugin",
            "version": "1.0.0",
            "services": [
                {"name": "test_service", "description": "Test service", "timeout_seconds": 30.0}
            ],
            "event_listeners": [],
            "dependencies": [{"name": "logger", "version": ">=1.0.0"}]
        }
        
        result = await engine.generate_plugin_code(plugin_spec)
        
        assert result["success"] is True
        assert "plugin_code" in result
        assert "test_code" in result
        assert "manifest_code" in result
        
        # Check plugin code structure
        plugin_code = result["plugin_code"]
        assert "class TestPlugin(PluginBase):" in plugin_code
        assert "@plugin(name=\"test_plugin\"" in plugin_code
        assert "@service(name=\"test_service\")" in plugin_code

    @pytest.mark.asyncio
    async def test_generate_test_code(self, engine: CodeTemplateEngine) -> None:
        """Test test code generation."""
        plugin_spec = {
            "name": "test_plugin",
            "class_name": "TestPlugin",
            "services": [{"name": "test_service"}]
        }
        
        test_code = await engine._generate_test_code(plugin_spec, {})
        
        assert "class TestTestPlugin:" in test_code
        assert "def test_plugin_initialization" in test_code
        assert "def test_test_service" in test_code
        assert "pytest.mark.asyncio" in test_code

    @pytest.mark.asyncio
    async def test_generate_manifest_code(self, engine: CodeTemplateEngine) -> None:
        """Test manifest code generation."""
        plugin_spec = {
            "name": "test_plugin",
            "description": "Test plugin",
            "version": "1.0.0",
            "services": [{"name": "test_service", "description": "Test service"}],
            "dependencies": [{"name": "logger", "version": ">=1.0.0"}]
        }
        
        manifest_code = await engine._generate_manifest_code(plugin_spec, {})
        
        assert "name: test_plugin" in manifest_code
        assert "version: 1.0.0" in manifest_code
        assert "description: Test plugin" in manifest_code
        assert "dependencies:" in manifest_code
        assert "services:" in manifest_code

    def test_estimate_complexity(self, engine: CodeTemplateEngine) -> None:
        """Test complexity estimation."""
        # Low complexity
        simple_spec = {"services": [{"name": "test"}], "event_listeners": [], "dependencies": []}
        assert engine._estimate_complexity(simple_spec) == "low"
        
        # High complexity
        complex_spec = {
            "services": [{"name": f"service_{i}"} for i in range(5)],
            "event_listeners": [{"event": f"event_{i}"} for i in range(3)],
            "dependencies": [{"name": f"dep_{i}"} for i in range(4)]
        }
        assert engine._estimate_complexity(complex_spec) == "high"


class TestWiringIntegration:
    """Test cases for WiringIntegration service."""

    @pytest.fixture
    def wiring(self) -> WiringIntegration:
        """Create WiringIntegration instance for testing."""
        mock_app = MagicMock()
        return WiringIntegration(mock_app)

    @pytest.mark.asyncio
    async def test_analyze_integration_opportunities(self, wiring: WiringIntegration) -> None:
        """Test integration opportunity analysis."""
        plugin_spec = {
            "name": "email_service",
            "services": [{"name": "send_email"}],
            "event_listeners": [],
            "dependencies": ["logger"]
        }
        
        app_context = {
            "available_services": ["logger", "config", "notification"],
            "available_events": ["app.startup", "user.login"],
            "available_dependencies": ["logger", "config", "auth"]
        }
        
        result = await wiring.analyze_integration_opportunities(
            plugin_spec=plugin_spec,
            app_context=app_context
        )
        
        assert result["success"] is True
        assert "service_analysis" in result
        assert "event_analysis" in result
        assert "dependency_injection_analysis" in result
        assert "integration_suggestions" in result

    def test_find_potential_service_dependencies(self, wiring: WiringIntegration) -> None:
        """Test potential service dependency finding."""
        available_services = ["logger", "config", "notification", "auth"]
        
        # Email service should suggest notification dependencies
        deps = wiring._find_potential_service_dependencies("send_email", available_services)
        assert "logger" in deps
        assert "notification" in deps
        
        # Auth service should suggest security dependencies
        deps = wiring._find_potential_service_dependencies("authenticate", available_services)
        assert "auth" in deps

    def test_calculate_service_compatibility(self, wiring: WiringIntegration) -> None:
        """Test service compatibility calculation."""
        potential_deps = ["logger", "config", "notification"]
        scores = wiring._calculate_service_compatibility("send_email", potential_deps)
        
        assert all(0.0 <= score <= 1.0 for score in scores.values())
        assert "logger" in scores
        assert scores["logger"] > 0.5  # Logger should have high compatibility

    def test_suggest_events_for_plugin_type(self, wiring: WiringIntegration) -> None:
        """Test event suggestions for plugin types."""
        service_events = wiring._suggest_events_for_plugin_type("service")
        assert "app.startup" in service_events
        assert "app.shutdown" in service_events
        
        auth_events = wiring._suggest_events_for_plugin_type("auth")
        assert "user.login" in auth_events
        assert "user.logout" in auth_events


class TestQualityScoring:
    """Test cases for QualityScoring service."""

    @pytest.fixture
    def scorer(self) -> QualityScoring:
        """Create QualityScoring instance for testing."""
        mock_app = MagicMock()
        return QualityScoring(mock_app)

    @pytest.mark.asyncio
    async def test_assess_plugin_quality_complete(self, scorer: QualityScoring) -> None:
        """Test complete plugin quality assessment."""
        plugin_spec = {
            "name": "test_plugin",
            "services": [{"name": "test_service"}],
            "event_listeners": [],
            "dependencies": ["logger"]
        }
        
        plugin_code = '''
"""Test plugin module."""

import logging
from typing import Any

from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service

logger = logging.getLogger(__name__)

@plugin(name="test_plugin", version="1.0.0")
class TestPlugin(PluginBase):
    """Test plugin."""
    
    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize plugin."""
        super().__init__(**injected_dependencies)
        self.logger = logger
    
    @service(name="test_service")
    async def test_service(self, data: dict[str, Any]) -> dict[str, Any]:
        """Test service."""
        try:
            return {"success": True}
        except Exception as e:
            raise PluggingerValidationError(f"Service failed: {e}") from e
'''
        
        test_code = '''
"""Tests for test plugin."""

import pytest
from test_plugin import TestPlugin

class TestTestPlugin:
    """Test cases."""
    
    @pytest.mark.asyncio
    async def test_service(self) -> None:
        """Test service."""
        plugin = TestPlugin()
        result = await plugin.test_service({})
        assert result["success"] is True
'''
        
        manifest_code = '''
name: test_plugin
version: 1.0.0
description: Test plugin
'''
        
        result = await scorer.assess_plugin_quality(
            plugin_spec=plugin_spec,
            generated_code=plugin_code,
            test_code=test_code,
            manifest_code=manifest_code
        )
        
        assert result["success"] is True
        assert "overall_score" in result
        assert "quality_grade" in result
        assert "assessments" in result
        assert 0.0 <= result["overall_score"] <= 1.0

    @pytest.mark.asyncio
    async def test_assess_code_structure(self, scorer: QualityScoring) -> None:
        """Test code structure assessment."""
        good_code = '''
@plugin(name="test", version="1.0.0")
class TestPlugin(PluginBase):
    def __init__(self, **injected_dependencies: Any) -> None:
        super().__init__(**injected_dependencies)
    
    @service(name="test")
    async def test(self) -> dict[str, Any]:
        return {}
'''
        
        plugin_spec = {"services": [{"name": "test"}]}
        assessment = await scorer._assess_code_structure(good_code, plugin_spec, {})
        
        assert assessment["score"] > 0.8
        assert len(assessment["issues"]) == 0

    def test_determine_quality_grade(self, scorer: QualityScoring) -> None:
        """Test quality grade determination."""
        assert scorer._determine_quality_grade(0.95) == "A"
        assert scorer._determine_quality_grade(0.85) == "B"
        assert scorer._determine_quality_grade(0.75) == "C"
        assert scorer._determine_quality_grade(0.65) == "D"
        assert scorer._determine_quality_grade(0.55) == "F"
