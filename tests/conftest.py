"""
Pytest configuration for Plugginger tests.
Sets up API keys and test environment.
"""

import os
import pytest
from pathlib import Path
from typing import Generator


@pytest.fixture(scope="session", autouse=True)
def setup_api_keys() -> Generator[None, None, None]:
    """Set up API keys for integration tests."""
    # Load from .env file if it exists
    env_file = Path(__file__).parent.parent / ".env"
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    os.environ[key] = value

    # Fallback: Set API keys directly if not already set
    if not os.getenv("OPENAI_API_KEY"):
        os.environ["OPENAI_API_KEY"] = "***********************************************************************************************************************************************************************"

    if not os.getenv("GOOGLE_API_KEY"):
        os.environ["GOOGLE_API_KEY"] = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"

    yield

    # Cleanup is optional since these are test keys
