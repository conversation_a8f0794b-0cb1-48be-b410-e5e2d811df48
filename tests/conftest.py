"""
Pytest configuration for Plugginger tests.
Sets up API keys and test environment.
"""

import os
import pytest


@pytest.fixture(scope="session", autouse=True)
def setup_api_keys():
    """Set up API keys for integration tests."""
    # Set OpenAI API key
    os.environ["OPENAI_API_KEY"] = "***********************************************************************************************************************************************************************"
    
    # Set Google API key
    os.environ["GOOGLE_API_KEY"] = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"
    
    yield
    
    # Cleanup is optional since these are test keys
