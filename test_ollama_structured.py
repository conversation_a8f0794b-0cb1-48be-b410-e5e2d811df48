#!/usr/bin/env python3
"""
Test Ollama Structured JSON Output
"""

import asyncio
import aiohttp
import json
import time


async def test_ollama_structured_json():
    """Test Ollama's structured JSON generation capability."""
    print("🧪 Testing Ollama Structured JSON Output\n")
    
    # Test cases
    test_cases = [
        {
            "name": "Simple Plugin Spec",
            "prompt": "Create a JSON object with 'name' and 'description' for a hello world plugin.",
            "expected_keys": ["name", "description"]
        },
        {
            "name": "Service Configuration",
            "prompt": "Create a JSON configuration for an email service with host, port, username, and ssl_enabled fields.",
            "expected_keys": ["host", "port", "username", "ssl_enabled"]
        },
        {
            "name": "Plugin Manifest",
            "prompt": "Create a plugin manifest JSON with name, version, author, dependencies (array), and services (array).",
            "expected_keys": ["name", "version", "author", "dependencies", "services"]
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}️⃣ Test: {test_case['name']}")
        print(f"   Prompt: {test_case['prompt']}")
        
        # Prepare request
        url = "http://localhost:11434/api/generate"
        data = {
            "model": "granite3-dense:2b",
            "prompt": f"{test_case['prompt']} Respond only with valid JSON, no additional text.",
            "options": {
                "num_predict": 100,
                "temperature": 0.1
            },
            "format": "json",  # This is the key for structured output!
            "stream": False
        }
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data, timeout=aiohttp.ClientTimeout(total=15)) as response:
                    if response.status == 200:
                        result = await response.json()
                        elapsed = time.time() - start_time
                        
                        content = result.get('response', '')
                        print(f"   ⏱️  Response time: {elapsed:.2f}s")
                        print(f"   📄 Raw response: {content}")
                        
                        # Try to parse JSON
                        try:
                            parsed_json = json.loads(content)
                            print(f"   ✅ Valid JSON: {parsed_json}")
                            
                            # Check if expected keys are present
                            missing_keys = [key for key in test_case['expected_keys'] if key not in parsed_json]
                            if missing_keys:
                                print(f"   ⚠️  Missing keys: {missing_keys}")
                            else:
                                print(f"   🎯 All expected keys present!")
                            
                            results.append({
                                'test': test_case['name'],
                                'success': True,
                                'valid_json': True,
                                'has_expected_keys': len(missing_keys) == 0,
                                'response_time': elapsed,
                                'content': parsed_json
                            })
                            
                        except json.JSONDecodeError as e:
                            print(f"   ❌ Invalid JSON: {e}")
                            results.append({
                                'test': test_case['name'],
                                'success': True,
                                'valid_json': False,
                                'response_time': elapsed,
                                'error': str(e)
                            })
                    else:
                        error_text = await response.text()
                        print(f"   ❌ HTTP Error {response.status}: {error_text}")
                        results.append({
                            'test': test_case['name'],
                            'success': False,
                            'error': f"HTTP {response.status}: {error_text}"
                        })
                        
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"   ❌ Exception after {elapsed:.2f}s: {e}")
            results.append({
                'test': test_case['name'],
                'success': False,
                'error': str(e)
            })
        
        print()  # Empty line between tests
    
    # Summary
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    successful_tests = [r for r in results if r.get('success', False)]
    valid_json_tests = [r for r in results if r.get('valid_json', False)]
    complete_tests = [r for r in results if r.get('has_expected_keys', False)]
    
    print(f"✅ Successful API calls: {len(successful_tests)}/{len(results)}")
    print(f"📋 Valid JSON responses: {len(valid_json_tests)}/{len(results)}")
    print(f"🎯 Complete responses: {len(complete_tests)}/{len(results)}")
    
    if valid_json_tests:
        avg_time = sum(r['response_time'] for r in valid_json_tests) / len(valid_json_tests)
        print(f"⏱️  Average response time: {avg_time:.2f}s")
    
    print("\n🔍 Detailed Results:")
    for result in results:
        status = "✅" if result.get('success') else "❌"
        json_status = "📋" if result.get('valid_json') else "❌"
        complete_status = "🎯" if result.get('has_expected_keys') else "⚠️"
        
        print(f"{status} {json_status} {complete_status} {result['test']}")
        if 'content' in result:
            print(f"    Content: {result['content']}")
    
    # Overall assessment
    if len(valid_json_tests) == len(results):
        print("\n🎉 OLLAMA STRUCTURED JSON: FULLY FUNCTIONAL!")
        print("   ✅ All tests produced valid JSON")
        print("   ✅ Fast response times")
        print("   ✅ Reliable structured output")
        return True
    elif len(valid_json_tests) > 0:
        print(f"\n⚠️  OLLAMA STRUCTURED JSON: PARTIALLY FUNCTIONAL ({len(valid_json_tests)}/{len(results)})")
        return False
    else:
        print("\n❌ OLLAMA STRUCTURED JSON: NOT FUNCTIONAL")
        return False


async def main():
    """Run Ollama structured JSON tests."""
    success = await test_ollama_structured_json()
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
