#!/usr/bin/env python3
"""Debug script for manifest conversion."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from plugginger.manifest.converter import ManifestConverter

def debug_manifest():
    """Debug the plugin_generator manifest conversion."""
    manifest_path = Path("src/plugginger/plugins/internal/plugin_generator/manifest.yaml")
    
    print(f"Debugging manifest: {manifest_path}")
    print(f"File exists: {manifest_path.exists()}")
    
    if manifest_path.exists():
        # Read raw content
        with open(manifest_path) as f:
            content = f.read()
        print(f"Raw content:\n{content[:500]}...")
        
        # Try conversion
        converter = ManifestConverter()
        try:
            result = converter.yaml_to_pydantic(manifest_path)
            print(f"Conversion successful: {result.metadata.name}")
        except Exception as e:
            print(f"Conversion failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_manifest()
