# CLI Model Selection Integration Design

## 🎯 Ziel
Intelligente Modellauswahl in den CLI-Workflow integrieren, sodass <PERSON><PERSON>er optimale Modelle für ihre Tasks erhalten.

## 🔧 CLI Parameter Erweiterung

### Neue Parameter
```bash
plugginger new plugin my_plugin --prompt "Create email service" \
  --model-tier fast|balanced|quality \
  --max-cost 0.001 \
  --max-response-time 1000 \
  --preferred-provider ollama|gemini|openai \
  --model-selection auto|manual
```

### Parameter Details
- `--model-tier`: Bevorzugte Performance-Stufe
- `--max-cost`: Maximale Kosten pro 1k Tokens
- `--max-response-time`: Maximale Antwortzeit in ms
- `--preferred-provider`: Bevorzugter Provider
- `--model-selection`: Automatisch oder manuell

## 🧠 Automatische Task-Erkennung

### Task-Type Detection
```python
def detect_task_type(prompt: str) -> TaskType:
    """Automatische Erkennung des Task-Types aus dem Prompt."""
    
    # Code-Generation Keywords
    if any(word in prompt.lower() for word in ['code', 'function', 'class', 'api', 'service']):
        return TaskType.CODE_GENERATION
    
    # JSON/Structured Keywords  
    if any(word in prompt.lower() for word in ['json', 'schema', 'structure', 'config']):
        return TaskType.STRUCTURED_JSON
    
    # Default: Simple Text
    return TaskType.SIMPLE_TEXT
```

### Quality Requirements Detection
```python
def detect_quality_requirements(prompt: str, context: str) -> ModelTier:
    """Automatische Erkennung der Qualitätsanforderungen."""
    
    # High Quality Keywords
    if any(word in prompt.lower() for word in ['production', 'critical', 'important', 'complex']):
        return ModelTier.QUALITY
    
    # Fast Keywords
    if any(word in prompt.lower() for word in ['quick', 'simple', 'basic', 'prototype']):
        return ModelTier.FAST
    
    # Default: Balanced
    return ModelTier.BALANCED
```

## 🔄 Workflow Integration

### 1. Pre-Generation Phase
```python
async def select_optimal_model(
    prompt: str,
    context: Optional[str] = None,
    user_preferences: Dict[str, Any] = None
) -> ModelSpec:
    """Wähle optimales Modell vor der Generation."""
    
    # 1. Task-Type automatisch erkennen
    task_type = detect_task_type(prompt)
    
    # 2. Quality-Tier automatisch erkennen
    preferred_tier = detect_quality_requirements(prompt, context or "")
    
    # 3. User-Preferences anwenden
    if user_preferences:
        preferred_tier = user_preferences.get('model_tier', preferred_tier)
        max_cost = user_preferences.get('max_cost')
        max_time = user_preferences.get('max_response_time')
        providers = user_preferences.get('available_providers')
    
    # 4. Modell auswählen
    selector = ModelSelector()
    selected = selector.select_model(
        task_type=task_type,
        preferred_tier=preferred_tier,
        max_cost_per_1k=max_cost,
        max_response_time_ms=max_time,
        available_providers=providers
    )
    
    return selected
```

### 2. Generation Phase
```python
async def generate_with_optimal_model(
    prompt: str,
    selected_model: ModelSpec,
    **kwargs
) -> Dict[str, Any]:
    """Generiere mit dem ausgewählten Modell."""
    
    # Provider erstellen
    provider = ProviderFactory.create_provider(
        provider_type=selected_model.provider,
        model=selected_model.model_name,
        **kwargs
    )
    
    # Generation durchführen
    result = await provider.generate_structured(
        system_message="You are a helpful plugin generator.",
        user_message=prompt,
        ebnf_grammar=get_plugin_grammar(),
        max_retries=3
    )
    
    # Metriken hinzufügen
    result['model_info'] = {
        'provider': selected_model.provider,
        'model': selected_model.model_name,
        'tier': selected_model.tier.value,
        'cost_per_1k': selected_model.cost_per_1k_tokens,
        'expected_time_ms': selected_model.typical_response_time_ms
    }
    
    return result
```

### 3. Post-Generation Analysis
```python
async def analyze_generation_performance(
    result: Dict[str, Any],
    selected_model: ModelSpec,
    actual_time_ms: int
) -> Dict[str, Any]:
    """Analysiere die Generation-Performance."""
    
    analysis = {
        'model_performance': {
            'expected_time_ms': selected_model.typical_response_time_ms,
            'actual_time_ms': actual_time_ms,
            'time_efficiency': selected_model.typical_response_time_ms / actual_time_ms,
            'cost_efficiency': selected_model.cost_per_1k_tokens,
            'quality_score': selected_model.quality_score
        },
        'generation_quality': {
            'success': result.get('success', False),
            'validated': result.get('validated', False),
            'retries_used': result.get('retries_used', 0)
        }
    }
    
    return analysis
```

## 📊 User Feedback & Learning

### Model Performance Tracking
```python
class ModelPerformanceTracker:
    """Verfolge Model-Performance für kontinuierliche Verbesserung."""
    
    def record_performance(
        self,
        model_spec: ModelSpec,
        task_type: TaskType,
        actual_time_ms: int,
        success: bool,
        quality_score: float
    ) -> None:
        """Zeichne Performance-Daten auf."""
        
        # Update model statistics
        # Adjust future selections based on real performance
        pass
    
    def get_model_recommendations(
        self,
        task_type: TaskType,
        user_history: List[Dict[str, Any]]
    ) -> List[ModelSpec]:
        """Gib personalisierte Modell-Empfehlungen."""
        pass
```

## 🎯 CLI Usage Examples

### Automatische Auswahl (Standard)
```bash
# Automatische Task-Erkennung und Modell-Auswahl
plugginger new plugin email_service --prompt "Create an email service with SMTP support"
# → Erkennt: CODE_GENERATION, BALANCED → Wählt: ollama/qwen2.5-coder:7b
```

### Explizite Präferenzen
```bash
# Schnelle Prototyping
plugginger new plugin quick_test --prompt "Simple hello world" --model-tier fast
# → Wählt: ollama/granite3-dense:2b (100ms)

# Hohe Qualität für Production
plugginger new plugin prod_service --prompt "Critical payment service" --model-tier quality
# → Wählt: openai/gpt-4o (beste Qualität)

# Kostenlos nur
plugginger new plugin free_plugin --prompt "Test plugin" --max-cost 0
# → Wählt: ollama/* (kostenlose Modelle)

# Bestimmter Provider
plugginger new plugin gemini_test --prompt "Create service" --preferred-provider gemini
# → Wählt: gemini/gemini-1.5-flash oder gemini-1.5-pro
```

### Performance-Optimierung
```bash
# Ultra-schnelle Antwort
plugginger new plugin fast_plugin --prompt "Simple service" --max-response-time 200
# → Wählt: ollama/granite3-dense:2b (100ms)

# Budget-bewusst
plugginger new plugin cheap_plugin --prompt "Create service" --max-cost 0.001
# → Wählt: gemini/gemini-1.5-flash (sehr günstig)
```

## 🔄 Fallback-Strategien

### Provider Unavailable
```python
async def handle_provider_fallback(
    selected_model: ModelSpec,
    available_providers: List[str]
) -> ModelSpec:
    """Handle provider fallback wenn ausgewählter Provider nicht verfügbar."""
    
    if selected_model.provider not in available_providers:
        # Finde ähnliches Modell bei verfügbarem Provider
        selector = ModelSelector()
        fallback = selector.select_model(
            task_type=detect_task_type_from_model(selected_model),
            preferred_tier=selected_model.tier,
            available_providers=available_providers
        )
        
        logger.warning(f"Provider {selected_model.provider} unavailable, using fallback: {fallback.provider}/{fallback.model_name}")
        return fallback
    
    return selected_model
```

## 📈 Kontinuierliche Verbesserung

### Model Performance Learning
- Echte Response-Zeiten vs. erwartete Zeiten
- Erfolgsraten pro Modell und Task-Type
- User-Zufriedenheit und Feedback
- Kosten-Nutzen-Analyse

### Adaptive Selection
- Modell-Auswahl basierend auf historischer Performance
- User-spezifische Präferenzen lernen
- Task-spezifische Optimierungen
- Provider-Verfügbarkeit berücksichtigen
