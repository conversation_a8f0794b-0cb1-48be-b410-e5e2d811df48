# src/plugginger/logging/structured_logger.py

"""
Structured logger implementation for AI-Agent analysis and debugging.

This module provides the core StructuredLogger class that outputs JSON-formatted
logs with rich context, performance metrics, and correlation IDs for AI-Agent
consumption and analysis.
"""

from __future__ import annotations

import json
import logging
import time
import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import Any

# --- Logging Constants ---

# Length of correlation ID for log session tracking
CORRELATION_ID_LENGTH: int = 8


class LogLevel(Enum):
    """Log level enumeration for structured logging."""

    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class EventType(Enum):
    """Event type enumeration for structured logging."""

    # Build Events
    BUILD_STARTED = "build_started"
    BUILD_COMPLETED = "build_completed"
    BUILD_FAILED = "build_failed"

    # Plugin Events
    PLUGIN_DISCOVERY = "plugin_discovery"
    PLUGIN_LOADING = "plugin_loading"
    PLUGIN_LOADED = "plugin_loaded"
    PLUGIN_FAILED = "plugin_failed"

    # DI Events
    DI_RESOLUTION_STARTED = "di_resolution_started"
    DI_RESOLUTION_COMPLETED = "di_resolution_completed"
    DI_DEPENDENCY_RESOLVED = "di_dependency_resolved"

    # Event System
    EVENT_LISTENER_REGISTERED = "event_listener_registered"
    EVENT_EMITTED = "event_emitted"
    EVENT_PROCESSED = "event_processed"

    # Performance
    PERFORMANCE_METRIC = "performance_metric"
    MEMORY_USAGE = "memory_usage"


class StructuredLogger:
    """
    AI-Agent-friendly structured logger.

    This logger outputs JSON-formatted log entries with rich context,
    performance metrics, and correlation IDs for machine analysis.
    """

    def __init__(
        self,
        name: str,
        base_logger: logging.Logger,
        enable_structured: bool = True,
        correlation_id: str | None = None
    ) -> None:
        """
        Initialize the structured logger.

        Args:
            name: Logger name for identification
            base_logger: Underlying Python logger instance
            enable_structured: Whether to output structured JSON logs
            correlation_id: Optional correlation ID for log grouping
        """
        self.name = name
        self.base_logger = base_logger
        self.enable_structured = enable_structured
        self.correlation_id = correlation_id or self._generate_correlation_id()
        self.session_start = datetime.now(UTC)

    def _generate_correlation_id(self) -> str:
        """Generate a unique correlation ID for this logger session."""
        return str(uuid.uuid4())[:CORRELATION_ID_LENGTH]

    def log_event(
        self,
        event_type: EventType,
        level: LogLevel = LogLevel.INFO,
        message: str = "",
        context: dict[str, Any] | None = None,
        metrics: dict[str, float] | None = None,
        duration_ms: float | None = None
    ) -> None:
        """
        Log structured event for AI-Agent analysis.

        Args:
            event_type: Type of event being logged
            level: Log level for the event
            message: Human-readable message
            context: Additional context data
            metrics: Performance metrics
            duration_ms: Operation duration in milliseconds
        """
        if not self.enable_structured:
            # Fallback to traditional logging
            self.base_logger.log(
                getattr(logging, level.value.upper()),
                f"[{event_type.value}] {message}"
            )
            return

        # Create structured log entry
        log_entry = {
            "timestamp": datetime.now(UTC).isoformat(),
            "correlation_id": self.correlation_id,
            "logger_name": self.name,
            "event_type": event_type.value,
            "level": level.value,
            "message": message,
            "context": context or {},
            "metrics": metrics or {},
            "session_duration_ms": (datetime.now(UTC) - self.session_start).total_seconds() * 1000
        }

        if duration_ms is not None:
            log_entry["duration_ms"] = duration_ms

        # Log as JSON for AI-Agent consumption
        self.base_logger.log(
            getattr(logging, level.value.upper()),
            json.dumps(log_entry, separators=(',', ':'))
        )

    def log_build_event(
        self,
        phase: str,
        status: str,
        context: dict[str, Any] | None = None,
        duration_ms: float | None = None
    ) -> None:
        """
        Log build-specific events.

        Args:
            phase: Build phase name
            status: Build status (started, completed, failed)
            context: Additional context data
            duration_ms: Phase duration in milliseconds
        """
        event_type_map = {
            "started": EventType.BUILD_STARTED,
            "completed": EventType.BUILD_COMPLETED,
            "failed": EventType.BUILD_FAILED
        }

        event_type = event_type_map.get(status, EventType.BUILD_STARTED)
        level = LogLevel.ERROR if status == "failed" else LogLevel.INFO

        self.log_event(
            event_type=event_type,
            level=level,
            message=f"Build phase '{phase}' {status}",
            context={
                "build_phase": phase,
                "build_status": status,
                **(context or {})
            },
            duration_ms=duration_ms
        )

    def log_performance(
        self,
        operation: str,
        duration_ms: float,
        context: dict[str, Any] | None = None
    ) -> None:
        """
        Log performance metrics.

        Args:
            operation: Name of the operation
            duration_ms: Operation duration in milliseconds
            context: Additional context data
        """
        self.log_event(
            event_type=EventType.PERFORMANCE_METRIC,
            message=f"Performance: {operation} took {duration_ms:.2f}ms",
            context={
                "operation": operation,
                **(context or {})
            },
            metrics={
                "duration_ms": duration_ms,
                "operations_per_second": 1000 / duration_ms if duration_ms > 0 else 0
            },
            duration_ms=duration_ms
        )

    def create_timer(self, operation: str) -> OperationTimer:
        """
        Create timer for automatic performance logging.

        Args:
            operation: Name of the operation to time

        Returns:
            OperationTimer context manager
        """
        return OperationTimer(self, operation)


class OperationTimer:
    """Context manager for automatic performance logging."""

    def __init__(self, logger: StructuredLogger, operation: str) -> None:
        """
        Initialize the operation timer.

        Args:
            logger: StructuredLogger instance
            operation: Name of the operation being timed
        """
        self.logger = logger
        self.operation = operation
        self.start_time: float | None = None

    def __enter__(self) -> OperationTimer:
        """Start timing the operation."""
        self.start_time = time.perf_counter()
        return self

    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        """Stop timing and log the performance metric."""
        if self.start_time is not None:
            duration_ms = (time.perf_counter() - self.start_time) * 1000
            self.logger.log_performance(self.operation, duration_ms)
