"""
JSON Validator Core Plugin.

Provides JSON schema validation with retry logic and EBNF grammar support.
This is a reusable core plugin that end users can leverage in their applications.
"""

import json
import logging
from typing import Any, Dict, List, Optional

from plugginger.api.plugin import PluginBase, service
from plugginger.core.exceptions import PluggingerValidationError
from plugginger.plugins.core.json_validator.services.validation_service import ValidationService
from plugginger.plugins.core.json_validator.services.schema_service import SchemaService
from plugginger.plugins.core.json_validator.services.ebnf_service import EbnfService

logger = logging.getLogger(__name__)


class JsonValidatorPlugin(PluginBase):
    """Core plugin for JSON validation with schema and EBNF support."""
    
    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize JSON validator plugin.
        
        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger
        
        # Initialize services
        self.validation_service = ValidationService()
        self.schema_service = SchemaService()
        self.ebnf_service = EbnfService()
        
        # Configuration from manifest
        self.default_retry_count = 3
        self.default_timeout_seconds = 30
        self.strict_validation = True
        self.allow_additional_properties = False
    
    async def setup(self) -> None:
        """Setup the JSON validator plugin."""
        self.logger.info("JSON Validator plugin initialized")
        
        # Load configuration if available
        if hasattr(self.app, 'config'):
            config = getattr(self.app.config, 'json_validator', {})
            self.default_retry_count = config.get('default_retry_count', self.default_retry_count)
            self.default_timeout_seconds = config.get('default_timeout_seconds', self.default_timeout_seconds)
            self.strict_validation = config.get('strict_validation', self.strict_validation)
            self.allow_additional_properties = config.get('allow_additional_properties', self.allow_additional_properties)
    
    async def teardown(self) -> None:
        """Cleanup the JSON validator plugin."""
        self.logger.info("JSON Validator plugin shutting down")
    
    @service(name="validate_json")
    async def validate_json(
        self,
        data: str,
        schema: Dict[str, Any],
        retry_count: Optional[int] = None,
        strict: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Validate JSON data against a schema with retry logic.
        
        Args:
            data: JSON string to validate
            schema: JSON schema dictionary
            retry_count: Number of retry attempts (default from config)
            strict: Enable strict validation (default from config)
            
        Returns:
            Validation result with success status and details
            
        Example:
            result = await app.call_service("json_validator.validate_json", {
                "data": '{"name": "John", "age": 30}',
                "schema": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "age": {"type": "integer"}
                    },
                    "required": ["name", "age"]
                }
            })
        """
        retry_count = retry_count or self.default_retry_count
        strict = strict if strict is not None else self.strict_validation
        
        self.logger.debug(f"Validating JSON with retry_count={retry_count}, strict={strict}")
        
        try:
            result = await self.validation_service.validate_with_retry(
                data=data,
                schema=schema,
                retry_count=retry_count,
                strict=strict,
                allow_additional_properties=self.allow_additional_properties
            )
            
            self.logger.info(f"JSON validation {'successful' if result['valid'] else 'failed'}")
            return result
            
        except Exception as e:
            self.logger.error(f"JSON validation error: {e}")
            raise PluggingerValidationError(f"JSON validation failed: {e}") from e
    
    @service(name="validate_with_ebnf")
    async def validate_with_ebnf(
        self,
        data: str,
        grammar: str,
        retry_count: Optional[int] = None
    ) -> Dict[str, Any]:
        """Validate JSON using EBNF grammar constraints.
        
        Args:
            data: JSON string to validate
            grammar: EBNF grammar string
            retry_count: Number of retry attempts (default from config)
            
        Returns:
            Validation result with success status and details
            
        Example:
            result = await app.call_service("json_validator.validate_with_ebnf", {
                "data": '{"status": "success", "code": 200}',
                "grammar": "response ::= '{\"status\":' status ',\"code\":' code '}'"
            })
        """
        retry_count = retry_count or self.default_retry_count
        
        self.logger.debug(f"Validating JSON with EBNF grammar, retry_count={retry_count}")
        
        try:
            result = await self.ebnf_service.validate_with_grammar(
                data=data,
                grammar=grammar,
                retry_count=retry_count
            )
            
            self.logger.info(f"EBNF validation {'successful' if result['valid'] else 'failed'}")
            return result
            
        except Exception as e:
            self.logger.error(f"EBNF validation error: {e}")
            raise PluggingerValidationError(f"EBNF validation failed: {e}") from e
    
    @service(name="create_schema")
    async def create_schema(
        self,
        example_data: str,
        strict: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Create JSON schema from example data.
        
        Args:
            example_data: Example JSON string to generate schema from
            strict: Generate strict schema (default from config)
            
        Returns:
            Generated JSON schema
            
        Example:
            result = await app.call_service("json_validator.create_schema", {
                "example_data": '{"name": "John", "age": 30, "active": true}'
            })
        """
        strict = strict if strict is not None else self.strict_validation
        
        self.logger.debug(f"Creating schema from example data, strict={strict}")
        
        try:
            schema = await self.schema_service.generate_schema(
                example_data=example_data,
                strict=strict,
                allow_additional_properties=self.allow_additional_properties
            )
            
            self.logger.info("Schema generation successful")
            return {
                "success": True,
                "schema": schema,
                "metadata": {
                    "strict": strict,
                    "allow_additional_properties": self.allow_additional_properties
                }
            }
            
        except Exception as e:
            self.logger.error(f"Schema generation error: {e}")
            raise PluggingerValidationError(f"Schema generation failed: {e}") from e
    
    @service(name="validate_batch")
    async def validate_batch(
        self,
        data_list: List[str],
        schema: Dict[str, Any],
        fail_fast: bool = False
    ) -> Dict[str, Any]:
        """Validate multiple JSON strings against a schema.
        
        Args:
            data_list: List of JSON strings to validate
            schema: JSON schema dictionary
            fail_fast: Stop on first validation error
            
        Returns:
            Batch validation results
        """
        self.logger.debug(f"Validating batch of {len(data_list)} items, fail_fast={fail_fast}")
        
        results = []
        errors = []
        
        for i, data in enumerate(data_list):
            try:
                result = await self.validate_json(data, schema)
                results.append({
                    "index": i,
                    "valid": result["valid"],
                    "data": data,
                    "errors": result.get("errors", [])
                })
                
                if fail_fast and not result["valid"]:
                    break
                    
            except Exception as e:
                error_info = {
                    "index": i,
                    "data": data,
                    "error": str(e)
                }
                errors.append(error_info)
                
                if fail_fast:
                    break
        
        valid_count = sum(1 for r in results if r["valid"])
        
        return {
            "success": len(errors) == 0,
            "total_items": len(data_list),
            "valid_items": valid_count,
            "invalid_items": len(results) - valid_count,
            "error_items": len(errors),
            "results": results,
            "errors": errors
        }
