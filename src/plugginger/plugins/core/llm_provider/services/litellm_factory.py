"""
LiteLLM Provider Factory with intelligent auto-detection.

This module provides factory functions for creating LiteLLM providers
with automatic provider detection based on environment variables.
"""

from __future__ import annotations

import logging
import os
from typing import Any

from plugginger.core.exceptions import PluggingerConfigurationError

from .litellm_provider import Li<PERSON><PERSON>MProvider

logger = logging.getLogger(__name__)


class LiteLLMProviderFactory:
    """
    Factory for creating LiteLLM providers with intelligent auto-detection.

    This factory replaces the legacy ProviderFactory with a unified approach
    that supports 100+ providers through LiteLLM while maintaining backward
    compatibility with existing configuration patterns.
    """

    # Provider detection priority order
    PROVIDER_DETECTION_ORDER = [
        "openai",
        "anthropic",
        "gemini",
        "google",
        "ollama",
        "groq",
        "cohere",
        "together",
        "replicate",
        "huggingface",
        "deepseek",
        "mistral",
        "perplexity"
    ]

    # Environment variable mappings for provider detection
    PROVIDER_ENV_MAPPINGS = {
        "openai": {
            "api_key": ["OPENAI_API_KEY", "PLUGGINGER_LLM_API_KEY"],
            "base_url": ["OPENAI_API_BASE", "OPENAI_BASE_URL"],
            "model": ["OPENAI_MODEL", "PLUGGINGER_LLM_MODEL"],
            "default_model": "gpt-4o-mini"
        },
        "anthropic": {
            "api_key": ["ANTHROPIC_API_KEY"],
            "base_url": ["ANTHROPIC_API_BASE"],
            "model": ["ANTHROPIC_MODEL"],
            "default_model": "claude-3-sonnet-20240229"
        },
        "gemini": {
            "api_key": ["GOOGLE_API_KEY", "GEMINI_API_KEY"],
            "base_url": ["GOOGLE_API_BASE"],
            "model": ["GOOGLE_MODEL", "GEMINI_MODEL"],
            "default_model": "gemini-1.5-flash"
        },
        "google": {
            "api_key": ["GOOGLE_API_KEY"],
            "base_url": ["GOOGLE_API_BASE"],
            "model": ["GOOGLE_MODEL"],
            "default_model": "gemini-1.5-flash"
        },
        "ollama": {
            "api_key": [],  # Ollama doesn't need API key
            "base_url": ["OLLAMA_API_BASE", "OLLAMA_BASE_URL"],
            "model": ["OLLAMA_MODEL"],
            "default_model": "llama3.2",
            "default_base_url": "http://localhost:11434"
        },
        "groq": {
            "api_key": ["GROQ_API_KEY"],
            "base_url": ["GROQ_API_BASE"],
            "model": ["GROQ_MODEL"],
            "default_model": "llama-3.1-70b-versatile"
        },
        "cohere": {
            "api_key": ["COHERE_API_KEY"],
            "base_url": ["COHERE_API_BASE"],
            "model": ["COHERE_MODEL"],
            "default_model": "command-r-plus"
        },
        "together": {
            "api_key": ["TOGETHER_API_KEY"],
            "base_url": ["TOGETHER_API_BASE"],
            "model": ["TOGETHER_MODEL"],
            "default_model": "meta-llama/Llama-3-70b-chat-hf"
        },
        "replicate": {
            "api_key": ["REPLICATE_API_TOKEN"],
            "base_url": ["REPLICATE_API_BASE"],
            "model": ["REPLICATE_MODEL"],
            "default_model": "meta/llama-2-70b-chat"
        },
        "huggingface": {
            "api_key": ["HUGGINGFACE_API_KEY", "HF_TOKEN"],
            "base_url": ["HUGGINGFACE_API_BASE"],
            "model": ["HUGGINGFACE_MODEL"],
            "default_model": "microsoft/DialoGPT-large"
        },
        "deepseek": {
            "api_key": ["DEEPSEEK_API_KEY"],
            "base_url": ["DEEPSEEK_API_BASE"],
            "model": ["DEEPSEEK_MODEL"],
            "default_model": "deepseek-chat"
        },
        "mistral": {
            "api_key": ["MISTRAL_API_KEY"],
            "base_url": ["MISTRAL_API_BASE"],
            "model": ["MISTRAL_MODEL"],
            "default_model": "mistral-large-latest"
        },
        "perplexity": {
            "api_key": ["PERPLEXITYAI_API_KEY"],
            "base_url": ["PERPLEXITYAI_API_BASE"],
            "model": ["PERPLEXITYAI_MODEL"],
            "default_model": "llama-3.1-sonar-large-128k-online"
        }
    }

    @classmethod
    def create_provider(
        cls,
        provider: str | None = None,
        model: str | None = None,
        api_key: str | None = None,
        base_url: str | None = None,
        **kwargs: Any
    ) -> LiteLLMProvider:
        """
        Create a LiteLLM provider with specified configuration.

        Args:
            provider: Provider name (openai, anthropic, gemini, etc.)
            model: Model name
            api_key: API key
            base_url: Base URL
            **kwargs: Additional configuration

        Returns:
            Configured LiteLLMProvider instance

        Raises:
            PluggingerConfigurationError: If configuration is invalid
        """
        if provider and provider.lower() not in cls.PROVIDER_ENV_MAPPINGS:
            logger.warning(f"Provider '{provider}' not in known mappings, using as-is")

        return LiteLLMProvider(
            provider=provider,
            model=model,
            api_key=api_key,
            base_url=base_url,
            **kwargs
        )

    @classmethod
    def create_from_env(
        cls,
        provider_type: str | None = None,
        model: str | None = None,
        api_key: str | None = None,
        base_url: str | None = None
    ) -> LiteLLMProvider:
        """
        Create provider from environment variables with auto-detection.

        Args:
            provider_type: Explicit provider type (optional)
            model: Explicit model name (optional)
            api_key: Explicit API key (optional)
            base_url: Explicit base URL (optional)

        Returns:
            Configured LiteLLMProvider instance

        Raises:
            PluggingerConfigurationError: If no valid provider configuration found
        """
        # If explicit provider specified, use it
        if provider_type:
            return cls._create_explicit_provider(provider_type, model, api_key, base_url)

        # Auto-detect provider from environment
        detected_provider = cls._auto_detect_provider()
        if not detected_provider:
            raise PluggingerConfigurationError(
                "No LLM provider configuration found. Set environment variables for "
                "OpenAI (OPENAI_API_KEY), Anthropic (ANTHROPIC_API_KEY), "
                "Google (GOOGLE_API_KEY), or configure Ollama (OLLAMA_API_BASE)."
            )

        return cls._create_detected_provider(detected_provider, model, api_key, base_url)

    @classmethod
    def _create_explicit_provider(
        cls,
        provider_type: str,
        model: str | None,
        api_key: str | None,
        base_url: str | None
    ) -> LiteLLMProvider:
        """Create provider with explicit configuration."""
        provider_lower = provider_type.lower()

        if provider_lower in cls.PROVIDER_ENV_MAPPINGS:
            config = cls.PROVIDER_ENV_MAPPINGS[provider_lower]

            # Get configuration from environment if not explicitly provided
            final_api_key = api_key or cls._get_env_value(config["api_key"])
            final_base_url = base_url or cls._get_env_value(config["base_url"])
            final_model = model or cls._get_env_value(config["model"]) or config.get("default_model")

            # Set default base URL for providers that need it
            if not final_base_url and "default_base_url" in config:
                final_base_url = config["default_base_url"]

            # Validate API key requirement (except for Ollama)
            if provider_lower != "ollama" and not final_api_key:
                raise PluggingerConfigurationError(
                    f"API key required for {provider_type} provider. "
                    f"Set {config['api_key'][0]} environment variable."
                )
        else:
            # Unknown provider, use as-is
            final_api_key = api_key
            final_base_url = base_url
            final_model = model

        return cls.create_provider(
            provider=provider_type,
            model=final_model,
            api_key=final_api_key,
            base_url=final_base_url
        )

    @classmethod
    def _create_detected_provider(
        cls,
        provider_info: dict[str, Any],
        model: str | None,
        api_key: str | None,
        base_url: str | None
    ) -> LiteLLMProvider:
        """Create provider from auto-detection results."""
        provider_name = provider_info["provider"]
        detected_config = provider_info["config"]

        # Use explicit parameters if provided, otherwise use detected values
        final_api_key = api_key or provider_info.get("api_key")
        final_base_url = base_url or provider_info.get("base_url")
        final_model = model or provider_info.get("model") or detected_config.get("default_model")

        return cls.create_provider(
            provider=provider_name,
            model=final_model,
            api_key=final_api_key,
            base_url=final_base_url
        )

    @classmethod
    def _auto_detect_provider(cls) -> dict[str, Any] | None:
        """Auto-detect available provider from environment variables."""
        for provider in cls.PROVIDER_DETECTION_ORDER:
            config = cls.PROVIDER_ENV_MAPPINGS[provider]

            # Check if API key is available (or not required for Ollama)
            api_key = cls._get_env_value(config["api_key"])
            if provider == "ollama" or api_key:
                # Skip test keys for OpenAI
                if provider == "openai" and api_key and api_key.startswith("sk-test-"):
                    continue

                base_url = cls._get_env_value(config["base_url"])
                model = cls._get_env_value(config["model"])

                # Set default base URL if needed
                if not base_url and "default_base_url" in config:
                    base_url = config["default_base_url"]

                logger.info(f"Auto-detected LLM provider: {provider}")
                return {
                    "provider": provider,
                    "config": config,
                    "api_key": api_key,
                    "base_url": base_url,
                    "model": model
                }

        return None

    @classmethod
    def _get_env_value(cls, env_vars: list[str] | Any) -> str | None:
        """Get value from first available environment variable."""
        # Handle both list and sequence types
        if not env_vars:
            return None

        # Convert to list if needed
        if not isinstance(env_vars, list):
            env_vars = list(env_vars)

        for env_var in env_vars:
            value = os.getenv(env_var)
            if value:
                return value
        return None

    @classmethod
    def list_supported_providers(cls) -> list[str]:
        """Get list of supported providers."""
        return list(cls.PROVIDER_ENV_MAPPINGS.keys())

    @classmethod
    def create_with_fallback(
        cls,
        primary_provider: str | None = None,
        fallback_providers: list[str] | None = None,
        **kwargs: Any
    ) -> LiteLLMProvider:
        """
        Create provider with built-in fallback configuration.

        Args:
            primary_provider: Primary provider to use
            fallback_providers: List of fallback providers
            **kwargs: Additional configuration

        Returns:
            LiteLLMProvider with fallback configuration
        """
        if not primary_provider:
            # Auto-detect primary provider
            detected = cls._auto_detect_provider()
            if not detected:
                raise PluggingerConfigurationError("No primary provider could be detected")
            primary_provider = detected["provider"]

        # Set default fallback providers if not specified
        if not fallback_providers:
            fallback_providers = cls._get_default_fallbacks(primary_provider)

        # Create primary provider
        provider = cls.create_from_env(provider_type=primary_provider, **kwargs)

        # Store fallback configuration
        provider.fallback_providers = fallback_providers

        return provider

    @classmethod
    def _get_default_fallbacks(cls, primary_provider: str) -> list[str]:
        """Get default fallback providers for a given primary provider."""
        fallback_chains = {
            "openai": ["groq", "anthropic", "gemini", "ollama"],
            "anthropic": ["openai", "groq", "gemini", "ollama"],
            "gemini": ["openai", "groq", "anthropic", "ollama"],
            "google": ["openai", "groq", "anthropic", "ollama"],
            "groq": ["openai", "anthropic", "gemini", "ollama"],
            "cohere": ["openai", "anthropic", "groq", "ollama"],
            "together": ["openai", "groq", "anthropic", "ollama"],
            "ollama": ["openai", "groq", "anthropic", "gemini"]
        }

        return fallback_chains.get(primary_provider.lower(), ["openai", "ollama"])

    @classmethod
    def create_load_balanced(
        cls,
        providers: list[str],
        strategy: str = "round_robin",
        **kwargs: Any
    ) -> list[LiteLLMProvider]:
        """
        Create multiple providers for load balancing.

        Args:
            providers: List of provider names
            strategy: Load balancing strategy (round_robin, random, weighted)
            **kwargs: Additional configuration

        Returns:
            List of configured LiteLLMProvider instances
        """
        provider_instances = []

        for provider_name in providers:
            try:
                provider = cls.create_from_env(provider_type=provider_name, **kwargs)
                provider_instances.append(provider)
                logger.info(f"Created load-balanced provider: {provider_name}")
            except Exception as e:
                logger.warning(f"Failed to create provider {provider_name}: {e}")
                continue

        if not provider_instances:
            raise PluggingerConfigurationError("No providers could be created for load balancing")

        return provider_instances

    @classmethod
    def get_available_providers(cls) -> dict[str, bool]:
        """
        Check which providers are currently available based on environment.

        Returns:
            Dictionary mapping provider names to availability status
        """
        availability = {}

        for provider in cls.PROVIDER_DETECTION_ORDER:
            config = cls.PROVIDER_ENV_MAPPINGS[provider]

            # Check API key availability (or not required for Ollama)
            api_key = cls._get_env_value(config["api_key"])

            if provider == "ollama":
                # Ollama doesn't need API key, check if base URL is accessible
                availability[provider] = True  # Assume available
            else:
                # Other providers need API key
                availability[provider] = bool(api_key and not api_key.startswith("sk-test-"))

        return availability

    @classmethod
    def get_optimal_provider(cls, requirements: dict[str, Any] | None = None) -> str:
        """
        Get optimal provider based on requirements.

        Args:
            requirements: Dictionary of requirements (speed, cost, quality, etc.)

        Returns:
            Optimal provider name
        """
        if not requirements:
            # Default to auto-detection
            detected = cls._auto_detect_provider()
            return detected["provider"] if detected else "ollama"

        # Provider rankings for different requirements
        speed_ranking = ["groq", "openai", "anthropic", "gemini", "ollama"]
        cost_ranking = ["ollama", "groq", "openai", "gemini", "anthropic"]
        quality_ranking = ["anthropic", "openai", "gemini", "groq", "ollama"]

        available_providers = cls.get_available_providers()

        # Filter by availability
        def filter_available(ranking: list[str]) -> list[str]:
            return [p for p in ranking if available_providers.get(p, False)]

        # Choose based on primary requirement
        primary_req = requirements.get("priority", "balanced")

        if primary_req == "speed":
            candidates = filter_available(speed_ranking)
        elif primary_req == "cost":
            candidates = filter_available(cost_ranking)
        elif primary_req == "quality":
            candidates = filter_available(quality_ranking)
        else:
            # Balanced approach - prefer available providers in detection order
            candidates = [p for p in cls.PROVIDER_DETECTION_ORDER if available_providers.get(p, False)]

        return candidates[0] if candidates else "ollama"

    @classmethod
    def get_provider_info(cls, provider: str) -> dict[str, Any] | None:
        """Get configuration information for a specific provider."""
        return cls.PROVIDER_ENV_MAPPINGS.get(provider.lower())
