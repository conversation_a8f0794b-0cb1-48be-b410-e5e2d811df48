"""
Model Selection Service for LLM Provider.

Provides intelligent model selection based on task requirements,
performance characteristics, and availability.
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class TaskType(Enum):
    """Types of tasks for model selection."""
    SIMPLE_TEXT = "simple_text"
    STRUCTURED_JSON = "structured_json"
    CODE_GENERATION = "code_generation"
    ANALYSIS = "analysis"
    CREATIVE = "creative"


class ModelTier(Enum):
    """Model performance tiers."""
    FAST = "fast"          # Quick responses, lower quality
    BALANCED = "balanced"  # Good balance of speed and quality
    QUALITY = "quality"    # Best quality, slower responses


@dataclass
class ModelSpec:
    """Specification for an LLM model."""
    provider: str
    model_name: str
    tier: ModelTier
    max_tokens: int
    cost_per_1k_tokens: float
    supports_json: bool
    supports_streaming: bool
    typical_response_time_ms: int
    quality_score: float  # 0.0 to 1.0


class ModelSelector:
    """Intelligent model selection service."""
    
    def __init__(self) -> None:
        """Initialize model selector with predefined model specifications."""
        self._models = self._initialize_models()
    
    def _initialize_models(self) -> Dict[str, ModelSpec]:
        """Initialize model specifications."""
        return {
            # OpenAI Models
            "openai/gpt-4o-mini": ModelSpec(
                provider="openai",
                model_name="gpt-4o-mini",
                tier=ModelTier.FAST,
                max_tokens=128000,
                cost_per_1k_tokens=0.00015,
                supports_json=True,
                supports_streaming=True,
                typical_response_time_ms=2000,
                quality_score=0.85
            ),
            "openai/gpt-4o": ModelSpec(
                provider="openai",
                model_name="gpt-4o",
                tier=ModelTier.QUALITY,
                max_tokens=128000,
                cost_per_1k_tokens=0.005,
                supports_json=True,
                supports_streaming=True,
                typical_response_time_ms=5000,
                quality_score=0.95
            ),
            
            # Gemini Models
            "gemini/gemini-1.5-flash": ModelSpec(
                provider="gemini",
                model_name="gemini-1.5-flash",
                tier=ModelTier.FAST,
                max_tokens=1000000,
                cost_per_1k_tokens=0.000075,
                supports_json=True,
                supports_streaming=True,
                typical_response_time_ms=1500,
                quality_score=0.80
            ),
            "gemini/gemini-1.5-pro": ModelSpec(
                provider="gemini",
                model_name="gemini-1.5-pro",
                tier=ModelTier.QUALITY,
                max_tokens=2000000,
                cost_per_1k_tokens=0.00125,
                supports_json=True,
                supports_streaming=True,
                typical_response_time_ms=4000,
                quality_score=0.92
            ),
            
            # Ollama Models (Local)
            "ollama/granite3-dense:2b": ModelSpec(
                provider="ollama",
                model_name="granite3-dense:2b",
                tier=ModelTier.FAST,
                max_tokens=4096,
                cost_per_1k_tokens=0.0,  # Free local model
                supports_json=True,
                supports_streaming=True,
                typical_response_time_ms=3000,
                quality_score=0.70
            ),
            "ollama/qwen2.5-coder:7b": ModelSpec(
                provider="ollama",
                model_name="qwen2.5-coder:7b",
                tier=ModelTier.BALANCED,
                max_tokens=8192,
                cost_per_1k_tokens=0.0,  # Free local model
                supports_json=True,
                supports_streaming=True,
                typical_response_time_ms=5000,
                quality_score=0.85
            ),
        }
    
    def select_model(
        self,
        task_type: TaskType,
        preferred_tier: ModelTier = ModelTier.BALANCED,
        max_cost_per_1k: Optional[float] = None,
        max_response_time_ms: Optional[int] = None,
        require_json: bool = False,
        available_providers: Optional[List[str]] = None
    ) -> Optional[ModelSpec]:
        """Select the best model for a given task.
        
        Args:
            task_type: Type of task to perform
            preferred_tier: Preferred performance tier
            max_cost_per_1k: Maximum cost per 1k tokens
            max_response_time_ms: Maximum acceptable response time
            require_json: Whether JSON support is required
            available_providers: List of available providers
            
        Returns:
            Best matching model specification or None if no match
        """
        candidates = []
        
        for model_id, spec in self._models.items():
            # Filter by available providers
            if available_providers and spec.provider not in available_providers:
                continue
                
            # Filter by cost
            if max_cost_per_1k is not None and spec.cost_per_1k_tokens > max_cost_per_1k:
                continue
                
            # Filter by response time
            if max_response_time_ms is not None and spec.typical_response_time_ms > max_response_time_ms:
                continue
                
            # Filter by JSON support
            if require_json and not spec.supports_json:
                continue
                
            candidates.append((model_id, spec))
        
        if not candidates:
            logger.warning(f"No models found matching criteria for task {task_type}")
            return None
        
        # Score candidates based on task type and preferences
        scored_candidates = []
        for model_id, spec in candidates:
            score = self._calculate_model_score(spec, task_type, preferred_tier)
            scored_candidates.append((score, model_id, spec))
        
        # Sort by score (highest first)
        scored_candidates.sort(reverse=True)
        
        best_score, best_id, best_spec = scored_candidates[0]
        logger.info(f"Selected model {best_id} with score {best_score:.3f} for task {task_type}")
        
        return best_spec
    
    def _calculate_model_score(
        self,
        spec: ModelSpec,
        task_type: TaskType,
        preferred_tier: ModelTier
    ) -> float:
        """Calculate a score for a model based on task requirements."""
        score = 0.0
        
        # Base quality score (40% weight)
        score += spec.quality_score * 0.4
        
        # Tier preference (30% weight)
        if spec.tier == preferred_tier:
            score += 0.3
        elif abs(list(ModelTier).index(spec.tier) - list(ModelTier).index(preferred_tier)) == 1:
            score += 0.15  # Adjacent tier gets partial credit
        
        # Task-specific bonuses (20% weight)
        task_bonus = 0.0
        if task_type == TaskType.CODE_GENERATION:
            if "coder" in spec.model_name.lower():
                task_bonus += 0.15
            if spec.provider == "ollama":  # Local models good for code
                task_bonus += 0.05
        elif task_type == TaskType.STRUCTURED_JSON:
            if spec.supports_json:
                task_bonus += 0.15
        elif task_type == TaskType.SIMPLE_TEXT:
            if spec.tier == ModelTier.FAST:
                task_bonus += 0.1
        
        score += task_bonus
        
        # Cost efficiency (10% weight)
        if spec.cost_per_1k_tokens == 0.0:  # Free models
            score += 0.1
        elif spec.cost_per_1k_tokens < 0.001:  # Very cheap
            score += 0.05
        
        return score
    
    def get_available_models(self, provider: Optional[str] = None) -> List[ModelSpec]:
        """Get list of available models, optionally filtered by provider."""
        models = list(self._models.values())
        
        if provider:
            models = [m for m in models if m.provider == provider]
        
        return sorted(models, key=lambda m: (m.provider, m.tier.value, m.model_name))
    
    def get_model_by_name(self, model_name: str, provider: Optional[str] = None) -> Optional[ModelSpec]:
        """Get model specification by name."""
        for model_id, spec in self._models.items():
            if spec.model_name == model_name:
                if provider is None or spec.provider == provider:
                    return spec
        return None
