"""
LiteLLM Production Features.

This module provides production-ready features for LiteLLM providers including
rate limiting, circuit breakers, health monitoring, and security hardening.
"""

from __future__ import annotations

import asyncio
import logging
import time
from dataclasses import dataclass, field
from datetime import UTC, datetime
from enum import Enum
from typing import Any

logger = logging.getLogger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class ProviderHealth:
    """Health status for a provider."""

    provider: str
    model: str
    is_healthy: bool = True
    last_success: datetime = field(default_factory=lambda: datetime.now(UTC))
    last_failure: datetime | None = None
    consecutive_failures: int = 0
    total_requests: int = 0
    successful_requests: int = 0
    avg_response_time_ms: float = 0.0
    circuit_breaker_state: CircuitBreakerState = CircuitBreakerState.CLOSED


class RateLimiter:
    """Token bucket rate limiter for provider requests."""

    def __init__(self, requests_per_minute: int = 60) -> None:
        """
        Initialize rate limiter.

        Args:
            requests_per_minute: Maximum requests allowed per minute
        """
        self.requests_per_minute = requests_per_minute
        self.tokens = requests_per_minute
        self.last_refill = time.time()
        self.lock = asyncio.Lock()

    async def acquire(self) -> bool:
        """
        Acquire a token for making a request.

        Returns:
            True if token acquired, False if rate limited
        """
        async with self.lock:
            now = time.time()

            # Refill tokens based on elapsed time
            elapsed = now - self.last_refill
            tokens_to_add = elapsed * (self.requests_per_minute / 60.0)
            self.tokens = min(float(self.requests_per_minute), self.tokens + tokens_to_add)
            self.last_refill = now

            # Check if we have tokens available
            if self.tokens >= 1:
                self.tokens -= 1
                return True
            else:
                return False


class CircuitBreaker:
    """Circuit breaker for provider fault tolerance."""

    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        success_threshold: int = 3
    ) -> None:
        """
        Initialize circuit breaker.

        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before trying half-open
            success_threshold: Successes needed in half-open to close circuit
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold

        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0.0
        self.lock = asyncio.Lock()

    async def call(self, func, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection.

        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Function result

        Raises:
            Exception: If circuit is open or function fails
        """
        async with self.lock:
            if self.state == CircuitBreakerState.OPEN:
                if time.time() - self.last_failure_time < self.recovery_timeout:
                    raise Exception("Circuit breaker is OPEN")
                else:
                    # Try half-open
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.success_count = 0

        try:
            result = await func(*args, **kwargs)
            await self._on_success()
            return result
        except Exception as e:
            await self._on_failure()
            raise e

    async def _on_success(self) -> None:
        """Handle successful request."""
        async with self.lock:
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.success_threshold:
                    self.state = CircuitBreakerState.CLOSED
                    self.failure_count = 0
            elif self.state == CircuitBreakerState.CLOSED:
                self.failure_count = 0

    async def _on_failure(self) -> None:
        """Handle failed request."""
        async with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()

            if self.failure_count >= self.failure_threshold:
                self.state = CircuitBreakerState.OPEN


class ProviderHealthMonitor:
    """Health monitoring for LLM providers."""

    def __init__(self) -> None:
        """Initialize health monitor."""
        self.provider_health: dict[str, ProviderHealth] = {}
        self.rate_limiters: dict[str, RateLimiter] = {}
        self.circuit_breakers: dict[str, CircuitBreaker] = {}

    def get_provider_key(self, provider: str, model: str) -> str:
        """Get unique key for provider/model combination."""
        return f"{provider}:{model}"

    def get_health(self, provider: str, model: str) -> ProviderHealth:
        """Get health status for provider/model."""
        key = self.get_provider_key(provider, model)
        if key not in self.provider_health:
            self.provider_health[key] = ProviderHealth(provider=provider, model=model)
        return self.provider_health[key]

    def get_rate_limiter(self, provider: str, requests_per_minute: int = 60) -> RateLimiter:
        """Get rate limiter for provider."""
        if provider not in self.rate_limiters:
            self.rate_limiters[provider] = RateLimiter(requests_per_minute)
        return self.rate_limiters[provider]

    def get_circuit_breaker(self, provider: str, model: str) -> CircuitBreaker:
        """Get circuit breaker for provider/model."""
        key = self.get_provider_key(provider, model)
        if key not in self.circuit_breakers:
            self.circuit_breakers[key] = CircuitBreaker()
        return self.circuit_breakers[key]

    async def record_request_start(self, provider: str, model: str) -> float:
        """Record start of request and return start time."""
        health = self.get_health(provider, model)
        health.total_requests += 1
        return time.time()

    async def record_request_success(
        self,
        provider: str,
        model: str,
        start_time: float
    ) -> None:
        """Record successful request."""
        health = self.get_health(provider, model)

        # Update success metrics
        health.successful_requests += 1
        health.consecutive_failures = 0
        health.last_success = datetime.now(UTC)
        health.is_healthy = True

        # Update response time
        response_time_ms = (time.time() - start_time) * 1000
        if health.avg_response_time_ms == 0:
            health.avg_response_time_ms = response_time_ms
        else:
            # Exponential moving average
            health.avg_response_time_ms = (
                0.9 * health.avg_response_time_ms + 0.1 * response_time_ms
            )

    async def record_request_failure(self, provider: str, model: str) -> None:
        """Record failed request."""
        health = self.get_health(provider, model)

        # Update failure metrics
        health.consecutive_failures += 1
        health.last_failure = datetime.now(UTC)

        # Mark as unhealthy if too many consecutive failures
        if health.consecutive_failures >= 3:
            health.is_healthy = False

    def get_healthy_providers(self) -> list[str]:
        """Get list of healthy providers."""
        healthy = []
        for _key, health in self.provider_health.items():
            if health.is_healthy:
                healthy.append(health.provider)
        return list(set(healthy))  # Remove duplicates

    def get_provider_stats(self) -> dict[str, dict[str, Any]]:
        """Get comprehensive provider statistics."""
        stats = {}

        for key, health in self.provider_health.items():
            success_rate = 0.0
            if health.total_requests > 0:
                success_rate = (health.successful_requests / health.total_requests) * 100

            stats[key] = {
                "provider": health.provider,
                "model": health.model,
                "is_healthy": health.is_healthy,
                "total_requests": health.total_requests,
                "successful_requests": health.successful_requests,
                "success_rate": round(success_rate, 2),
                "consecutive_failures": health.consecutive_failures,
                "avg_response_time_ms": round(health.avg_response_time_ms, 2),
                "last_success": health.last_success.isoformat() if health.last_success else None,
                "last_failure": health.last_failure.isoformat() if health.last_failure else None,
                "circuit_breaker_state": health.circuit_breaker_state.value
            }

        return stats


class SecurityManager:
    """Security features for LLM providers."""

    @staticmethod
    def sanitize_api_key(api_key: str) -> str:
        """Sanitize API key for logging."""
        if not api_key:
            return "None"

        if len(api_key) <= 8:
            return "*" * len(api_key)

        return api_key[:4] + "*" * (len(api_key) - 8) + api_key[-4:]

    @staticmethod
    def validate_api_key_format(provider: str, api_key: str) -> bool:
        """Validate API key format for provider."""
        if not api_key:
            return False

        # Provider-specific validation
        if provider.lower() == "openai":
            return api_key.startswith("sk-") and len(api_key) >= 20
        elif provider.lower() == "anthropic":
            return api_key.startswith("sk-ant-") and len(api_key) >= 20
        elif provider.lower() in ["gemini", "google"]:
            return len(api_key) >= 20  # Google API keys vary in format
        elif provider.lower() == "groq":
            return api_key.startswith("gsk_") and len(api_key) >= 20

        # Default validation for other providers
        return len(api_key) >= 10

    @staticmethod
    def is_test_key(api_key: str) -> bool:
        """Check if API key is a test/invalid key."""
        if not api_key:
            return True

        test_patterns = [
            "sk-test-",
            "test-",
            "demo-",
            "example-",
            "placeholder",
            "your-api-key",
            "insert-key-here"
        ]

        api_key_lower = api_key.lower()
        return any(pattern in api_key_lower for pattern in test_patterns)


# Global instances
health_monitor = ProviderHealthMonitor()
security_manager = SecurityManager()
