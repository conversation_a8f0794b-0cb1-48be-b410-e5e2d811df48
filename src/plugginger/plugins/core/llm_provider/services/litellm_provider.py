"""
LiteLLM-based provider implementation for unified LLM access.

This module provides a unified interface to 100+ LLM providers through LiteLLM,
replacing the legacy provider-specific implementations with a single, 
production-ready solution.
"""

from __future__ import annotations

import json
import logging
import os
import time
from typing import Any, Dict, List, Optional

from plugginger.core.exceptions import PluggingerConfigurationError
from .litellm_observability import observability

# LiteLLM import with fallback
try:
    import litellm
    from litellm import completion
    LITELLM_AVAILABLE = True
except ImportError:
    LITELLM_AVAILABLE = False
    litellm = None
    completion = None

logger = logging.getLogger(__name__)


class LiteLLMProvider:
    """
    Unified LLM provider using LiteLLM for 100+ provider support.
    
    This class replaces the legacy OpenAI/Gemini/Ollama provider implementations
    with a single, unified interface that supports all LiteLLM-compatible providers.
    
    Features:
    - 100+ LLM providers with unified API
    - Automatic retry and fallback logic
    - Built-in cost tracking and observability
    - Streaming support for all providers
    - Production-ready error handling
    """

    def __init__(
        self,
        provider: str | None = None,
        model: str | None = None,
        api_key: str | None = None,
        base_url: str | None = None,
        **kwargs: Any
    ) -> None:
        """
        Initialize LiteLLM provider.

        Args:
            provider: Provider name (openai, anthropic, gemini, ollama, etc.)
            model: Model name (gpt-4, claude-3-sonnet, gemini-pro, etc.)
            api_key: API key for the provider
            base_url: Custom base URL for the provider
            **kwargs: Additional provider-specific configuration
        """
        if not LITELLM_AVAILABLE:
            raise PluggingerConfigurationError(
                "LiteLLM is not available. Install with: pip install litellm"
            )

        self.provider = provider
        self.model = model
        self.api_key = api_key
        self.base_url = base_url
        self.config = kwargs

        # Set up LiteLLM configuration
        self._configure_litellm()

    def _configure_litellm(self) -> None:
        """Configure LiteLLM with provider-specific settings."""
        if not litellm:
            return

        # Set API keys in environment if provided
        if self.api_key and self.provider:
            env_key = self._get_env_key_name(self.provider)
            if env_key:
                os.environ[env_key] = self.api_key

        # Configure base URL if provided
        if self.base_url and self.provider:
            base_url_key = self._get_base_url_key(self.provider)
            if base_url_key:
                os.environ[base_url_key] = self.base_url

        # Set LiteLLM configuration
        litellm.set_verbose = False  # Reduce logging noise
        litellm.drop_params = True   # Drop unsupported parameters

    def _get_env_key_name(self, provider: str) -> str | None:
        """Get environment variable name for API key based on provider."""
        provider_env_map = {
            "openai": "OPENAI_API_KEY",
            "anthropic": "ANTHROPIC_API_KEY",
            "gemini": "GOOGLE_API_KEY",
            "google": "GOOGLE_API_KEY",
            "cohere": "COHERE_API_KEY",
            "huggingface": "HUGGINGFACE_API_KEY",
            "replicate": "REPLICATE_API_TOKEN",
            "together": "TOGETHER_API_KEY",
            "groq": "GROQ_API_KEY",
            "deepseek": "DEEPSEEK_API_KEY",
            "mistral": "MISTRAL_API_KEY",
            "perplexity": "PERPLEXITYAI_API_KEY",
        }
        return provider_env_map.get(provider.lower())

    def _get_base_url_key(self, provider: str) -> str | None:
        """Get environment variable name for base URL based on provider."""
        base_url_env_map = {
            "openai": "OPENAI_API_BASE",
            "anthropic": "ANTHROPIC_API_BASE",
            "ollama": "OLLAMA_API_BASE",
        }
        return base_url_env_map.get(provider.lower())

    def _format_model_name(self) -> str:
        """Format model name for LiteLLM based on provider."""
        if not self.model:
            raise PluggingerConfigurationError("Model name is required")

        if not self.provider:
            # If no provider specified, assume model name is already formatted
            return self.model

        # Format as provider/model for LiteLLM
        if "/" in self.model:
            # Already formatted
            return self.model
        else:
            # Add provider prefix
            return f"{self.provider}/{self.model}"

    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Generate text response using LiteLLM.

        Args:
            prompt: Input prompt
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional generation parameters

        Returns:
            Response dictionary with content, model, provider, tokens_used, etc.
        """
        if not LITELLM_AVAILABLE:
            logger.warning("LiteLLM not available, using mock response")
            return {
                "content": f"Mock response for prompt: {prompt[:50]}...",
                "model": self.model or "unknown",
                "provider": self.provider or "mock",
                "tokens_used": 100,
                "success": True,
                "finish_reason": "stop"
            }

        # Start observability tracking
        request_id = observability.start_request(
            self.provider or "unknown",
            self.model or "unknown",
            "completion"
        )
        start_time = time.time()

        try:
            model_name = self._format_model_name()

            # Prepare messages for chat completion
            messages = [{"role": "user", "content": prompt}]

            # Make LiteLLM completion call
            response = await completion(
                model=model_name,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )

            # Extract response data
            choice = response.choices[0]
            content = choice.message.content or ""

            result = {
                "content": content,
                "model": response.model,
                "provider": self.provider or "unknown",
                "tokens_used": response.usage.total_tokens if response.usage else 0,
                "success": True,
                "finish_reason": choice.finish_reason or "stop"
            }

            # End observability tracking
            observability.end_request(request_id, result, start_time, success=True)
            return result

        except Exception as e:
            logger.error(f"LiteLLM generation error: {e}")
            result = {
                "content": "",
                "model": self.model or "unknown",
                "provider": self.provider or "unknown",
                "tokens_used": 0,
                "success": False,
                "error": str(e),
                "finish_reason": "error"
            }

            # End observability tracking with error
            observability.end_request(
                request_id, result, start_time, success=False,
                error_info={"type": type(e).__name__, "message": str(e)}
            )
            return result

    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1,
        enable_streaming: bool = False,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Generate structured JSON response using LiteLLM.

        Args:
            system_message: System prompt
            user_message: User prompt
            ebnf_grammar: EBNF grammar for validation (maintained for compatibility)
            max_retries: Maximum retry attempts
            temperature: Sampling temperature
            enable_streaming: Whether to use streaming responses
            **kwargs: Additional generation parameters

        Returns:
            Response dictionary with validated JSON content
        """
        if not LITELLM_AVAILABLE:
            logger.warning("LiteLLM not available, using mock response")
            return {
                "content": '{"plugin_name": "test_plugin", "services": []}',
                "model": self.model or "unknown",
                "provider": self.provider or "mock",
                "tokens_used": 150,
                "success": True,
                "validated": True,
                "retries_used": 0
            }

        try:
            model_name = self._format_model_name()
            
            # Prepare messages
            messages = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": f"{user_message}\n\nRespond with valid JSON only."}
            ]

            # Use JSON mode for supported providers with enhanced options
            response_format = self._get_json_response_format()

            # Configure streaming if requested
            stream = enable_streaming and self._supports_streaming()

            retries_used = 0
            last_error = None

            for attempt in range(max_retries):
                try:
                    # Make LiteLLM completion call with enhanced parameters
                    completion_params = {
                        "model": model_name,
                        "messages": messages,
                        "max_tokens": kwargs.get("max_tokens", 2000),
                        "temperature": temperature,
                        "stream": stream,
                        **{k: v for k, v in kwargs.items() if k not in ["max_tokens", "enable_streaming"]}
                    }

                    # Add response format if supported
                    if response_format:
                        completion_params["response_format"] = response_format

                    if stream:
                        response = await self._handle_streaming_response(completion_params)
                    else:
                        response = await completion(**completion_params)

                    # Extract and validate JSON content
                    choice = response.choices[0]
                    content = choice.message.content or ""

                    # Validate JSON
                    try:
                        json.loads(content)
                        # JSON is valid
                        return {
                            "content": content,
                            "model": response.model,
                            "provider": self.provider or "unknown",
                            "tokens_used": response.usage.total_tokens if response.usage else 0,
                            "success": True,
                            "validated": True,
                            "retries_used": retries_used
                        }
                    except json.JSONDecodeError as json_error:
                        last_error = f"Invalid JSON: {json_error}"
                        retries_used += 1
                        logger.warning(f"JSON validation failed (attempt {attempt + 1}): {json_error}")
                        continue

                except Exception as e:
                    last_error = str(e)
                    retries_used += 1
                    logger.warning(f"Generation failed (attempt {attempt + 1}): {e}")
                    continue

            # All retries exhausted
            return {
                "content": "",
                "model": self.model or "unknown",
                "provider": self.provider or "unknown",
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": retries_used,
                "error": f"Max retries exceeded. Last error: {last_error}"
            }

        except Exception as e:
            logger.error(f"LiteLLM structured generation error: {e}")
            return {
                "content": "",
                "model": self.model or "unknown",
                "provider": self.provider or "unknown",
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": 0,
                "error": str(e)
            }

    def _get_json_response_format(self) -> Dict[str, Any] | None:
        """Get JSON response format configuration for the provider."""
        if not self.provider:
            return None

        provider_lower = self.provider.lower()

        # OpenAI and compatible providers
        if provider_lower in ["openai", "groq", "together", "deepseek", "mistral"]:
            return {"type": "json_object"}

        # Anthropic Claude
        elif provider_lower == "anthropic":
            return {"type": "json_object"}

        # Google Gemini
        elif provider_lower in ["gemini", "google"]:
            return {"type": "json_object"}

        # Other providers may not support structured output
        return None

    def _supports_streaming(self) -> bool:
        """Check if the provider supports streaming responses."""
        if not self.provider:
            return False

        # Most major providers support streaming
        streaming_providers = [
            "openai", "anthropic", "gemini", "google", "groq",
            "cohere", "together", "deepseek", "mistral", "perplexity"
        ]

        return self.provider.lower() in streaming_providers

    async def _handle_streaming_response(self, completion_params: Dict[str, Any]) -> Any:
        """Handle streaming response and collect full content."""
        if not LITELLM_AVAILABLE:
            # Mock streaming response
            return type('MockResponse', (), {
                'choices': [type('Choice', (), {
                    'message': type('Message', (), {'content': '{"mock": "streaming_response"}'})(),
                    'finish_reason': 'stop'
                })()],
                'model': self.model or 'mock',
                'usage': type('Usage', (), {'total_tokens': 150})()
            })()

        try:
            # Collect streaming chunks
            full_content = ""
            async for chunk in await completion(**completion_params):
                if chunk.choices and chunk.choices[0].delta.content:
                    full_content += chunk.choices[0].delta.content

            # Create response object similar to non-streaming
            return type('StreamResponse', (), {
                'choices': [type('Choice', (), {
                    'message': type('Message', (), {'content': full_content})(),
                    'finish_reason': 'stop'
                })()],
                'model': completion_params.get('model', 'unknown'),
                'usage': type('Usage', (), {'total_tokens': len(full_content.split())})()
            })()

        except Exception as e:
            logger.error(f"Streaming error: {e}")
            # Fallback to non-streaming
            completion_params["stream"] = False
            return await completion(**completion_params)

    async def generate_with_fallback(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        fallback_providers: list[str] | None = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Generate structured response with multi-provider fallback.

        Args:
            system_message: System prompt
            user_message: User prompt
            ebnf_grammar: EBNF grammar for validation
            fallback_providers: List of provider names to try as fallbacks
            **kwargs: Additional generation parameters

        Returns:
            Response dictionary with fallback information
        """
        # Try primary provider first
        result = await self.generate_structured(
            system_message, user_message, ebnf_grammar, **kwargs
        )

        if result["success"]:
            result["fallback_used"] = False
            result["provider_used"] = self.provider
            return result

        # Try fallback providers if specified
        if fallback_providers and LITELLM_AVAILABLE:
            for fallback_provider in fallback_providers:
                try:
                    logger.info(f"Trying fallback provider: {fallback_provider}")

                    # Create temporary provider for fallback
                    fallback = LiteLLMProvider(
                        provider=fallback_provider,
                        model=self._get_fallback_model(fallback_provider),
                        api_key=self._get_fallback_api_key(fallback_provider)
                    )

                    fallback_result = await fallback.generate_structured(
                        system_message, user_message, ebnf_grammar, **kwargs
                    )

                    if fallback_result["success"]:
                        fallback_result["fallback_used"] = True
                        fallback_result["provider_used"] = fallback_provider
                        fallback_result["primary_provider"] = self.provider
                        return fallback_result

                except Exception as e:
                    logger.warning(f"Fallback provider {fallback_provider} failed: {e}")
                    continue

        # All providers failed
        result["fallback_used"] = False
        result["provider_used"] = self.provider
        result["fallback_attempts"] = len(fallback_providers) if fallback_providers else 0
        return result

    def _get_fallback_model(self, provider: str) -> str:
        """Get default model for fallback provider."""
        fallback_models = {
            "openai": "gpt-4o-mini",
            "anthropic": "claude-3-haiku-20240307",
            "gemini": "gemini-1.5-flash",
            "groq": "llama-3.1-8b-instant",
            "ollama": "llama3.2"
        }
        return fallback_models.get(provider.lower(), "default")

    def _get_fallback_api_key(self, provider: str) -> str | None:
        """Get API key for fallback provider from environment."""
        env_mappings = {
            "openai": "OPENAI_API_KEY",
            "anthropic": "ANTHROPIC_API_KEY",
            "gemini": "GOOGLE_API_KEY",
            "google": "GOOGLE_API_KEY",
            "groq": "GROQ_API_KEY"
        }
        env_key = env_mappings.get(provider.lower())
        return os.getenv(env_key) if env_key else None

    def get_info(self) -> Dict[str, Any]:
        """Get provider information."""
        return {
            "provider": self.provider or "auto-detected",
            "model": self.model or "default",
            "base_url": self.base_url,
            "has_api_key": bool(self.api_key),
            "litellm_available": LITELLM_AVAILABLE,
            "supports_streaming": self._supports_streaming(),
            "supports_json_mode": self._get_json_response_format() is not None,
            "supported_features": [
                "text_generation",
                "structured_generation",
                "streaming",
                "json_mode",
                "multi_provider_fallback",
                "cost_tracking",
                "retry_logic"
            ]
        }
