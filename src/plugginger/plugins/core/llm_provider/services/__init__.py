"""Services for LLM Provider Core Plugin."""

# LiteLLM-based implementation (S5.4 + S5.5 + S5.6)
from .litellm_factory import LiteLLMProviderFactory
from .litellm_observability import LiteLLMObservability, observability
from .litellm_production import (
    CircuitBreaker,
    ProviderHealthMonitor,
    RateLimiter,
    SecurityManager,
    health_monitor,
    security_manager,
)
from .litellm_provider import LiteLLMProvider
from .validation_service import ResponseValidationService

# Backward compatibility aliases
LLMProvider = LiteLLMProvider  # Legacy alias
ProviderFactory = LiteLLMProviderFactory  # Legacy alias

# All exports (unified LiteLLM-based with production features)
__all__ = [
    "LiteLLMProvider",
    "LiteLLMProviderFactory",
    "LiteLLMObservability",
    "observability",
    "ProviderHealthMonitor",
    "RateLimiter",
    "CircuitBreaker",
    "SecurityManager",
    "health_monitor",
    "security_manager",
    "ResponseValidationService",
    # Legacy aliases for backward compatibility
    "LLMProvider",
    "ProviderFactory"
]
