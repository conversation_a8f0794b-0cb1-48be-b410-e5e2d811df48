"""Services for LLM Provider Core Plugin."""

# New LiteLLM-based implementation
from .litellm_provider import LiteLLMProvider
from .litellm_factory import Lite<PERSON><PERSON>roviderFactory

# Legacy provider imports for backward compatibility
try:
    from .provider_service import LLMProvider, ProviderFactory
    LEGACY_PROVIDERS_AVAILABLE = True
except ImportError:
    # Legacy providers not available (removed in S5.6)
    LEGACY_PROVIDERS_AVAILABLE = False
    LLMProvider = None
    ProviderFactory = None

from .validation_service import ResponseValidationService

# Primary exports (new LiteLLM-based)
__all__ = [
    "LiteLLMProvider",
    "LiteLLMProviderFactory",
    "ResponseValidationService",
]

# Legacy exports for backward compatibility
if LEGACY_PROVIDERS_AVAILABLE:
    __all__.extend([
        "LLMProvider",
        "ProviderFactory"
    ])
