"""
LiteLLM Observability and Cost Tracking.

This module provides observability features for LiteLLM providers including
cost tracking, performance monitoring, and usage analytics.
"""

from __future__ import annotations

import time
import logging
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, field
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


@dataclass
class LLMUsageMetrics:
    """Metrics for LLM usage tracking."""
    
    provider: str
    model: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Token usage
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    
    # Performance metrics
    response_time_ms: float = 0.0
    success: bool = True
    
    # Cost tracking (estimated)
    estimated_cost_usd: float = 0.0
    
    # Request details
    request_type: str = "completion"  # completion, structured, streaming
    temperature: float = 0.1
    max_tokens: int = 1000
    
    # Error information
    error_type: str | None = None
    error_message: str | None = None
    
    # Fallback information
    fallback_used: bool = False
    fallback_provider: str | None = None
    retry_count: int = 0


class LiteLLMObservability:
    """
    Observability and monitoring for LiteLLM providers.
    
    Provides cost tracking, performance monitoring, and usage analytics
    for all LiteLLM-based provider interactions.
    """
    
    def __init__(self) -> None:
        """Initialize observability tracker."""
        self.metrics: List[LLMUsageMetrics] = []
        self.session_start = datetime.now(timezone.utc)
        
        # Cost per token estimates (USD per 1K tokens)
        self.cost_estimates = {
            "openai": {
                "gpt-4": {"input": 0.03, "output": 0.06},
                "gpt-4o": {"input": 0.005, "output": 0.015},
                "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
                "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
            },
            "anthropic": {
                "claude-3-opus": {"input": 0.015, "output": 0.075},
                "claude-3-sonnet": {"input": 0.003, "output": 0.015},
                "claude-3-haiku": {"input": 0.00025, "output": 0.00125},
            },
            "gemini": {
                "gemini-1.5-pro": {"input": 0.0035, "output": 0.0105},
                "gemini-1.5-flash": {"input": 0.000075, "output": 0.0003},
            },
            "groq": {
                "llama-3.1-70b-versatile": {"input": 0.00059, "output": 0.00079},
                "llama-3.1-8b-instant": {"input": 0.00005, "output": 0.00008},
            },
            "ollama": {
                # Local models - no cost
                "default": {"input": 0.0, "output": 0.0}
            }
        }

    def start_request(self, provider: str, model: str, request_type: str = "completion") -> str:
        """
        Start tracking a new request.
        
        Args:
            provider: Provider name
            model: Model name
            request_type: Type of request (completion, structured, streaming)
            
        Returns:
            Request ID for tracking
        """
        request_id = f"{provider}_{model}_{int(time.time() * 1000)}"
        
        # Create initial metrics entry
        metrics = LLMUsageMetrics(
            provider=provider,
            model=model,
            request_type=request_type,
            timestamp=datetime.now(timezone.utc)
        )
        
        self.metrics.append(metrics)
        
        logger.debug(f"Started tracking request {request_id}")
        return request_id

    def end_request(
        self,
        request_id: str,
        response_data: Dict[str, Any],
        start_time: float,
        success: bool = True,
        error_info: Dict[str, Any] | None = None
    ) -> None:
        """
        End tracking for a request and update metrics.
        
        Args:
            request_id: Request ID from start_request
            response_data: Response data from LLM
            start_time: Request start time (from time.time())
            success: Whether request was successful
            error_info: Error information if request failed
        """
        # Find the metrics entry
        metrics = None
        for m in reversed(self.metrics):
            if f"{m.provider}_{m.model}" in request_id:
                metrics = m
                break
        
        if not metrics:
            logger.warning(f"Could not find metrics for request {request_id}")
            return
        
        # Update performance metrics
        metrics.response_time_ms = (time.time() - start_time) * 1000
        metrics.success = success
        
        if success and response_data:
            # Extract token usage
            metrics.total_tokens = response_data.get("tokens_used", 0)
            
            # Estimate prompt/completion tokens (rough approximation)
            if metrics.total_tokens > 0:
                # Assume 70% prompt, 30% completion for structured generation
                if metrics.request_type == "structured":
                    metrics.prompt_tokens = int(metrics.total_tokens * 0.7)
                    metrics.completion_tokens = int(metrics.total_tokens * 0.3)
                else:
                    # For regular completion, assume 50/50 split
                    metrics.prompt_tokens = int(metrics.total_tokens * 0.5)
                    metrics.completion_tokens = int(metrics.total_tokens * 0.5)
            
            # Calculate estimated cost
            metrics.estimated_cost_usd = self._calculate_cost(
                metrics.provider, 
                metrics.model,
                metrics.prompt_tokens,
                metrics.completion_tokens
            )
            
            # Track fallback usage
            metrics.fallback_used = response_data.get("fallback_used", False)
            metrics.fallback_provider = response_data.get("provider_used")
            metrics.retry_count = response_data.get("retries_used", 0)
        
        if error_info:
            metrics.error_type = error_info.get("type", "unknown")
            metrics.error_message = error_info.get("message", "")
        
        logger.debug(f"Completed tracking for request {request_id}")

    def _calculate_cost(
        self, 
        provider: str, 
        model: str, 
        prompt_tokens: int, 
        completion_tokens: int
    ) -> float:
        """Calculate estimated cost for token usage."""
        provider_costs = self.cost_estimates.get(provider.lower(), {})
        
        # Try exact model match first
        model_costs = provider_costs.get(model.lower())
        
        # If no exact match, try to find a similar model
        if not model_costs:
            for cost_model, costs in provider_costs.items():
                if cost_model in model.lower() or model.lower() in cost_model:
                    model_costs = costs
                    break
        
        # Default to first available model for provider
        if not model_costs and provider_costs:
            model_costs = next(iter(provider_costs.values()))
        
        if not model_costs:
            return 0.0
        
        # Calculate cost (rates are per 1K tokens)
        input_cost = (prompt_tokens / 1000) * model_costs.get("input", 0.0)
        output_cost = (completion_tokens / 1000) * model_costs.get("output", 0.0)
        
        return input_cost + output_cost

    def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of current session metrics."""
        if not self.metrics:
            return {
                "total_requests": 0,
                "total_cost_usd": 0.0,
                "total_tokens": 0,
                "avg_response_time_ms": 0.0,
                "success_rate": 0.0,
                "providers_used": [],
                "session_duration_minutes": 0.0
            }
        
        successful_requests = [m for m in self.metrics if m.success]
        
        # Calculate aggregated metrics
        total_cost = sum(m.estimated_cost_usd for m in self.metrics)
        total_tokens = sum(m.total_tokens for m in self.metrics)
        avg_response_time = sum(m.response_time_ms for m in self.metrics) / len(self.metrics)
        success_rate = len(successful_requests) / len(self.metrics) * 100
        
        providers_used = list(set(m.provider for m in self.metrics))
        
        session_duration = (datetime.now(timezone.utc) - self.session_start).total_seconds() / 60
        
        return {
            "total_requests": len(self.metrics),
            "successful_requests": len(successful_requests),
            "total_cost_usd": round(total_cost, 6),
            "total_tokens": total_tokens,
            "avg_response_time_ms": round(avg_response_time, 2),
            "success_rate": round(success_rate, 2),
            "providers_used": providers_used,
            "session_duration_minutes": round(session_duration, 2),
            "fallback_usage_rate": round(
                len([m for m in self.metrics if m.fallback_used]) / len(self.metrics) * 100, 2
            )
        }

    def get_provider_breakdown(self) -> Dict[str, Dict[str, Any]]:
        """Get metrics breakdown by provider."""
        breakdown = {}
        
        for metrics in self.metrics:
            provider = metrics.provider
            if provider not in breakdown:
                breakdown[provider] = {
                    "requests": 0,
                    "successful_requests": 0,
                    "total_cost_usd": 0.0,
                    "total_tokens": 0,
                    "avg_response_time_ms": 0.0,
                    "models_used": set()
                }
            
            provider_data = breakdown[provider]
            provider_data["requests"] += 1
            if metrics.success:
                provider_data["successful_requests"] += 1
            provider_data["total_cost_usd"] += metrics.estimated_cost_usd
            provider_data["total_tokens"] += metrics.total_tokens
            provider_data["models_used"].add(metrics.model)
        
        # Calculate averages and convert sets to lists
        for provider, data in breakdown.items():
            provider_metrics = [m for m in self.metrics if m.provider == provider]
            if provider_metrics:
                data["avg_response_time_ms"] = round(
                    sum(m.response_time_ms for m in provider_metrics) / len(provider_metrics), 2
                )
            data["models_used"] = list(data["models_used"])
            data["total_cost_usd"] = round(data["total_cost_usd"], 6)
            data["success_rate"] = round(
                data["successful_requests"] / data["requests"] * 100, 2
            ) if data["requests"] > 0 else 0.0
        
        return breakdown

    def export_metrics(self, format: str = "dict") -> Any:
        """
        Export metrics in specified format.
        
        Args:
            format: Export format (dict, json, csv)
            
        Returns:
            Metrics in requested format
        """
        if format == "dict":
            return [
                {
                    "timestamp": m.timestamp.isoformat(),
                    "provider": m.provider,
                    "model": m.model,
                    "request_type": m.request_type,
                    "success": m.success,
                    "response_time_ms": m.response_time_ms,
                    "total_tokens": m.total_tokens,
                    "estimated_cost_usd": m.estimated_cost_usd,
                    "fallback_used": m.fallback_used,
                    "retry_count": m.retry_count,
                    "error_type": m.error_type
                }
                for m in self.metrics
            ]
        elif format == "json":
            import json
            return json.dumps(self.export_metrics("dict"), indent=2)
        elif format == "csv":
            import csv
            import io
            
            output = io.StringIO()
            if self.metrics:
                fieldnames = [
                    "timestamp", "provider", "model", "request_type", "success",
                    "response_time_ms", "total_tokens", "estimated_cost_usd",
                    "fallback_used", "retry_count", "error_type"
                ]
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()
                for row in self.export_metrics("dict"):
                    writer.writerow(row)
            
            return output.getvalue()
        else:
            raise ValueError(f"Unsupported export format: {format}")

    def clear_metrics(self) -> None:
        """Clear all collected metrics."""
        self.metrics.clear()
        self.session_start = datetime.now(timezone.utc)
        logger.info("Cleared all observability metrics")


# Global observability instance
observability = LiteLLMObservability()
