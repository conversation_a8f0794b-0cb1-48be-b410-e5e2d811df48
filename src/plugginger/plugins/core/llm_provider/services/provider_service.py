"""
LLM Provider Service.

Provides unified interface for different LLM providers with environment-based configuration.
"""

import asyncio
import json
import logging
import os
from abc import ABC, abstractmethod
from typing import Any

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""

    def __init__(self, api_key: str, model: str | None = None, base_url: str | None = None) -> None:
        """Initialize LLM provider.

        Args:
            api_key: API key for the LLM service
            model: Model name (uses provider default if None)
            base_url: Base URL for API (uses provider default if None)
        """
        self.api_key = api_key
        self.model = model or self.default_model
        self.base_url = base_url or self.default_base_url

    @property
    @abstractmethod
    def default_model(self) -> str:
        """Default model for this provider."""
        pass

    @property
    @abstractmethod
    def default_base_url(self) -> str:
        """Default base URL for this provider."""
        pass

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Name of this provider."""
        pass

    @abstractmethod
    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate text response from LLM.

        Args:
            prompt: Input prompt
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature

        Returns:
            Response dictionary with content and metadata
        """
        pass

    @abstractmethod
    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate structured response using EBNF grammar constraints.

        Args:
            system_message: System prompt
            user_message: User prompt
            ebnf_grammar: EBNF grammar for validation
            max_retries: Maximum retry attempts
            temperature: Sampling temperature

        Returns:
            Response dictionary with validated content
        """
        pass

    def get_info(self) -> dict[str, Any]:
        """Get provider information.

        Returns:
            Provider information dictionary
        """
        return {
            "provider": self.provider_name,
            "model": self.model,
            "base_url": self.base_url,
            "has_api_key": bool(self.api_key)
        }


class OpenAIProvider(LLMProvider):
    """OpenAI GPT provider implementation."""

    @property
    def default_model(self) -> str:
        return "gpt-4"

    @property
    def default_base_url(self) -> str:
        return "https://api.openai.com/v1"

    @property
    def provider_name(self) -> str:
        return "openai"

    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate text response using OpenAI API."""
        if not OPENAI_AVAILABLE:
            logger.warning("OpenAI library not available, using mock response")
            prompt_preview = str(prompt)[:50] if len(str(prompt)) > 50 else str(prompt)
            return {
                "content": f"Mock response for prompt: {prompt_preview}...",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 100,
                "success": True,
                "finish_reason": "stop"
            }

        logger.info(f"Generating text with OpenAI {self.model}")

        try:
            # Create OpenAI client
            client = openai.AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url if self.base_url else None
            )

            # Make API call
            response = await client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )

            # Extract response data
            choice = response.choices[0]
            content = choice.message.content or ""

            return {
                "content": content,
                "model": response.model,
                "provider": self.provider_name,
                "tokens_used": response.usage.total_tokens if response.usage else 0,
                "success": True,
                "finish_reason": choice.finish_reason or "stop"
            }

        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "error": str(e),
                "finish_reason": "error"
            }

    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate structured response using OpenAI API."""
        if not OPENAI_AVAILABLE:
            logger.warning("OpenAI library not available, using mock response")
            return {
                "content": '{"plugin_name": "test_plugin", "services": []}',
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 150,
                "success": True,
                "validated": True,
                "retries_used": 0
            }

        logger.info(f"Generating structured response with OpenAI {self.model}")

        try:
            # Create OpenAI client
            client = openai.AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url if self.base_url else None
            )

            # Construct prompt with EBNF grammar instructions
            structured_prompt = f"""
{user_message}

Please respond with valid JSON that follows this EBNF grammar:
{ebnf_grammar}

Respond only with the JSON, no additional text.
"""

            retries_used = 0
            last_error = None

            for attempt in range(max_retries):
                try:
                    # Make API call
                    response = await client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {"role": "system", "content": system_message},
                            {"role": "user", "content": structured_prompt}
                        ],
                        max_tokens=1000,
                        temperature=temperature,
                        response_format={"type": "json_object"}  # Force JSON output
                    )

                    # Extract response data
                    choice = response.choices[0]
                    content = choice.message.content or ""

                    # Try to parse JSON to validate
                    try:
                        json.loads(content)
                        json_valid = True
                    except json.JSONDecodeError:
                        json_valid = False

                    if json_valid:
                        return {
                            "content": content,
                            "model": response.model,
                            "provider": self.provider_name,
                            "tokens_used": response.usage.total_tokens if response.usage else 0,
                            "success": True,
                            "validated": True,
                            "retries_used": retries_used,
                            "finish_reason": choice.finish_reason or "stop"
                        }
                    else:
                        retries_used += 1
                        last_error = "Invalid JSON response"
                        if attempt < max_retries - 1:
                            await asyncio.sleep(0.5)  # Brief delay before retry

                except Exception as e:
                    retries_used += 1
                    last_error = str(e)
                    logger.warning(f"OpenAI API attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(0.5)  # Brief delay before retry

            # All retries failed
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": retries_used,
                "error": f"Failed after {max_retries} attempts: {last_error}",
                "finish_reason": "error"
            }

        except Exception as e:
            logger.error(f"OpenAI structured generation error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": 0,
                "error": str(e),
                "finish_reason": "error"
            }


class GeminiProvider(LLMProvider):
    """Google Gemini provider implementation."""

    @property
    def default_model(self) -> str:
        return "gemini-1.5-flash"

    @property
    def default_base_url(self) -> str:
        return "https://generativelanguage.googleapis.com"

    @property
    def provider_name(self) -> str:
        return "gemini"

    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate text response using Google Gemini API."""
        logger.info(f"Generating text with Gemini {self.model}")

        try:
            import google.generativeai as genai

            # Configure API key
            genai.configure(api_key=self.api_key)

            # Create model
            model = genai.GenerativeModel(self.model)

            # Configure generation
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=max_tokens,
                temperature=temperature
            )

            # Make API call
            response = await model.generate_content_async(
                prompt,
                generation_config=generation_config
            )

            # Extract response data
            content = response.text if response.text else ""

            return {
                "content": content,
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": response.usage_metadata.total_token_count if response.usage_metadata else 0,
                "success": True,
                "finish_reason": "stop"
            }

        except ImportError:
            logger.warning("Google GenerativeAI library not available, using mock response")
            prompt_preview = str(prompt)[:50] if len(str(prompt)) > 50 else str(prompt)
            return {
                "content": f"Mock response for prompt: {prompt_preview}...",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 120,
                "success": True,
                "finish_reason": "stop"
            }
        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "error": str(e),
                "finish_reason": "error"
            }

    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate structured response using Google Gemini API."""
        logger.info(f"Generating structured response with Gemini {self.model}")

        try:
            import google.generativeai as genai

            # Configure API key
            genai.configure(api_key=self.api_key)

            # Create model
            model = genai.GenerativeModel(self.model)

            # Construct prompt with EBNF grammar instructions
            structured_prompt = f"""
{system_message}

{user_message}

Please respond with valid JSON that follows this EBNF grammar:
{ebnf_grammar}

Respond only with the JSON, no additional text.
"""

            retries_used = 0
            last_error = None

            for attempt in range(max_retries):
                try:
                    # Configure generation
                    generation_config = genai.types.GenerationConfig(
                        max_output_tokens=1000,
                        temperature=temperature
                    )

                    # Make API call
                    response = await model.generate_content_async(
                        structured_prompt,
                        generation_config=generation_config
                    )

                    # Extract response data
                    content = response.text if response.text else ""

                    # Try to parse JSON to validate
                    try:
                        json.loads(content)
                        json_valid = True
                    except json.JSONDecodeError:
                        json_valid = False

                    if json_valid:
                        return {
                            "content": content,
                            "model": self.model,
                            "provider": self.provider_name,
                            "tokens_used": response.usage_metadata.total_token_count if response.usage_metadata else 0,
                            "success": True,
                            "validated": True,
                            "retries_used": retries_used,
                            "finish_reason": "stop"
                        }
                    else:
                        retries_used += 1
                        last_error = "Invalid JSON response"
                        if attempt < max_retries - 1:
                            await asyncio.sleep(0.5)  # Brief delay before retry

                except Exception as e:
                    retries_used += 1
                    last_error = str(e)
                    logger.warning(f"Gemini API attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(0.5)  # Brief delay before retry

            # All retries failed
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": retries_used,
                "error": f"Failed after {max_retries} attempts: {last_error}",
                "finish_reason": "error"
            }

        except ImportError:
            logger.warning("Google GenerativeAI library not available, using mock response")
            return {
                "content": '{"plugin_name": "test_plugin", "services": []}',
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 180,
                "success": True,
                "validated": True,
                "retries_used": 0
            }
        except Exception as e:
            logger.error(f"Gemini structured generation error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": 0,
                "error": str(e),
                "finish_reason": "error"
            }


class OllamaProvider(LLMProvider):
    """Ollama local LLM provider."""

    @property
    def default_model(self) -> str:
        return "llama3.2"

    @property
    def default_base_url(self) -> str:
        return "http://localhost:11434"

    @property
    def provider_name(self) -> str:
        return "ollama"

    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate text response using Ollama API."""
        logger.info(f"Generating text with Ollama {self.model}")

        try:
            import aiohttp

            # Prepare request data
            request_data = {
                "model": self.model,
                "prompt": prompt,
                "options": {
                    "num_predict": max_tokens,
                    "temperature": temperature
                },
                "stream": False
            }

            # Make API call
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=request_data,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        result = await response.json()

                        return {
                            "content": result.get("response", ""),
                            "model": self.model,
                            "provider": self.provider_name,
                            "tokens_used": result.get("eval_count", 0) + result.get("prompt_eval_count", 0),
                            "success": True,
                            "finish_reason": "stop" if result.get("done", False) else "length"
                        }
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ollama API error {response.status}: {error_text}")

        except ImportError:
            logger.warning("aiohttp library not available, using mock response")
            prompt_preview = str(prompt)[:50] if len(str(prompt)) > 50 else str(prompt)
            return {
                "content": f"Mock response for prompt: {prompt_preview}...",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 80,
                "success": True,
                "finish_reason": "stop"
            }
        except Exception as e:
            logger.error(f"Ollama API error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "error": str(e),
                "finish_reason": "error"
            }

    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate structured response using Ollama API with JSON format."""
        logger.info(f"Generating structured response with Ollama {self.model}")

        try:
            import aiohttp

            # Construct prompt with EBNF grammar instructions
            structured_prompt = f"""
{system_message}

{user_message}

Please respond with valid JSON that follows this EBNF grammar:
{ebnf_grammar}

Respond only with the JSON, no additional text.
"""

            retries_used = 0
            last_error = None

            for attempt in range(max_retries):
                try:
                    # Prepare request data
                    request_data = {
                        "model": self.model,
                        "prompt": structured_prompt,
                        "options": {
                            "num_predict": 1000,
                            "temperature": temperature
                        },
                        "format": "json",  # Request JSON format
                        "stream": False
                    }

                    # Make API call
                    async with aiohttp.ClientSession() as session:
                        async with session.post(
                            f"{self.base_url}/api/generate",
                            json=request_data,
                            timeout=aiohttp.ClientTimeout(total=60)
                        ) as response:
                            if response.status == 200:
                                result = await response.json()
                                content = result.get("response", "")

                                # Try to parse JSON to validate
                                try:
                                    json.loads(content)
                                    json_valid = True
                                except json.JSONDecodeError:
                                    json_valid = False

                                if json_valid:
                                    return {
                                        "content": content,
                                        "model": self.model,
                                        "provider": self.provider_name,
                                        "tokens_used": result.get("eval_count", 0) + result.get("prompt_eval_count", 0),
                                        "success": True,
                                        "validated": True,
                                        "retries_used": retries_used,
                                        "finish_reason": "stop" if result.get("done", False) else "length"
                                    }
                                else:
                                    retries_used += 1
                                    last_error = "Invalid JSON response"
                                    if attempt < max_retries - 1:
                                        await asyncio.sleep(0.5)  # Brief delay before retry
                            else:
                                error_text = await response.text()
                                raise Exception(f"Ollama API error {response.status}: {error_text}")

                except Exception as e:
                    retries_used += 1
                    last_error = str(e)
                    logger.warning(f"Ollama API attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(0.5)  # Brief delay before retry

            # All retries failed
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": retries_used,
                "error": f"Failed after {max_retries} attempts: {last_error}",
                "finish_reason": "error"
            }

        except ImportError:
            logger.warning("aiohttp library not available, using mock response")
            return {
                "content": '{"plugin_name": "test_plugin", "services": []}',
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 90,
                "success": True,
                "validated": True,
                "retries_used": 0
            }
        except Exception as e:
            logger.error(f"Ollama structured generation error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": 0,
                "error": str(e),
                "finish_reason": "error"
            }


class ProviderFactory:
    """Factory for creating LLM providers."""

    @staticmethod
    def create_provider(
        provider_type: str,
        api_key: str | None = None,
        model: str | None = None,
        base_url: str | None = None
    ) -> LLMProvider:
        """Create LLM provider instance.

        Args:
            provider_type: Provider type (openai, anthropic, local)
            api_key: API key for the provider
            model: Model name (optional)
            base_url: Base URL (optional)

        Returns:
            Configured LLM provider instance

        Raises:
            ValueError: If provider type is unsupported
        """
        provider_type_lower = provider_type.lower()

        if provider_type_lower == "openai":
            return OpenAIProvider(
                api_key=api_key or "",
                model=model,
                base_url=base_url
            )
        elif provider_type_lower == "gemini":
            return GeminiProvider(
                api_key=api_key or "",
                model=model,
                base_url=base_url
            )
        elif provider_type_lower == "ollama":
            return OllamaProvider(
                api_key=api_key or "",
                model=model,
                base_url=base_url
            )
        else:
            raise ValueError(
                f"Unsupported LLM provider: {provider_type}. "
                f"Supported providers: openai, gemini, ollama"
            )

    @staticmethod
    def create_from_env() -> LLMProvider:
        """Create LLM provider from environment variables.

        Environment Variables (Plugginger-specific):
            PLUGGINGER_LLM_PROVIDER: Provider type (openai, gemini, ollama)
            PLUGGINGER_LLM_API_KEY: API key for the provider
            PLUGGINGER_LLM_MODEL: Model name (optional)
            PLUGGINGER_LLM_BASE_URL: Base URL (optional)

        Environment Variables (Standard):
            OPENAI_API_KEY: OpenAI API key (fallback)
            GOOGLE_API_KEY: Google Gemini API key (fallback)

        Returns:
            Configured LLM provider instance

        Raises:
            ValueError: If required environment variables are missing
        """
        # Try Plugginger-specific variables first
        provider_type = os.getenv("PLUGGINGER_LLM_PROVIDER")
        api_key = os.getenv("PLUGGINGER_LLM_API_KEY")
        model = os.getenv("PLUGGINGER_LLM_MODEL")
        base_url = os.getenv("PLUGGINGER_LLM_BASE_URL")

        # Fallback to standard environment variables
        if not provider_type:
            if os.getenv("OPENAI_API_KEY"):
                provider_type = "openai"
                api_key = api_key or os.getenv("OPENAI_API_KEY")
                model = model or "gpt-4o-mini"  # Default to available model
            elif os.getenv("GOOGLE_API_KEY"):
                provider_type = "gemini"
                api_key = api_key or os.getenv("GOOGLE_API_KEY")
                model = model or "gemini-1.5-flash"

        if not provider_type:
            raise ValueError(
                "No LLM provider configured. Set PLUGGINGER_LLM_PROVIDER or provide "
                "OPENAI_API_KEY/GOOGLE_API_KEY environment variables."
            )

        if not api_key and provider_type.lower() != "ollama":
            raise ValueError(
                f"API key required for {provider_type} provider. "
                f"Set PLUGGINGER_LLM_API_KEY or {provider_type.upper()}_API_KEY."
            )

        return ProviderFactory.create_provider(
            provider_type=provider_type,
            api_key=api_key,
            model=model,
            base_url=base_url
        )
