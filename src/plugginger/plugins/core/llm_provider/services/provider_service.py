"""
LLM Provider Service.

Provides unified interface for different LLM providers with environment-based configuration.
"""

import asyncio
import json
import logging
import os
from abc import ABC, abstractmethod
from typing import Any

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers."""

    def __init__(self, api_key: str, model: str | None = None, base_url: str | None = None) -> None:
        """Initialize LLM provider.

        Args:
            api_key: API key for the LLM service
            model: Model name (uses provider default if None)
            base_url: Base URL for API (uses provider default if None)
        """
        self.api_key = api_key
        self.model = model or self.default_model
        self.base_url = base_url or self.default_base_url

    @property
    @abstractmethod
    def default_model(self) -> str:
        """Default model for this provider."""
        pass

    @property
    @abstractmethod
    def default_base_url(self) -> str:
        """Default base URL for this provider."""
        pass

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Name of this provider."""
        pass

    @abstractmethod
    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate text response from LLM.

        Args:
            prompt: Input prompt
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature

        Returns:
            Response dictionary with content and metadata
        """
        pass

    @abstractmethod
    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate structured response using EBNF grammar constraints.

        Args:
            system_message: System prompt
            user_message: User prompt
            ebnf_grammar: EBNF grammar for validation
            max_retries: Maximum retry attempts
            temperature: Sampling temperature

        Returns:
            Response dictionary with validated content
        """
        pass

    def get_info(self) -> dict[str, Any]:
        """Get provider information.

        Returns:
            Provider information dictionary
        """
        return {
            "provider": self.provider_name,
            "model": self.model,
            "base_url": self.base_url,
            "has_api_key": bool(self.api_key)
        }


class OpenAIProvider(LLMProvider):
    """OpenAI GPT provider implementation."""

    @property
    def default_model(self) -> str:
        return "gpt-4"

    @property
    def default_base_url(self) -> str:
        return "https://api.openai.com/v1"

    @property
    def provider_name(self) -> str:
        return "openai"

    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate text response using OpenAI API."""
        if not OPENAI_AVAILABLE:
            logger.warning("OpenAI library not available, using mock response")
            prompt_preview = str(prompt)[:50] if len(str(prompt)) > 50 else str(prompt)
            return {
                "content": f"Mock response for prompt: {prompt_preview}...",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 100,
                "success": True,
                "finish_reason": "stop"
            }

        logger.info(f"Generating text with OpenAI {self.model}")

        try:
            # Create OpenAI client
            client = openai.AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url if self.base_url else None
            )

            # Make API call
            response = await client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )

            # Extract response data
            choice = response.choices[0]
            content = choice.message.content or ""

            return {
                "content": content,
                "model": response.model,
                "provider": self.provider_name,
                "tokens_used": response.usage.total_tokens if response.usage else 0,
                "success": True,
                "finish_reason": choice.finish_reason or "stop"
            }

        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "error": str(e),
                "finish_reason": "error"
            }

    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate structured response using OpenAI API."""
        if not OPENAI_AVAILABLE:
            logger.warning("OpenAI library not available, using mock response")
            return {
                "content": '{"plugin_name": "test_plugin", "services": []}',
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 150,
                "success": True,
                "validated": True,
                "retries_used": 0
            }

        logger.info(f"Generating structured response with OpenAI {self.model}")

        try:
            # Create OpenAI client
            client = openai.AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url if self.base_url else None
            )

            # Construct prompt with EBNF grammar instructions
            structured_prompt = f"""
{user_message}

Please respond with valid JSON that follows this EBNF grammar:
{ebnf_grammar}

Respond only with the JSON, no additional text.
"""

            retries_used = 0
            last_error = None

            for attempt in range(max_retries):
                try:
                    # Make API call
                    response = await client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {"role": "system", "content": system_message},
                            {"role": "user", "content": structured_prompt}
                        ],
                        max_tokens=1000,
                        temperature=temperature,
                        response_format={"type": "json_object"}  # Force JSON output
                    )

                    # Extract response data
                    choice = response.choices[0]
                    content = choice.message.content or ""

                    # Try to parse JSON to validate
                    try:
                        json.loads(content)
                        json_valid = True
                    except json.JSONDecodeError:
                        json_valid = False

                    if json_valid:
                        return {
                            "content": content,
                            "model": response.model,
                            "provider": self.provider_name,
                            "tokens_used": response.usage.total_tokens if response.usage else 0,
                            "success": True,
                            "validated": True,
                            "retries_used": retries_used,
                            "finish_reason": choice.finish_reason or "stop"
                        }
                    else:
                        retries_used += 1
                        last_error = "Invalid JSON response"
                        if attempt < max_retries - 1:
                            await asyncio.sleep(0.5)  # Brief delay before retry

                except Exception as e:
                    retries_used += 1
                    last_error = str(e)
                    logger.warning(f"OpenAI API attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(0.5)  # Brief delay before retry

            # All retries failed
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": retries_used,
                "error": f"Failed after {max_retries} attempts: {last_error}",
                "finish_reason": "error"
            }

        except Exception as e:
            logger.error(f"OpenAI structured generation error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": 0,
                "error": str(e),
                "finish_reason": "error"
            }


class AnthropicProvider(LLMProvider):
    """Anthropic Claude provider implementation."""

    @property
    def default_model(self) -> str:
        return "claude-3-sonnet-20240229"

    @property
    def default_base_url(self) -> str:
        return "https://api.anthropic.com"

    @property
    def provider_name(self) -> str:
        return "anthropic"

    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate text response using Anthropic API."""
        logger.info(f"Generating text with Anthropic {self.model}")

        try:
            import anthropic

            # Create Anthropic client
            client = anthropic.AsyncAnthropic(
                api_key=self.api_key,
                base_url=self.base_url if self.base_url else None
            )

            # Make API call
            response = await client.messages.create(
                model=self.model,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )

            # Extract response data
            content = ""
            if response.content and len(response.content) > 0:
                content = response.content[0].text if hasattr(response.content[0], 'text') else str(response.content[0])

            return {
                "content": content,
                "model": response.model,
                "provider": self.provider_name,
                "tokens_used": response.usage.input_tokens + response.usage.output_tokens if response.usage else 0,
                "success": True,
                "finish_reason": response.stop_reason or "end_turn"
            }

        except ImportError:
            logger.warning("Anthropic library not available, using mock response")
            prompt_preview = str(prompt)[:50] if len(str(prompt)) > 50 else str(prompt)
            return {
                "content": f"Mock response for prompt: {prompt_preview}...",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 120,
                "success": True,
                "finish_reason": "end_turn"
            }
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "error": str(e),
                "finish_reason": "error"
            }

    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate structured response using Anthropic API."""
        logger.info(f"Generating structured response with Anthropic {self.model}")

        try:
            import anthropic

            # Create Anthropic client
            client = anthropic.AsyncAnthropic(
                api_key=self.api_key,
                base_url=self.base_url if self.base_url else None
            )

            # Construct prompt with EBNF grammar instructions
            structured_prompt = f"""
{user_message}

Please respond with valid JSON that follows this EBNF grammar:
{ebnf_grammar}

Respond only with the JSON, no additional text.
"""

            retries_used = 0
            last_error = None

            for attempt in range(max_retries):
                try:
                    # Make API call
                    response = await client.messages.create(
                        model=self.model,
                        max_tokens=1000,
                        temperature=temperature,
                        system=system_message,
                        messages=[
                            {"role": "user", "content": structured_prompt}
                        ]
                    )

                    # Extract response data
                    content = ""
                    if response.content and len(response.content) > 0:
                        content = response.content[0].text if hasattr(response.content[0], 'text') else str(response.content[0])

                    # Try to parse JSON to validate
                    try:
                        json.loads(content)
                        json_valid = True
                    except json.JSONDecodeError:
                        json_valid = False

                    if json_valid:
                        return {
                            "content": content,
                            "model": response.model,
                            "provider": self.provider_name,
                            "tokens_used": response.usage.input_tokens + response.usage.output_tokens if response.usage else 0,
                            "success": True,
                            "validated": True,
                            "retries_used": retries_used,
                            "finish_reason": response.stop_reason or "end_turn"
                        }
                    else:
                        retries_used += 1
                        last_error = "Invalid JSON response"
                        if attempt < max_retries - 1:
                            await asyncio.sleep(0.5)  # Brief delay before retry

                except Exception as e:
                    retries_used += 1
                    last_error = str(e)
                    logger.warning(f"Anthropic API attempt {attempt + 1} failed: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(0.5)  # Brief delay before retry

            # All retries failed
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": retries_used,
                "error": f"Failed after {max_retries} attempts: {last_error}",
                "finish_reason": "error"
            }

        except ImportError:
            logger.warning("Anthropic library not available, using mock response")
            return {
                "content": '{"plugin_name": "test_plugin", "services": []}',
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 180,
                "success": True,
                "validated": True,
                "retries_used": 0
            }
        except Exception as e:
            logger.error(f"Anthropic structured generation error: {e}")
            return {
                "content": "",
                "model": self.model,
                "provider": self.provider_name,
                "tokens_used": 0,
                "success": False,
                "validated": False,
                "retries_used": 0,
                "error": str(e),
                "finish_reason": "error"
            }


class LocalProvider(LLMProvider):
    """Local LLM provider (e.g., llama.cpp, Ollama)."""

    @property
    def default_model(self) -> str:
        return "llama2"

    @property
    def default_base_url(self) -> str:
        return "http://localhost:8080"

    @property
    def provider_name(self) -> str:
        return "local"

    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate text response using local LLM."""
        # TODO: Implement local LLM integration
        logger.info(f"Generating text with local {self.model}")
        # Safe string truncation
        prompt_preview = str(prompt)[:50] if len(str(prompt)) > 50 else str(prompt)
        return {
            "content": f"Mock response for prompt: {prompt_preview}...",
            "model": self.model,
            "provider": self.provider_name,
            "tokens_used": 80,
            "success": True,
            "finish_reason": "stop"
        }

    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> dict[str, Any]:
        """Generate structured response using local LLM with GBNF grammar."""
        # TODO: Implement local LLM with GBNF grammar support
        logger.info(f"Generating structured response with local {self.model}")
        return {
            "content": '{"plugin_name": "test_plugin", "services": []}',
            "model": self.model,
            "provider": self.provider_name,
            "tokens_used": 90,
            "success": True,
            "validated": True,
            "retries_used": 0
        }


class ProviderFactory:
    """Factory for creating LLM providers."""

    @staticmethod
    def create_provider(
        provider_type: str,
        api_key: str | None = None,
        model: str | None = None,
        base_url: str | None = None
    ) -> LLMProvider:
        """Create LLM provider instance.

        Args:
            provider_type: Provider type (openai, anthropic, local)
            api_key: API key for the provider
            model: Model name (optional)
            base_url: Base URL (optional)

        Returns:
            Configured LLM provider instance

        Raises:
            ValueError: If provider type is unsupported
        """
        provider_type_lower = provider_type.lower()

        if provider_type_lower == "openai":
            return OpenAIProvider(
                api_key=api_key or "",
                model=model,
                base_url=base_url
            )
        elif provider_type_lower == "anthropic":
            return AnthropicProvider(
                api_key=api_key or "",
                model=model,
                base_url=base_url
            )
        elif provider_type_lower == "local":
            return LocalProvider(
                api_key=api_key or "",
                model=model,
                base_url=base_url
            )
        else:
            raise ValueError(
                f"Unsupported LLM provider: {provider_type}. "
                f"Supported providers: openai, anthropic, local"
            )

    @staticmethod
    def create_from_env() -> LLMProvider:
        """Create LLM provider from environment variables.

        Environment Variables (Plugginger-specific):
            PLUGGINGER_LLM_PROVIDER: Provider type (openai, anthropic, local)
            PLUGGINGER_LLM_API_KEY: API key for the provider
            PLUGGINGER_LLM_MODEL: Model name (optional)
            PLUGGINGER_LLM_BASE_URL: Base URL (optional)

        Environment Variables (Standard):
            OPENAI_API_KEY: OpenAI API key (fallback)
            ANTHROPIC_API_KEY: Anthropic API key (fallback)

        Returns:
            Configured LLM provider instance

        Raises:
            ValueError: If required environment variables are missing
        """
        # Try Plugginger-specific variables first
        provider_type = os.getenv("PLUGGINGER_LLM_PROVIDER")
        api_key = os.getenv("PLUGGINGER_LLM_API_KEY")
        model = os.getenv("PLUGGINGER_LLM_MODEL")
        base_url = os.getenv("PLUGGINGER_LLM_BASE_URL")

        # Fallback to standard environment variables
        if not provider_type:
            if os.getenv("OPENAI_API_KEY"):
                provider_type = "openai"
                api_key = api_key or os.getenv("OPENAI_API_KEY")
                model = model or "gpt-4o-mini"  # Default to available model
            elif os.getenv("ANTHROPIC_API_KEY"):
                provider_type = "anthropic"
                api_key = api_key or os.getenv("ANTHROPIC_API_KEY")
                model = model or "claude-3-haiku-20240307"

        if not provider_type:
            raise ValueError(
                "No LLM provider configured. Set PLUGGINGER_LLM_PROVIDER or provide "
                "OPENAI_API_KEY/ANTHROPIC_API_KEY environment variables."
            )

        if not api_key and provider_type.lower() != "local":
            raise ValueError(
                f"API key required for {provider_type} provider. "
                f"Set PLUGGINGER_LLM_API_KEY or {provider_type.upper()}_API_KEY."
            )

        return ProviderFactory.create_provider(
            provider_type=provider_type,
            api_key=api_key,
            model=model,
            base_url=base_url
        )
