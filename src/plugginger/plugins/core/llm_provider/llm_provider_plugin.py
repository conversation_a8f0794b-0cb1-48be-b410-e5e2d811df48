"""
LLM Provider Core Plugin.

Provides LLM abstraction with multi-provider support and structured output validation.
"""

import logging
from typing import Any

from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.core.exceptions import PluggingerConfigurationError, PluggingerValidationError
from plugginger.plugins.core.llm_provider.services.provider_service import (
    LL<PERSON>rovider,
    ProviderFactory,
)
from plugginger.plugins.core.llm_provider.services.validation_service import (
    ResponseValidationService,
)

# --- LLM Provider Plugin Constants ---

# Default temperature for LLM generation (lower = more deterministic)
DEFAULT_TEMPERATURE: float = 0.1

# Default maximum retry attempts for failed LLM requests
DEFAULT_MAX_RETRIES: int = 3

# Default timeout for LLM requests (seconds)
DEFAULT_TIMEOUT_SECONDS: float = 30.0

logger = logging.getLogger(__name__)


@plugin(name="llm_provider", version="1.0.0")
class LLMProviderPlugin(PluginBase):
    """Core plugin for LLM provider abstraction and response validation."""

    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize LLM provider plugin.

        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger

        # Initialize services
        self.validation_service = ResponseValidationService()

        # Configuration from manifest
        self.default_temperature = DEFAULT_TEMPERATURE
        self.default_max_retries = DEFAULT_MAX_RETRIES
        self.default_timeout_seconds = DEFAULT_TIMEOUT_SECONDS

        # Provider will be initialized in setup
        self.provider: LLMProvider | None = None

    async def setup(self, plugin_config: Any = None) -> None:
        """Setup the LLM provider plugin."""
        self.logger.info("LLM Provider plugin initializing")

        # Load configuration from plugin_config if provided
        if plugin_config:
            if hasattr(plugin_config, 'default_temperature'):
                self.default_temperature = plugin_config.default_temperature
            if hasattr(plugin_config, 'default_max_retries'):
                self.default_max_retries = plugin_config.default_max_retries
            if hasattr(plugin_config, 'default_timeout_seconds'):
                self.default_timeout_seconds = plugin_config.default_timeout_seconds

            # Initialize provider from config
            if hasattr(plugin_config, 'provider'):
                try:
                    self.provider = ProviderFactory.create_provider(
                        provider_type=plugin_config.provider,
                        api_key=getattr(plugin_config, 'api_key', None),
                        model=getattr(plugin_config, 'model', None),
                        base_url=getattr(plugin_config, 'base_url', None)
                    )
                    self.logger.info(f"Initialized LLM provider: {plugin_config.provider}")
                except Exception as e:
                    self.logger.error(f"Failed to initialize LLM provider: {e}")
                    raise PluggingerConfigurationError(f"LLM provider initialization failed: {e}") from e

        # Fallback: Try to initialize from environment
        if not self.provider:
            try:
                self.provider = ProviderFactory.create_from_env()
                self.logger.info(f"Initialized LLM provider from environment: {self.provider.provider_name}")
            except Exception as e:
                self.logger.warning(f"Could not initialize LLM provider from environment: {e}")
                # Continue without provider - services will handle gracefully

    async def teardown(self) -> None:
        """Cleanup the LLM provider plugin."""
        self.logger.info("LLM Provider plugin shutting down")

    @service(name="generate_structured")
    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int | None = None,
        temperature: float | None = None
    ) -> dict[str, Any]:
        """Generate structured LLM response with EBNF grammar validation.

        Args:
            system_message: System prompt for the LLM
            user_message: User prompt for the LLM
            ebnf_grammar: EBNF grammar for response validation
            max_retries: Number of retry attempts (default from config)
            temperature: Sampling temperature (default from config)

        Returns:
            Structured response with validation results

        Example:
            result = await app.call_service("llm_provider.generate_structured", {
                "system_message": "You are a helpful assistant",
                "user_message": "Generate a JSON object with name and age",
                "ebnf_grammar": "response ::= '{\"name\":' string ',\"age\":' number '}'"
            })
        """
        if not self.provider:
            raise PluggingerConfigurationError("No LLM provider configured")

        max_retries = max_retries or self.default_max_retries
        temperature = temperature if temperature is not None else self.default_temperature

        self.logger.debug(f"Generating structured response with {max_retries} retries")

        try:
            # Emit start event
            await self.app.emit_event("llm.request.started", {
                "provider": self.provider.provider_name,
                "model": self.provider.model,
                "type": "structured"
            })

            result = await self.provider.generate_structured(
                system_message=system_message,
                user_message=user_message,
                ebnf_grammar=ebnf_grammar,
                max_retries=max_retries,
                temperature=temperature
            )

            # Validate response
            validation_result = await self.validation_service.validate_ebnf_response(
                result["content"],
                ebnf_grammar
            )

            # Emit validation event
            await self.app.emit_event("llm.response.validated", {
                "valid": validation_result["valid"],
                "format": "ebnf"
            })

            # Combine results
            final_result = {
                **result,
                "validation": validation_result,
                "ebnf_grammar": ebnf_grammar
            }

            # Emit completion event
            await self.app.emit_event("llm.request.completed", {
                "provider": self.provider.provider_name,
                "success": result["success"],
                "tokens_used": result.get("tokens_used", 0)
            })

            self.logger.info(f"Structured generation {'successful' if result['success'] else 'failed'}")
            return final_result

        except Exception as e:
            # Emit failure event
            await self.app.emit_event("llm.request.failed", {
                "provider": self.provider.provider_name if self.provider else "unknown",
                "error": str(e)
            })
            self.logger.error(f"Structured generation error: {e}")
            raise PluggingerValidationError(f"Structured generation failed: {e}") from e

    @service(name="generate_text")
    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float | None = None
    ) -> dict[str, Any]:
        """Generate plain text response from LLM.

        Args:
            prompt: Input prompt for the LLM
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature (default from config)

        Returns:
            Text response with metadata

        Example:
            result = await app.call_service("llm_provider.generate_text", {
                "prompt": "Explain quantum computing in simple terms",
                "max_tokens": 500
            })
        """
        if not self.provider:
            raise PluggingerConfigurationError("No LLM provider configured")

        temperature = temperature if temperature is not None else self.default_temperature

        self.logger.debug(f"Generating text response with max_tokens={max_tokens}")

        try:
            # Emit start event
            await self.app.emit_event("llm.request.started", {
                "provider": self.provider.provider_name,
                "model": self.provider.model,
                "type": "text"
            })

            result = await self.provider.generate_text(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature
            )

            # Validate response
            validation_result = await self.validation_service.validate_text_response(
                result["content"],
                min_length=10  # Basic sanity check
            )

            # Combine results
            final_result = {
                **result,
                "validation": validation_result,
                "prompt": prompt
            }

            # Emit completion event
            await self.app.emit_event("llm.request.completed", {
                "provider": self.provider.provider_name,
                "success": result["success"],
                "tokens_used": result.get("tokens_used", 0)
            })

            self.logger.info(f"Text generation {'successful' if result['success'] else 'failed'}")
            return final_result

        except Exception as e:
            # Emit failure event
            await self.app.emit_event("llm.request.failed", {
                "provider": self.provider.provider_name if self.provider else "unknown",
                "error": str(e)
            })
            self.logger.error(f"Text generation error: {e}")
            raise PluggingerValidationError(f"Text generation failed: {e}") from e

    @service(name="validate_response")
    async def validate_response(
        self,
        response_content: str,
        expected_format: str,
        validation_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Validate LLM response against expected format.

        Args:
            response_content: Raw response content to validate
            expected_format: Expected format (json, ebnf, text)
            validation_options: Format-specific validation options

        Returns:
            Validation result with errors and suggestions

        Example:
            result = await app.call_service("llm_provider.validate_response", {
                "response_content": '{"name": "John", "age": 30}',
                "expected_format": "json",
                "validation_options": {"schema": {"type": "object"}}
            })
        """
        self.logger.debug(f"Validating response format: {expected_format}")

        try:
            validation_result = await self.validation_service.validate_response_format(
                response_content=response_content,
                expected_format=expected_format,
                validation_options=validation_options
            )

            # Add suggestions for fixes
            suggestions = await self.validation_service.suggest_fixes(validation_result)
            validation_result["suggestions"] = suggestions

            # Emit validation event
            await self.app.emit_event("llm.response.validated", {
                "valid": validation_result["valid"],
                "format": expected_format,
                "error_count": len(validation_result.get("errors", []))
            })

            self.logger.info(f"Response validation {'passed' if validation_result['valid'] else 'failed'}")
            return validation_result

        except Exception as e:
            self.logger.error(f"Response validation error: {e}")
            raise PluggingerValidationError(f"Response validation failed: {e}") from e

    @service(name="get_provider_info")
    async def get_provider_info(self) -> dict[str, Any]:
        """Get information about current LLM provider.

        Returns:
            Provider information and configuration

        Example:
            info = await app.call_service("llm_provider.get_provider_info")
        """
        if not self.provider:
            return {
                "provider": None,
                "configured": False,
                "error": "No LLM provider configured"
            }

        try:
            provider_info = self.provider.get_info()
            return {
                **provider_info,
                "configured": True,
                "default_temperature": self.default_temperature,
                "default_max_retries": self.default_max_retries,
                "default_timeout_seconds": self.default_timeout_seconds
            }

        except Exception as e:
            self.logger.error(f"Error getting provider info: {e}")
            return {
                "provider": None,
                "configured": False,
                "error": str(e)
            }
