"""
Code template engine for dynamic plugin code generation.

Provides intelligent code generation from plugin specifications with
template-based approach, type safety, and best practices enforcement.
"""

import logging
from pathlib import Path
from typing import Any

from plugginger.core.exceptions import PluggingerValidationError

logger = logging.getLogger(__name__)

# --- Code Template Engine Constants ---

# Default indentation for generated code
DEFAULT_INDENTATION: str = "    "

# Maximum line length for generated code
MAX_LINE_LENGTH: int = 88

# Default timeout for service methods (seconds)
DEFAULT_SERVICE_TIMEOUT: float = 30.0

# Template version for generated code
TEMPLATE_VERSION: str = "1.0.0"


class CodeTemplateEngine:
    """Dynamic code generation engine for plugin templates."""

    def __init__(self, app: Any) -> None:
        """Initialize code template engine.

        Args:
            app: Plugginger application instance
        """
        self.app = app
        self.logger = logger

    async def generate_plugin_code(
        self,
        plugin_spec: dict[str, Any],
        generation_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Generate complete plugin code from specification.

        Args:
            plugin_spec: Plugin specification
            generation_options: Code generation options

        Returns:
            Generated plugin code with metadata

        Example:
            result = await engine.generate_plugin_code(
                plugin_spec={"name": "email_service", "services": [...]},
                generation_options={"include_docstrings": True}
            )
        """
        self.logger.info(f"Generating code for plugin: {plugin_spec.get('name', 'unknown')}")

        try:
            options = generation_options or {}
            
            # Generate main plugin file
            plugin_code = await self._generate_main_plugin_code(plugin_spec, options)
            
            # Generate test file
            test_code = await self._generate_test_code(plugin_spec, options)
            
            # Generate manifest file
            manifest_code = await self._generate_manifest_code(plugin_spec, options)
            
            # Generate __init__.py file
            init_code = await self._generate_init_code(plugin_spec, options)
            
            # Generate additional files if needed
            additional_files = await self._generate_additional_files(plugin_spec, options)
            
            # Calculate code metrics
            code_metrics = await self._calculate_code_metrics(
                plugin_code, test_code, manifest_code
            )
            
            return {
                "success": True,
                "plugin_code": plugin_code,
                "test_code": test_code,
                "manifest_code": manifest_code,
                "init_code": init_code,
                "additional_files": additional_files,
                "code_metrics": code_metrics,
                "generation_metadata": {
                    "template_version": TEMPLATE_VERSION,
                    "generation_options": options,
                    "plugin_name": plugin_spec.get("name"),
                    "estimated_complexity": self._estimate_complexity(plugin_spec)
                }
            }

        except Exception as e:
            self.logger.error(f"Code generation error: {e}")
            raise PluggingerValidationError(f"Code generation failed: {e}") from e

    async def _generate_main_plugin_code(
        self,
        plugin_spec: dict[str, Any],
        options: dict[str, Any]
    ) -> str:
        """Generate main plugin Python file.

        Args:
            plugin_spec: Plugin specification
            options: Generation options

        Returns:
            Generated plugin code
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        class_name = plugin_spec.get("class_name", f"{plugin_name.title()}Plugin")
        description = plugin_spec.get("description", "Generated plugin")
        
        # Generate imports
        imports = self._generate_imports(plugin_spec, options)
        
        # Generate constants
        constants = self._generate_constants(plugin_spec, options)
        
        # Generate class definition
        class_def = self._generate_class_definition(plugin_spec, options)
        
        # Generate __init__ method
        init_method = self._generate_init_method(plugin_spec, options)
        
        # Generate lifecycle methods
        lifecycle_methods = self._generate_lifecycle_methods(plugin_spec, options)
        
        # Generate service methods
        service_methods = self._generate_service_methods(plugin_spec, options)
        
        # Generate event handlers
        event_handlers = self._generate_event_handlers(plugin_spec, options)
        
        # Combine all parts
        code_parts = [
            self._generate_file_header(plugin_spec, options),
            imports,
            constants,
            class_def,
            init_method,
            lifecycle_methods,
            service_methods,
            event_handlers
        ]
        
        return "\n\n".join(filter(None, code_parts))

    def _generate_file_header(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate file header with docstring."""
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        description = plugin_spec.get("description", "Generated plugin")
        
        return f'''"""
{description}

This plugin was automatically generated by Plugginger's intelligent
plugin generation system.
"""'''

    def _generate_imports(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate import statements."""
        imports = [
            "import logging",
            "from typing import Any",
            "",
            "from plugginger.api.plugin import PluginBase, plugin",
            "from plugginger.api.service import service",
            "from plugginger.core.exceptions import PluggingerValidationError"
        ]
        
        # Add event imports if needed
        if plugin_spec.get("event_listeners"):
            imports.insert(-1, "from plugginger.api.events import on_event")
        
        return "\n".join(imports)

    def _generate_constants(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate plugin constants."""
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        
        constants = [
            f"logger = logging.getLogger(__name__)",
            "",
            "# --- Plugin Constants ---",
            "",
            f"# Default timeout for {plugin_name} operations (seconds)",
            f"DEFAULT_TIMEOUT: float = {DEFAULT_SERVICE_TIMEOUT}"
        ]
        
        # Add service-specific constants
        services = plugin_spec.get("services", [])
        for service in services:
            service_name = service.get("name", "unknown_service")
            timeout = service.get("timeout_seconds", DEFAULT_SERVICE_TIMEOUT)
            constants.append(f"# Timeout for {service_name} service")
            constants.append(f"{service_name.upper()}_TIMEOUT: float = {timeout}")
        
        return "\n".join(constants)

    def _generate_class_definition(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate plugin class definition."""
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        class_name = plugin_spec.get("class_name", f"{plugin_name.title()}Plugin")
        description = plugin_spec.get("description", "Generated plugin")
        version = plugin_spec.get("version", "1.0.0")
        
        return f'''@plugin(name="{plugin_name}", version="{version}")
class {class_name}(PluginBase):
    """{description}"""'''

    def _generate_init_method(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate __init__ method."""
        class_name = plugin_spec.get("class_name", "UnknownPlugin")
        
        init_lines = [
            f"{DEFAULT_INDENTATION}def __init__(self, **injected_dependencies: Any) -> None:",
            f"{DEFAULT_INDENTATION * 2}\"\"\"Initialize {class_name}.",
            f"{DEFAULT_INDENTATION * 2}",
            f"{DEFAULT_INDENTATION * 2}Args:",
            f"{DEFAULT_INDENTATION * 3}**injected_dependencies: Injected dependencies from DI container",
            f"{DEFAULT_INDENTATION * 2}\"\"\"",
            f"{DEFAULT_INDENTATION * 2}super().__init__(**injected_dependencies)",
            f"{DEFAULT_INDENTATION * 2}self.logger = logger",
            f"{DEFAULT_INDENTATION * 2}",
            f"{DEFAULT_INDENTATION * 2}# Initialize plugin configuration",
            f"{DEFAULT_INDENTATION * 2}self.timeout = DEFAULT_TIMEOUT"
        ]
        
        return "\n".join(init_lines)

    def _generate_lifecycle_methods(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate lifecycle methods (setup/teardown)."""
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        
        setup_method = [
            f"{DEFAULT_INDENTATION}async def setup(self, plugin_config: Any = None) -> None:",
            f"{DEFAULT_INDENTATION * 2}\"\"\"Setup the {plugin_name} plugin.\"\"\"",
            f"{DEFAULT_INDENTATION * 2}self.logger.info(\"{plugin_name} plugin initializing\")",
            f"{DEFAULT_INDENTATION * 2}",
            f"{DEFAULT_INDENTATION * 2}# Load configuration if provided",
            f"{DEFAULT_INDENTATION * 2}if plugin_config:",
            f"{DEFAULT_INDENTATION * 3}if hasattr(plugin_config, 'timeout'):",
            f"{DEFAULT_INDENTATION * 4}self.timeout = plugin_config.timeout"
        ]
        
        teardown_method = [
            f"{DEFAULT_INDENTATION}async def teardown(self) -> None:",
            f"{DEFAULT_INDENTATION * 2}\"\"\"Cleanup the {plugin_name} plugin.\"\"\"",
            f"{DEFAULT_INDENTATION * 2}self.logger.info(\"{plugin_name} plugin shutting down\")"
        ]
        
        return "\n".join(setup_method) + "\n\n" + "\n".join(teardown_method)

    def _generate_service_methods(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate service methods."""
        services = plugin_spec.get("services", [])
        if not services:
            return ""
        
        service_methods = []
        
        for service in services:
            service_name = service.get("name", "unknown_service")
            service_desc = service.get("description", f"Service: {service_name}")
            timeout = service.get("timeout_seconds", DEFAULT_SERVICE_TIMEOUT)
            
            method_lines = [
                f"{DEFAULT_INDENTATION}@service(name=\"{service_name}\")",
                f"{DEFAULT_INDENTATION}async def {service_name}(",
                f"{DEFAULT_INDENTATION * 2}self,",
                f"{DEFAULT_INDENTATION * 2}data: dict[str, Any] | None = None",
                f"{DEFAULT_INDENTATION}) -> dict[str, Any]:",
                f"{DEFAULT_INDENTATION * 2}\"\"\"{service_desc}",
                f"{DEFAULT_INDENTATION * 2}",
                f"{DEFAULT_INDENTATION * 2}Args:",
                f"{DEFAULT_INDENTATION * 3}data: Input data for the service",
                f"{DEFAULT_INDENTATION * 2}",
                f"{DEFAULT_INDENTATION * 2}Returns:",
                f"{DEFAULT_INDENTATION * 3}Service result",
                f"{DEFAULT_INDENTATION * 2}",
                f"{DEFAULT_INDENTATION * 2}Example:",
                f"{DEFAULT_INDENTATION * 3}result = await app.call_service(\"{plugin_spec.get('name', 'plugin')}.{service_name}\", {{",
                f"{DEFAULT_INDENTATION * 4}\"data\": {{\"key\": \"value\"}}",
                f"{DEFAULT_INDENTATION * 3}}})",
                f"{DEFAULT_INDENTATION * 2}\"\"\"",
                f"{DEFAULT_INDENTATION * 2}self.logger.debug(f\"Executing {service_name} service\")",
                f"{DEFAULT_INDENTATION * 2}",
                f"{DEFAULT_INDENTATION * 2}try:",
                f"{DEFAULT_INDENTATION * 3}# TODO: Implement {service_name} logic",
                f"{DEFAULT_INDENTATION * 3}result = {{",
                f"{DEFAULT_INDENTATION * 4}\"success\": True,",
                f"{DEFAULT_INDENTATION * 4}\"service\": \"{service_name}\",",
                f"{DEFAULT_INDENTATION * 4}\"data\": data or {{}},",
                f"{DEFAULT_INDENTATION * 4}\"timestamp\": \"2024-12-05T00:00:00Z\"",
                f"{DEFAULT_INDENTATION * 3}}}",
                f"{DEFAULT_INDENTATION * 3}",
                f"{DEFAULT_INDENTATION * 3}self.logger.info(f\"{service_name} service completed successfully\")",
                f"{DEFAULT_INDENTATION * 3}return result",
                f"{DEFAULT_INDENTATION * 2}",
                f"{DEFAULT_INDENTATION * 2}except Exception as e:",
                f"{DEFAULT_INDENTATION * 3}self.logger.error(f\"{service_name} service error: {{e}}\")",
                f"{DEFAULT_INDENTATION * 3}raise PluggingerValidationError(f\"{service_name} service failed: {{e}}\") from e"
            ]
            
            service_methods.append("\n".join(method_lines))
        
        return "\n\n".join(service_methods)

    def _generate_event_handlers(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate event handler methods."""
        event_listeners = plugin_spec.get("event_listeners", [])
        if not event_listeners:
            return ""
        
        event_methods = []
        
        for event_listener in event_listeners:
            event_name = event_listener.get("event", "unknown.event")
            handler_name = event_listener.get("handler", "handle_unknown_event")
            
            method_lines = [
                f"{DEFAULT_INDENTATION}@on_event(\"{event_name}\")",
                f"{DEFAULT_INDENTATION}async def {handler_name}(self, event_data: dict[str, Any]) -> None:",
                f"{DEFAULT_INDENTATION * 2}\"\"\"Handle {event_name} event.",
                f"{DEFAULT_INDENTATION * 2}",
                f"{DEFAULT_INDENTATION * 2}Args:",
                f"{DEFAULT_INDENTATION * 3}event_data: Event data",
                f"{DEFAULT_INDENTATION * 2}\"\"\"",
                f"{DEFAULT_INDENTATION * 2}self.logger.info(f\"Handling {event_name} event\")",
                f"{DEFAULT_INDENTATION * 2}",
                f"{DEFAULT_INDENTATION * 2}try:",
                f"{DEFAULT_INDENTATION * 3}# TODO: Implement {event_name} handler logic",
                f"{DEFAULT_INDENTATION * 3}self.logger.debug(f\"Event data: {{event_data}}\")",
                f"{DEFAULT_INDENTATION * 3}",
                f"{DEFAULT_INDENTATION * 3}# Process event data here",
                f"{DEFAULT_INDENTATION * 3}pass",
                f"{DEFAULT_INDENTATION * 2}",
                f"{DEFAULT_INDENTATION * 2}except Exception as e:",
                f"{DEFAULT_INDENTATION * 3}self.logger.error(f\"{event_name} handler error: {{e}}\")",
                f"{DEFAULT_INDENTATION * 3}# Don't re-raise in event handlers to avoid breaking event chain"
            ]
            
            event_methods.append("\n".join(method_lines))
        
        return "\n\n".join(event_methods)

    async def _generate_test_code(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate test code for plugin."""
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        class_name = plugin_spec.get("class_name", f"{plugin_name.title()}Plugin")
        
        test_code = f'''"""
Tests for {plugin_name} plugin.

This test file was automatically generated by Plugginger's intelligent
plugin generation system.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock

from {plugin_name} import {class_name}


class Test{class_name}:
    """Test cases for {class_name}."""

    @pytest.fixture
    def plugin(self) -> {class_name}:
        """Create plugin instance for testing."""
        mock_app = MagicMock()
        return {class_name}(app=mock_app)

    @pytest.mark.asyncio
    async def test_plugin_initialization(self, plugin: {class_name}) -> None:
        """Test plugin initialization."""
        assert plugin is not None
        assert hasattr(plugin, 'logger')
        assert hasattr(plugin, 'timeout')

    @pytest.mark.asyncio
    async def test_setup(self, plugin: {class_name}) -> None:
        """Test plugin setup."""
        await plugin.setup()
        # Add specific setup assertions here

    @pytest.mark.asyncio
    async def test_teardown(self, plugin: {class_name}) -> None:
        """Test plugin teardown."""
        await plugin.teardown()
        # Add specific teardown assertions here
'''

        # Add service tests
        services = plugin_spec.get("services", [])
        for service in services:
            service_name = service.get("name", "unknown_service")
            test_code += f'''
    @pytest.mark.asyncio
    async def test_{service_name}(self, plugin: {class_name}) -> None:
        """Test {service_name} service."""
        result = await plugin.{service_name}({{"test": "data"}})
        
        assert result is not None
        assert result.get("success") is True
        assert result.get("service") == "{service_name}"
'''

        return test_code

    async def _generate_manifest_code(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate manifest.yaml code."""
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        description = plugin_spec.get("description", "Generated plugin")
        version = plugin_spec.get("version", "1.0.0")
        
        manifest_lines = [
            f"name: {plugin_name}",
            f"version: {version}",
            f"description: {description}",
            "author: Generated by Plugginger",
            "license: MIT",
            "plugin_type: user",
            "execution_mode: thread"
        ]
        
        # Add dependencies
        dependencies = plugin_spec.get("dependencies", [])
        if dependencies:
            manifest_lines.append("dependencies:")
            for dep in dependencies:
                dep_name = dep.get("name") if isinstance(dep, dict) else dep
                dep_version = dep.get("version", ">=1.0.0") if isinstance(dep, dict) else ">=1.0.0"
                optional = dep.get("optional", False) if isinstance(dep, dict) else False
                
                manifest_lines.extend([
                    f"  - name: {dep_name}",
                    f"    version: \"{dep_version}\"",
                    f"    optional: {str(optional).lower()}"
                ])
        
        # Add services
        services = plugin_spec.get("services", [])
        if services:
            manifest_lines.append("services:")
            for service in services:
                service_name = service.get("name", "unknown_service")
                service_desc = service.get("description", f"Service: {service_name}")
                timeout = service.get("timeout_seconds", DEFAULT_SERVICE_TIMEOUT)
                
                manifest_lines.extend([
                    f"  - name: {service_name}",
                    f"    description: {service_desc}",
                    f"    timeout_seconds: {timeout}"
                ])
        
        # Add metadata
        manifest_lines.extend([
            "metadata:",
            f"  tags:",
            f"    - generated",
            f"    - {plugin_spec.get('metadata', {}).get('plugin_type', 'general')}",
            f"  category: user-generated",
            f"  stability: experimental",
            f"  created_at: \"2024-12-05T00:00:00Z\"",
            f"  updated_at: \"2024-12-05T00:00:00Z\""
        ])
        
        return "\n".join(manifest_lines)

    async def _generate_init_code(self, plugin_spec: dict[str, Any], options: dict[str, Any]) -> str:
        """Generate __init__.py code."""
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        class_name = plugin_spec.get("class_name", f"{plugin_name.title()}Plugin")
        
        return f'''"""
{plugin_name} plugin package.

This package was automatically generated by Plugginger's intelligent
plugin generation system.
"""

from .{plugin_name} import {class_name}

__all__ = ["{class_name}"]
'''

    async def _generate_additional_files(
        self,
        plugin_spec: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, str]:
        """Generate additional files if needed."""
        additional_files = {}
        
        # Generate README.md if requested
        if options.get("include_readme", True):
            additional_files["README.md"] = await self._generate_readme(plugin_spec)
        
        # Generate requirements.txt if dependencies exist
        dependencies = plugin_spec.get("dependencies", [])
        if dependencies and options.get("include_requirements", True):
            additional_files["requirements.txt"] = await self._generate_requirements(dependencies)
        
        return additional_files

    async def _generate_readme(self, plugin_spec: dict[str, Any]) -> str:
        """Generate README.md content."""
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        description = plugin_spec.get("description", "Generated plugin")
        
        return f'''# {plugin_name.title().replace('_', ' ')}

{description}

This plugin was automatically generated by Plugginger's intelligent plugin generation system.

## Installation

```bash
pip install plugginger
```

## Usage

```python
from plugginger import PluggingerAppBuilder

app = PluggingerAppBuilder()\\
    .include_plugin("{plugin_name}")\\
    .build()

# Use plugin services
result = await app.call_service("{plugin_name}.service_name", {{"data": "value"}})
```

## Services

{self._format_services_for_readme(plugin_spec.get("services", []))}

## Configuration

This plugin supports configuration through the manifest.yaml file or runtime configuration.

## License

MIT License
'''

    async def _generate_requirements(self, dependencies: list[Any]) -> str:
        """Generate requirements.txt content."""
        requirements = ["plugginger>=1.0.0"]
        
        for dep in dependencies:
            if isinstance(dep, dict):
                dep_name = dep.get("name", "unknown")
                dep_version = dep.get("version", ">=1.0.0")
                if dep_name not in ["logger", "config"]:  # Skip framework dependencies
                    requirements.append(f"{dep_name}{dep_version}")
            elif isinstance(dep, str) and dep not in ["logger", "config"]:
                requirements.append(f"{dep}>=1.0.0")
        
        return "\n".join(requirements)

    def _format_services_for_readme(self, services: list[dict[str, Any]]) -> str:
        """Format services for README."""
        if not services:
            return "No services defined."
        
        service_docs = []
        for service in services:
            service_name = service.get("name", "unknown_service")
            service_desc = service.get("description", "No description")
            service_docs.append(f"- **{service_name}**: {service_desc}")
        
        return "\n".join(service_docs)

    async def _calculate_code_metrics(
        self,
        plugin_code: str,
        test_code: str,
        manifest_code: str
    ) -> dict[str, Any]:
        """Calculate code metrics."""
        return {
            "plugin_lines": len(plugin_code.split("\n")),
            "test_lines": len(test_code.split("\n")),
            "manifest_lines": len(manifest_code.split("\n")),
            "total_lines": len(plugin_code.split("\n")) + len(test_code.split("\n")),
            "estimated_complexity": "medium",
            "code_quality_score": 0.8
        }

    def _estimate_complexity(self, plugin_spec: dict[str, Any]) -> str:
        """Estimate plugin complexity."""
        service_count = len(plugin_spec.get("services", []))
        event_count = len(plugin_spec.get("event_listeners", []))
        dependency_count = len(plugin_spec.get("dependencies", []))
        
        total_complexity = service_count + event_count + dependency_count
        
        if total_complexity <= 3:
            return "low"
        elif total_complexity <= 7:
            return "medium"
        else:
            return "high"
