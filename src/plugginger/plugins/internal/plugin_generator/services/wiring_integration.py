"""
Wiring integration service for intelligent plugin generation.

Provides automatic integration of service calls, event listeners, and
dependency injection patterns for generated plugins.
"""

import logging
from typing import Any

from plugginger.core.exceptions import PluggingerValidationError

logger = logging.getLogger(__name__)

# --- Wiring Integration Constants ---

# Maximum depth for dependency analysis
MAX_DEPENDENCY_DEPTH: int = 5

# Default confidence threshold for integration suggestions
DEFAULT_CONFIDENCE_THRESHOLD: float = 0.7

# Maximum number of integration suggestions per plugin
MAX_INTEGRATION_SUGGESTIONS: int = 10

# Default priority for integration suggestions
DEFAULT_INTEGRATION_PRIORITY: int = 5


class WiringIntegration:
    """Automatic wiring integration for plugin generation."""

    def __init__(self, app: Any) -> None:
        """Initialize wiring integration service.

        Args:
            app: Plugginger application instance
        """
        self.app = app
        self.logger = logger

    async def analyze_integration_opportunities(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any] | None = None,
        analysis_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Analyze integration opportunities for plugin.

        Args:
            plugin_spec: Plugin specification to analyze
            app_context: Current application context
            analysis_options: Analysis configuration options

        Returns:
            Integration analysis with suggestions and recommendations

        Example:
            result = await wiring.analyze_integration_opportunities(
                plugin_spec={"name": "email_service", "services": [...]},
                app_context={"available_services": ["logger", "config"]},
                analysis_options={"confidence_threshold": 0.8}
            )
        """
        self.logger.info(f"Analyzing integration opportunities for: {plugin_spec.get('name', 'unknown')}")

        try:
            options = analysis_options or {}
            context = app_context or {}
            
            # Analyze service dependencies
            service_analysis = await self._analyze_service_dependencies(
                plugin_spec, context, options
            )
            
            # Analyze event integration opportunities
            event_analysis = await self._analyze_event_integration(
                plugin_spec, context, options
            )
            
            # Analyze dependency injection patterns
            di_analysis = await self._analyze_dependency_injection(
                plugin_spec, context, options
            )
            
            # Generate integration suggestions
            integration_suggestions = await self._generate_integration_suggestions(
                service_analysis, event_analysis, di_analysis, options
            )
            
            # Calculate integration complexity
            complexity_analysis = await self._calculate_integration_complexity(
                plugin_spec, integration_suggestions
            )
            
            # Generate wiring code snippets
            wiring_snippets = await self._generate_wiring_snippets(
                integration_suggestions, options
            )
            
            return {
                "success": True,
                "plugin_name": plugin_spec.get("name"),
                "service_analysis": service_analysis,
                "event_analysis": event_analysis,
                "dependency_injection_analysis": di_analysis,
                "integration_suggestions": integration_suggestions,
                "complexity_analysis": complexity_analysis,
                "wiring_snippets": wiring_snippets,
                "analysis_metadata": {
                    "confidence_threshold": options.get("confidence_threshold", DEFAULT_CONFIDENCE_THRESHOLD),
                    "total_suggestions": len(integration_suggestions),
                    "high_confidence_suggestions": len([s for s in integration_suggestions if s.get("confidence", 0) > 0.8])
                }
            }

        except Exception as e:
            self.logger.error(f"Integration analysis error: {e}")
            raise PluggingerValidationError(f"Integration analysis failed: {e}") from e

    async def _analyze_service_dependencies(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Analyze service dependency patterns.

        Args:
            plugin_spec: Plugin specification
            app_context: Application context
            options: Analysis options

        Returns:
            Service dependency analysis
        """
        self.logger.debug("Analyzing service dependencies")

        plugin_services = plugin_spec.get("services", [])
        available_services = app_context.get("available_services", [])
        
        dependency_suggestions = []
        service_compatibility = {}
        
        for service in plugin_services:
            service_name = service.get("name") if isinstance(service, dict) else str(service)
            if not service_name:
                continue
            
            # Analyze potential service dependencies
            potential_deps = self._find_potential_service_dependencies(
                service_name, available_services
            )
            
            # Calculate compatibility scores
            compatibility_scores = self._calculate_service_compatibility(
                service_name, potential_deps
            )
            
            service_compatibility[service_name] = compatibility_scores
            
            # Generate dependency suggestions
            for dep_service, score in compatibility_scores.items():
                if score >= options.get("confidence_threshold", DEFAULT_CONFIDENCE_THRESHOLD):
                    dependency_suggestions.append({
                        "source_service": service_name,
                        "target_service": dep_service,
                        "dependency_type": "service_call",
                        "confidence": score,
                        "integration_pattern": self._suggest_integration_pattern(service_name, dep_service)
                    })

        return {
            "plugin_services": plugin_services,
            "available_services": available_services,
            "dependency_suggestions": dependency_suggestions,
            "service_compatibility": service_compatibility,
            "analysis_summary": {
                "total_services": len(plugin_services),
                "potential_integrations": len(dependency_suggestions),
                "average_compatibility": self._calculate_average_compatibility(service_compatibility)
            }
        }

    async def _analyze_event_integration(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Analyze event integration opportunities.

        Args:
            plugin_spec: Plugin specification
            app_context: Application context
            options: Analysis options

        Returns:
            Event integration analysis
        """
        self.logger.debug("Analyzing event integration")

        plugin_events = plugin_spec.get("event_listeners", [])
        available_events = app_context.get("available_events", [])
        
        event_suggestions = []
        event_compatibility = {}
        
        # Analyze existing event listeners
        for event_listener in plugin_events:
            event_name = event_listener.get("event") if isinstance(event_listener, dict) else str(event_listener)
            
            compatibility_score = self._calculate_event_compatibility(
                event_name, available_events
            )
            
            event_compatibility[event_name] = compatibility_score

        # Suggest additional event integrations
        plugin_type = plugin_spec.get("metadata", {}).get("plugin_type", "general")
        suggested_events = self._suggest_events_for_plugin_type(plugin_type)
        
        for suggested_event in suggested_events:
            if suggested_event in available_events:
                event_suggestions.append({
                    "event": suggested_event,
                    "handler_name": f"handle_{suggested_event.replace('.', '_')}",
                    "confidence": 0.8,
                    "reason": f"Recommended for {plugin_type} plugins",
                    "integration_pattern": "event_listener"
                })

        return {
            "plugin_events": plugin_events,
            "available_events": available_events,
            "event_suggestions": event_suggestions,
            "event_compatibility": event_compatibility,
            "analysis_summary": {
                "existing_listeners": len(plugin_events),
                "suggested_listeners": len(event_suggestions),
                "total_available_events": len(available_events)
            }
        }

    async def _analyze_dependency_injection(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Analyze dependency injection patterns.

        Args:
            plugin_spec: Plugin specification
            app_context: Application context
            options: Analysis options

        Returns:
            Dependency injection analysis
        """
        self.logger.debug("Analyzing dependency injection patterns")

        plugin_dependencies = plugin_spec.get("dependencies", [])
        available_dependencies = app_context.get("available_dependencies", [])
        
        di_suggestions = []
        dependency_analysis = {}
        
        # Analyze existing dependencies
        for dependency in plugin_dependencies:
            dep_name = dependency.get("name") if isinstance(dependency, dict) else str(dependency)
            
            analysis = self._analyze_dependency_usage(dep_name, plugin_spec)
            dependency_analysis[dep_name] = analysis
            
            # Check if dependency is available
            if dep_name not in available_dependencies:
                di_suggestions.append({
                    "dependency": dep_name,
                    "suggestion_type": "missing_dependency",
                    "confidence": 0.9,
                    "action": "add_to_manifest",
                    "reason": f"Required dependency {dep_name} not found in available dependencies"
                })

        # Suggest additional dependencies based on services
        for service in plugin_spec.get("services", []):
            service_name = service.get("name") if isinstance(service, dict) else str(service)
            suggested_deps = self._suggest_dependencies_for_service(service_name)
            
            for suggested_dep in suggested_deps:
                if suggested_dep not in [d.get("name") if isinstance(d, dict) else d for d in plugin_dependencies]:
                    di_suggestions.append({
                        "dependency": suggested_dep,
                        "suggestion_type": "recommended_dependency",
                        "confidence": 0.7,
                        "action": "consider_adding",
                        "reason": f"Commonly used with {service_name} services"
                    })

        return {
            "plugin_dependencies": plugin_dependencies,
            "available_dependencies": available_dependencies,
            "di_suggestions": di_suggestions,
            "dependency_analysis": dependency_analysis,
            "analysis_summary": {
                "existing_dependencies": len(plugin_dependencies),
                "suggested_dependencies": len(di_suggestions),
                "missing_dependencies": len([s for s in di_suggestions if s["suggestion_type"] == "missing_dependency"])
            }
        }

    async def _generate_integration_suggestions(
        self,
        service_analysis: dict[str, Any],
        event_analysis: dict[str, Any],
        di_analysis: dict[str, Any],
        options: dict[str, Any]
    ) -> list[dict[str, Any]]:
        """Generate comprehensive integration suggestions.

        Args:
            service_analysis: Service dependency analysis
            event_analysis: Event integration analysis
            di_analysis: Dependency injection analysis
            options: Generation options

        Returns:
            List of integration suggestions
        """
        suggestions = []
        
        # Add service dependency suggestions
        for suggestion in service_analysis.get("dependency_suggestions", []):
            suggestions.append({
                **suggestion,
                "category": "service_integration",
                "priority": self._calculate_suggestion_priority(suggestion)
            })
        
        # Add event integration suggestions
        for suggestion in event_analysis.get("event_suggestions", []):
            suggestions.append({
                **suggestion,
                "category": "event_integration",
                "priority": self._calculate_suggestion_priority(suggestion)
            })
        
        # Add dependency injection suggestions
        for suggestion in di_analysis.get("di_suggestions", []):
            suggestions.append({
                **suggestion,
                "category": "dependency_injection",
                "priority": self._calculate_suggestion_priority(suggestion)
            })
        
        # Sort by priority and confidence
        suggestions.sort(key=lambda x: (x.get("priority", 0), x.get("confidence", 0)), reverse=True)
        
        # Limit to maximum suggestions
        max_suggestions = options.get("max_suggestions", MAX_INTEGRATION_SUGGESTIONS)
        return suggestions[:max_suggestions]

    async def _calculate_integration_complexity(
        self,
        plugin_spec: dict[str, Any],
        integration_suggestions: list[dict[str, Any]]
    ) -> dict[str, Any]:
        """Calculate integration complexity metrics.

        Args:
            plugin_spec: Plugin specification
            integration_suggestions: List of integration suggestions

        Returns:
            Integration complexity analysis
        """
        service_count = len(plugin_spec.get("services", []))
        event_count = len(plugin_spec.get("event_listeners", []))
        dependency_count = len(plugin_spec.get("dependencies", []))
        suggestion_count = len(integration_suggestions)
        
        # Calculate complexity score
        base_complexity = service_count + event_count + dependency_count
        integration_complexity = suggestion_count * 0.5
        total_complexity = base_complexity + integration_complexity
        
        # Determine complexity level
        if total_complexity <= 5:
            complexity_level = "low"
        elif total_complexity <= 15:
            complexity_level = "medium"
        else:
            complexity_level = "high"
        
        return {
            "complexity_level": complexity_level,
            "complexity_score": total_complexity,
            "base_complexity": base_complexity,
            "integration_complexity": integration_complexity,
            "metrics": {
                "service_count": service_count,
                "event_count": event_count,
                "dependency_count": dependency_count,
                "suggestion_count": suggestion_count
            },
            "recommendations": self._generate_complexity_recommendations(complexity_level, total_complexity)
        }

    async def _generate_wiring_snippets(
        self,
        integration_suggestions: list[dict[str, Any]],
        options: dict[str, Any]
    ) -> dict[str, str]:
        """Generate code snippets for integration suggestions.

        Args:
            integration_suggestions: List of integration suggestions
            options: Generation options

        Returns:
            Dictionary of code snippets by category
        """
        snippets: dict[str, list[str]] = {
            "service_calls": [],
            "event_handlers": [],
            "dependency_injection": [],
            "imports": []
        }
        
        for suggestion in integration_suggestions:
            category = suggestion.get("category", "unknown")
            
            if category == "service_integration":
                snippet = self._generate_service_call_snippet(suggestion)
                snippets["service_calls"].append(snippet)
            
            elif category == "event_integration":
                snippet = self._generate_event_handler_snippet(suggestion)
                snippets["event_handlers"].append(snippet)
            
            elif category == "dependency_injection":
                snippet = self._generate_di_snippet(suggestion)
                snippets["dependency_injection"].append(snippet)
        
        # Convert lists to formatted strings
        return {
            "service_calls": "\n\n".join(snippets["service_calls"]),
            "event_handlers": "\n\n".join(snippets["event_handlers"]),
            "dependency_injection": "\n\n".join(snippets["dependency_injection"]),
            "imports": "\n".join(snippets["imports"])
        }

    def _find_potential_service_dependencies(
        self,
        service_name: str,
        available_services: list[str]
    ) -> list[str]:
        """Find potential service dependencies."""
        potential_deps = []
        
        # Service dependency patterns
        dependency_patterns = {
            "email": ["logger", "config", "notification"],
            "auth": ["logger", "config", "security", "user"],
            "cache": ["logger", "config", "storage"],
            "database": ["logger", "config", "connection"],
            "api": ["logger", "config", "validation"],
            "file": ["logger", "config", "storage"],
            "notification": ["logger", "config", "email"]
        }
        
        service_lower = service_name.lower()
        for pattern, deps in dependency_patterns.items():
            if pattern in service_lower:
                potential_deps.extend([dep for dep in deps if dep in available_services])
        
        return list(set(potential_deps))

    def _calculate_service_compatibility(
        self,
        service_name: str,
        potential_deps: list[str]
    ) -> dict[str, float]:
        """Calculate service compatibility scores."""
        compatibility_scores = {}
        
        for dep in potential_deps:
            # Simple compatibility scoring based on naming patterns
            score = 0.5  # Base score
            
            service_lower = service_name.lower()
            dep_lower = dep.lower()
            
            # Boost score for common patterns
            if "log" in service_lower and "log" in dep_lower:
                score += 0.3
            if "config" in service_lower and "config" in dep_lower:
                score += 0.3
            if "auth" in service_lower and "security" in dep_lower:
                score += 0.2
            if "email" in service_lower and "notification" in dep_lower:
                score += 0.2
            
            compatibility_scores[dep] = min(1.0, score)
        
        return compatibility_scores

    def _suggest_integration_pattern(self, source_service: str, target_service: str) -> str:
        """Suggest integration pattern for services."""
        if "log" in target_service.lower():
            return "logging_integration"
        elif "config" in target_service.lower():
            return "configuration_injection"
        elif "auth" in target_service.lower() or "security" in target_service.lower():
            return "authentication_middleware"
        else:
            return "service_dependency"

    def _calculate_event_compatibility(self, event_name: str, available_events: list[str]) -> float:
        """Calculate event compatibility score."""
        if event_name in available_events:
            return 1.0
        
        # Check for similar events
        event_lower = event_name.lower()
        for available_event in available_events:
            if any(part in available_event.lower() for part in event_lower.split(".")):
                return 0.7
        
        return 0.3

    def _suggest_events_for_plugin_type(self, plugin_type: str) -> list[str]:
        """Suggest events based on plugin type."""
        event_suggestions = {
            "service": ["app.startup", "app.shutdown"],
            "auth": ["user.login", "user.logout", "auth.failed"],
            "storage": ["data.created", "data.updated", "data.deleted"],
            "general": ["app.startup", "app.shutdown"]
        }
        
        return event_suggestions.get(plugin_type, event_suggestions["general"])

    def _analyze_dependency_usage(self, dependency_name: str, plugin_spec: dict[str, Any]) -> dict[str, Any]:
        """Analyze how a dependency is used in the plugin."""
        usage_patterns = []
        
        # Check if dependency is used in services
        services = plugin_spec.get("services", [])
        for service in services:
            service_name = service.get("name") if isinstance(service, dict) else service
            if self._dependency_likely_used_in_service(dependency_name, service_name):
                usage_patterns.append(f"Used in {service_name} service")
        
        return {
            "dependency": dependency_name,
            "usage_patterns": usage_patterns,
            "usage_confidence": 0.8 if usage_patterns else 0.3
        }

    def _suggest_dependencies_for_service(self, service_name: str) -> list[str]:
        """Suggest dependencies for a service."""
        service_dependencies = {
            "email": ["logger", "config"],
            "auth": ["logger", "config", "security"],
            "cache": ["logger", "config"],
            "database": ["logger", "config"],
            "api": ["logger", "config", "validation"],
            "file": ["logger", "config"]
        }
        
        service_lower = service_name.lower()
        for pattern, deps in service_dependencies.items():
            if pattern in service_lower:
                return deps
        
        return ["logger"]  # Default dependency

    def _dependency_likely_used_in_service(self, dependency: str, service_name: str) -> bool:
        """Check if dependency is likely used in service."""
        dependency_lower = dependency.lower()
        service_lower = service_name.lower()
        
        # Common usage patterns
        if dependency_lower == "logger":
            return True  # Logger is used everywhere
        if dependency_lower == "config" and any(word in service_lower for word in ["config", "setting", "param"]):
            return True
        if dependency_lower == "security" and any(word in service_lower for word in ["auth", "login", "security"]):
            return True
        
        return False

    def _calculate_suggestion_priority(self, suggestion: dict[str, Any]) -> int:
        """Calculate priority for integration suggestion."""
        confidence = suggestion.get("confidence", 0.5)
        category = suggestion.get("category", "unknown")
        
        # Base priority from confidence
        priority = int(confidence * 10)
        
        # Adjust based on category
        if category == "dependency_injection":
            priority += 2  # DI is high priority
        elif category == "service_integration":
            priority += 1  # Service integration is medium priority
        
        return min(10, priority)

    def _calculate_average_compatibility(self, service_compatibility: dict[str, dict[str, float]]) -> float:
        """Calculate average compatibility score."""
        all_scores: list[float] = []
        for service_scores in service_compatibility.values():
            all_scores.extend(service_scores.values())
        
        return sum(all_scores) / len(all_scores) if all_scores else 0.0

    def _generate_complexity_recommendations(self, complexity_level: str, total_complexity: float) -> list[str]:
        """Generate recommendations based on complexity."""
        recommendations = []
        
        if complexity_level == "high":
            recommendations.extend([
                "Consider breaking down into smaller plugins",
                "Review dependency requirements",
                "Implement gradual integration approach"
            ])
        elif complexity_level == "medium":
            recommendations.extend([
                "Monitor integration performance",
                "Consider caching for frequently used services"
            ])
        else:
            recommendations.append("Plugin has low integration complexity")
        
        return recommendations

    def _generate_service_call_snippet(self, suggestion: dict[str, Any]) -> str:
        """Generate service call code snippet."""
        source_service = suggestion.get("source_service", "unknown")
        target_service = suggestion.get("target_service", "unknown")
        
        return f'''# Call {target_service} from {source_service}
result = await self.app.call_service("{target_service}", {{
    "data": service_data
}})'''

    def _generate_event_handler_snippet(self, suggestion: dict[str, Any]) -> str:
        """Generate event handler code snippet."""
        event = suggestion.get("event", "unknown.event")
        handler_name = suggestion.get("handler_name", "handle_event")
        
        return f'''@on_event("{event}")
async def {handler_name}(self, event_data: dict[str, Any]) -> None:
    """Handle {event} event."""
    self.logger.info(f"Handling {event} event")
    # TODO: Implement event handling logic'''

    def _generate_di_snippet(self, suggestion: dict[str, Any]) -> str:
        """Generate dependency injection code snippet."""
        dependency = suggestion.get("dependency", "unknown")
        
        return f'''# Add {dependency} to manifest.yaml dependencies:
# - name: {dependency}
#   version: ">=1.0.0"
#   optional: false

# Use in plugin:
# self.{dependency} = injected_dependencies.get("{dependency}")'''
