"""
Prompt processing service for intelligent plugin generation.

Provides advanced prompt engineering, context analysis, and structured
prompt generation for LLM-based plugin creation.
"""

import logging
from typing import Any

from plugginger.core.exceptions import PluggingerValidationError

logger = logging.getLogger(__name__)

# --- Prompt Processing Constants ---

# Maximum context length for LLM prompts
MAX_CONTEXT_LENGTH: int = 8000

# Default temperature for creative plugin generation
DEFAULT_GENERATION_TEMPERATURE: float = 0.3

# Maximum retry attempts for prompt processing
MAX_PROMPT_RETRIES: int = 3


class PromptProcessor:
    """Advanced prompt processing for plugin generation."""

    def __init__(self, app: Any) -> None:
        """Initialize prompt processor.

        Args:
            app: Plugginger application instance
        """
        self.app = app
        self.logger = logger

    async def process_user_prompt(
        self,
        user_prompt: str,
        app_context: dict[str, Any] | None = None,
        generation_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Process user prompt into structured plugin specification request.

        Args:
            user_prompt: Raw user prompt for plugin generation
            app_context: Application context for intelligent integration
            generation_options: Additional generation options

        Returns:
            Processed prompt with enhanced context and specifications

        Example:
            result = await processor.process_user_prompt(
                "Create an email service with authentication",
                app_context={"services": ["auth", "logger"]},
                generation_options={"include_tests": True}
            )
        """
        self.logger.info(f"Processing user prompt: {user_prompt[:100]}...")

        try:
            # Analyze prompt intent and extract requirements
            prompt_analysis = await self._analyze_prompt_intent(user_prompt)
            
            # Enhance with application context
            enhanced_context = await self._enhance_with_app_context(
                prompt_analysis, app_context
            )
            
            # Generate structured prompt for LLM
            structured_prompt = await self._create_structured_prompt(
                enhanced_context, generation_options
            )
            
            # Validate prompt structure
            validation_result = await self._validate_prompt_structure(structured_prompt)
            
            return {
                "success": True,
                "original_prompt": user_prompt,
                "prompt_analysis": prompt_analysis,
                "enhanced_context": enhanced_context,
                "structured_prompt": structured_prompt,
                "validation": validation_result,
                "processing_metadata": {
                    "context_length": len(str(enhanced_context)),
                    "complexity_score": prompt_analysis.get("complexity_score", 0.5),
                    "integration_suggestions": enhanced_context.get("integration_suggestions", [])
                }
            }

        except Exception as e:
            self.logger.error(f"Prompt processing error: {e}")
            raise PluggingerValidationError(f"Prompt processing failed: {e}") from e

    async def _analyze_prompt_intent(self, user_prompt: str) -> dict[str, Any]:
        """Analyze user prompt to extract intent and requirements.

        Args:
            user_prompt: Raw user prompt

        Returns:
            Prompt analysis with extracted requirements
        """
        self.logger.debug("Analyzing prompt intent")

        # Extract plugin type hints
        plugin_type = self._extract_plugin_type(user_prompt)
        
        # Extract service requirements
        service_requirements = self._extract_service_requirements(user_prompt)
        
        # Extract event requirements
        event_requirements = self._extract_event_requirements(user_prompt)
        
        # Extract dependency hints
        dependency_hints = self._extract_dependency_hints(user_prompt)
        
        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(
            user_prompt, service_requirements, event_requirements
        )

        return {
            "plugin_type": plugin_type,
            "service_requirements": service_requirements,
            "event_requirements": event_requirements,
            "dependency_hints": dependency_hints,
            "complexity_score": complexity_score,
            "prompt_length": len(user_prompt),
            "technical_keywords": self._extract_technical_keywords(user_prompt)
        }

    async def _enhance_with_app_context(
        self,
        prompt_analysis: dict[str, Any],
        app_context: dict[str, Any] | None
    ) -> dict[str, Any]:
        """Enhance prompt analysis with application context.

        Args:
            prompt_analysis: Initial prompt analysis
            app_context: Application context information

        Returns:
            Enhanced context with integration suggestions
        """
        if not app_context:
            return prompt_analysis

        self.logger.debug("Enhancing with application context")

        # Analyze available services for integration
        available_services = app_context.get("services", [])
        integration_suggestions = self._suggest_service_integrations(
            prompt_analysis["service_requirements"], available_services
        )
        
        # Analyze available events for integration
        available_events = app_context.get("events", [])
        event_suggestions = self._suggest_event_integrations(
            prompt_analysis["event_requirements"], available_events
        )
        
        # Suggest dependencies based on context
        dependency_suggestions = self._suggest_dependencies(
            prompt_analysis, app_context
        )

        enhanced = prompt_analysis.copy()
        enhanced.update({
            "app_context": app_context,
            "integration_suggestions": integration_suggestions,
            "event_suggestions": event_suggestions,
            "dependency_suggestions": dependency_suggestions,
            "context_compatibility_score": self._calculate_compatibility_score(
                prompt_analysis, app_context
            )
        })

        return enhanced

    async def _create_structured_prompt(
        self,
        enhanced_context: dict[str, Any],
        generation_options: dict[str, Any] | None
    ) -> dict[str, Any]:
        """Create structured prompt for LLM generation.

        Args:
            enhanced_context: Enhanced prompt context
            generation_options: Generation options

        Returns:
            Structured prompt for LLM
        """
        options = generation_options or {}
        
        system_prompt = self._build_system_prompt(enhanced_context, options)
        user_prompt = self._build_user_prompt(enhanced_context, options)
        constraints = self._build_constraints(enhanced_context, options)

        return {
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "constraints": constraints,
            "temperature": options.get("temperature", DEFAULT_GENERATION_TEMPERATURE),
            "max_tokens": options.get("max_tokens", 2000),
            "ebnf_grammar": self._get_plugin_grammar()
        }

    def _extract_plugin_type(self, prompt: str) -> str:
        """Extract plugin type from prompt."""
        prompt_lower = prompt.lower()
        
        if any(word in prompt_lower for word in ["service", "api", "endpoint"]):
            return "service"
        elif any(word in prompt_lower for word in ["event", "listener", "handler"]):
            return "event"
        elif any(word in prompt_lower for word in ["cache", "storage", "database"]):
            return "storage"
        elif any(word in prompt_lower for word in ["auth", "security", "login"]):
            return "auth"
        else:
            return "general"

    def _extract_service_requirements(self, prompt: str) -> list[str]:
        """Extract service requirements from prompt."""
        # Simple keyword-based extraction
        services = []
        prompt_lower = prompt.lower()
        
        if "email" in prompt_lower:
            services.append("send_email")
        if "auth" in prompt_lower or "login" in prompt_lower:
            services.append("authenticate")
        if "cache" in prompt_lower:
            services.append("cache_data")
        if "log" in prompt_lower:
            services.append("log_activity")
            
        return services

    def _extract_event_requirements(self, prompt: str) -> list[str]:
        """Extract event requirements from prompt."""
        events = []
        prompt_lower = prompt.lower()
        
        if "startup" in prompt_lower or "init" in prompt_lower:
            events.append("app.startup")
        if "shutdown" in prompt_lower or "cleanup" in prompt_lower:
            events.append("app.shutdown")
        if "user" in prompt_lower and "login" in prompt_lower:
            events.append("user.login")
            
        return events

    def _extract_dependency_hints(self, prompt: str) -> list[str]:
        """Extract dependency hints from prompt."""
        dependencies = []
        prompt_lower = prompt.lower()
        
        if "log" in prompt_lower:
            dependencies.append("logger")
        if "config" in prompt_lower:
            dependencies.append("config")
        if "database" in prompt_lower or "db" in prompt_lower:
            dependencies.append("database")
            
        return dependencies

    def _calculate_complexity_score(
        self,
        prompt: str,
        services: list[str],
        events: list[str]
    ) -> float:
        """Calculate complexity score for prompt."""
        base_score = min(len(prompt) / 200, 1.0)  # Length factor
        service_factor = min(len(services) * 0.2, 0.5)  # Service complexity
        event_factor = min(len(events) * 0.1, 0.3)  # Event complexity
        
        return min(base_score + service_factor + event_factor, 1.0)

    def _extract_technical_keywords(self, prompt: str) -> list[str]:
        """Extract technical keywords from prompt."""
        technical_terms = [
            "api", "service", "auth", "cache", "database", "email", "log",
            "event", "handler", "async", "sync", "rest", "json", "xml"
        ]
        
        prompt_lower = prompt.lower()
        return [term for term in technical_terms if term in prompt_lower]

    def _suggest_service_integrations(
        self,
        required_services: list[str],
        available_services: list[str]
    ) -> list[dict[str, Any]]:
        """Suggest service integrations."""
        suggestions = []
        
        for required in required_services:
            for available in available_services:
                if self._services_compatible(required, available):
                    suggestions.append({
                        "required_service": required,
                        "available_service": available,
                        "integration_type": "dependency",
                        "confidence": 0.8
                    })
                    
        return suggestions

    def _suggest_event_integrations(
        self,
        required_events: list[str],
        available_events: list[str]
    ) -> list[dict[str, Any]]:
        """Suggest event integrations."""
        suggestions = []
        
        for required in required_events:
            if required in available_events:
                suggestions.append({
                    "event": required,
                    "integration_type": "listener",
                    "confidence": 0.9
                })
                
        return suggestions

    def _suggest_dependencies(
        self,
        prompt_analysis: dict[str, Any],
        app_context: dict[str, Any]
    ) -> list[str]:
        """Suggest plugin dependencies."""
        suggestions = prompt_analysis["dependency_hints"].copy()
        
        # Add common dependencies based on plugin type
        plugin_type = prompt_analysis["plugin_type"]
        if plugin_type == "service":
            suggestions.extend(["logger", "config"])
        elif plugin_type == "auth":
            suggestions.extend(["logger", "config", "security"])
            
        return list(set(suggestions))  # Remove duplicates

    def _calculate_compatibility_score(
        self,
        prompt_analysis: dict[str, Any],
        app_context: dict[str, Any]
    ) -> float:
        """Calculate compatibility score with app context."""
        # Simple compatibility scoring
        available_services = set(app_context.get("services", []))
        required_services = set(prompt_analysis["service_requirements"])
        
        if not required_services:
            return 1.0
            
        compatible_count = len(required_services.intersection(available_services))
        return compatible_count / len(required_services)

    def _services_compatible(self, required: str, available: str) -> bool:
        """Check if services are compatible."""
        # Simple compatibility check
        compatibility_map = {
            "send_email": ["email", "notification"],
            "authenticate": ["auth", "security", "user"],
            "cache_data": ["cache", "storage"],
            "log_activity": ["logger", "logging"]
        }
        
        compatible_services = compatibility_map.get(required, [])
        return any(compat in available.lower() for compat in compatible_services)

    def _build_system_prompt(
        self,
        enhanced_context: dict[str, Any],
        options: dict[str, Any]
    ) -> str:
        """Build system prompt for LLM."""
        return f"""You are an expert Plugginger plugin developer. Generate a plugin specification based on the user's requirements.

Plugin Type: {enhanced_context.get('plugin_type', 'general')}
Complexity Score: {enhanced_context.get('complexity_score', 0.5)}
Available Services: {enhanced_context.get('app_context', {}).get('services', [])}
Integration Suggestions: {enhanced_context.get('integration_suggestions', [])}

Generate a complete plugin specification with proper services, events, and dependencies.
Follow Plugginger best practices and ensure type safety."""

    def _build_user_prompt(
        self,
        enhanced_context: dict[str, Any],
        options: dict[str, Any]
    ) -> str:
        """Build user prompt for LLM."""
        return f"Create a plugin specification for: {enhanced_context.get('original_prompt', 'Unknown requirement')}"

    def _build_constraints(
        self,
        enhanced_context: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Build generation constraints."""
        return {
            "max_services": options.get("max_services", 5),
            "max_events": options.get("max_events", 3),
            "max_dependencies": options.get("max_dependencies", 5),
            "include_tests": options.get("include_tests", True),
            "include_docs": options.get("include_docs", True),
            "code_style": options.get("code_style", "pep8")
        }

    def _get_plugin_grammar(self) -> str:
        """Get EBNF grammar for plugin specification."""
        return """
        plugin_spec ::= "{" 
            "\"name\":" string "," 
            "\"description\":" string "," 
            "\"class_name\":" string "," 
            "\"services\":" service_list "," 
            "\"event_listeners\":" event_list "," 
            "\"dependencies\":" dependency_list 
        "}"
        
        service_list ::= "[" (service ("," service)*)? "]"
        service ::= "{" "\"name\":" string "," "\"description\":" string "}"
        
        event_list ::= "[" (event ("," event)*)? "]"
        event ::= "{" "\"event\":" string "," "\"handler\":" string "}"
        
        dependency_list ::= "[" (dependency ("," dependency)*)? "]"
        dependency ::= "{" "\"name\":" string "," "\"version\":" string "}"
        
        string ::= "\"" [^"]* "\""
        """

    async def _validate_prompt_structure(self, structured_prompt: dict[str, Any]) -> dict[str, Any]:
        """Validate structured prompt."""
        validation_errors = []
        
        if not structured_prompt.get("system_prompt"):
            validation_errors.append("Missing system prompt")
        if not structured_prompt.get("user_prompt"):
            validation_errors.append("Missing user prompt")
        if not structured_prompt.get("ebnf_grammar"):
            validation_errors.append("Missing EBNF grammar")
            
        return {
            "valid": len(validation_errors) == 0,
            "errors": validation_errors,
            "prompt_length": len(str(structured_prompt)),
            "complexity_estimate": "medium"
        }
