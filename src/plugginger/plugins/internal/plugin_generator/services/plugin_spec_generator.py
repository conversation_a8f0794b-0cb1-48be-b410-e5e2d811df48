"""
Plugin specification generator service.

Converts LLM outputs into structured plugin specifications with validation,
type checking, and integration analysis.
"""

import json
import logging
from typing import Any

from plugginger.core.exceptions import PluggingerValidationError

logger = logging.getLogger(__name__)

# --- Plugin Spec Generator Constants ---

# Maximum number of services per plugin
MAX_SERVICES_PER_PLUGIN: int = 10

# Maximum number of event listeners per plugin
MAX_EVENT_LISTENERS_PER_PLUGIN: int = 8

# Maximum number of dependencies per plugin
MAX_DEPENDENCIES_PER_PLUGIN: int = 15

# Default plugin version for generated specs
DEFAULT_PLUGIN_VERSION: str = "1.0.0"


class PluginSpecGenerator:
    """Generates structured plugin specifications from LLM outputs."""

    def __init__(self, app: Any) -> None:
        """Initialize plugin spec generator.

        Args:
            app: Plugginger application instance
        """
        self.app = app
        self.logger = logger

    async def generate_plugin_spec(
        self,
        llm_output: str,
        prompt_context: dict[str, Any],
        validation_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Generate structured plugin specification from LLM output.

        Args:
            llm_output: Raw LLM output containing plugin specification
            prompt_context: Original prompt context for validation
            validation_options: Additional validation options

        Returns:
            Structured and validated plugin specification

        Example:
            result = await generator.generate_plugin_spec(
                llm_output='{"name": "email_service", "description": "Email handling"}',
                prompt_context={"plugin_type": "service"},
                validation_options={"strict_validation": True}
            )
        """
        self.logger.info("Generating plugin specification from LLM output")

        try:
            # Parse LLM output into structured data
            parsed_spec = await self._parse_llm_output(llm_output)
            
            # Validate basic structure
            structure_validation = await self._validate_spec_structure(parsed_spec)
            if not structure_validation["valid"]:
                raise PluggingerValidationError(f"Invalid spec structure: {structure_validation['errors']}")
            
            # Enhance specification with defaults and improvements
            enhanced_spec = await self._enhance_plugin_spec(parsed_spec, prompt_context)
            
            # Validate enhanced specification
            enhanced_validation = await self._validate_enhanced_spec(
                enhanced_spec, validation_options
            )
            
            # Generate metadata
            spec_metadata = await self._generate_spec_metadata(
                enhanced_spec, prompt_context
            )
            
            return {
                "success": True,
                "plugin_spec": enhanced_spec,
                "validation": enhanced_validation,
                "metadata": spec_metadata,
                "generation_info": {
                    "source": "llm",
                    "original_output": llm_output[:200] + "..." if len(llm_output) > 200 else llm_output,
                    "enhancements_applied": self._get_applied_enhancements(parsed_spec, enhanced_spec)
                }
            }

        except Exception as e:
            self.logger.error(f"Plugin spec generation error: {e}")
            raise PluggingerValidationError(f"Plugin spec generation failed: {e}") from e

    async def _parse_llm_output(self, llm_output: str) -> dict[str, Any]:
        """Parse LLM output into structured data.

        Args:
            llm_output: Raw LLM output

        Returns:
            Parsed plugin specification
        """
        self.logger.debug("Parsing LLM output")

        try:
            # Try direct JSON parsing first
            if llm_output.strip().startswith("{"):
                parsed_json: dict[str, Any] = json.loads(llm_output)
                return parsed_json
            
            # Extract JSON from markdown code blocks
            if "```json" in llm_output:
                start = llm_output.find("```json") + 7
                end = llm_output.find("```", start)
                json_content = llm_output[start:end].strip()
                markdown_json: dict[str, Any] = json.loads(json_content)
                return markdown_json

            # Extract JSON from text
            start = llm_output.find("{")
            end = llm_output.rfind("}") + 1
            if start >= 0 and end > start:
                json_content = llm_output[start:end]
                extracted_json: dict[str, Any] = json.loads(json_content)
                return extracted_json
            
            raise ValueError("No valid JSON found in LLM output")

        except (json.JSONDecodeError, ValueError) as e:
            self.logger.warning(f"Failed to parse LLM output as JSON: {e}")
            # Fallback: create basic spec from text analysis
            return await self._create_fallback_spec(llm_output)

    async def _create_fallback_spec(self, llm_output: str) -> dict[str, Any]:
        """Create fallback specification from unparseable LLM output.

        Args:
            llm_output: Raw LLM output

        Returns:
            Basic plugin specification
        """
        self.logger.info("Creating fallback specification")

        # Extract plugin name from output
        plugin_name = self._extract_plugin_name(llm_output)
        
        # Extract description
        description = self._extract_description(llm_output)
        
        # Extract services
        services = self._extract_services_from_text(llm_output)
        
        # Extract events
        events = self._extract_events_from_text(llm_output)

        return {
            "name": plugin_name,
            "description": description,
            "class_name": f"{plugin_name.title().replace('_', '')}Plugin",
            "services": services,
            "event_listeners": events,
            "dependencies": ["logger"]  # Default dependency
        }

    async def _validate_spec_structure(self, spec: dict[str, Any]) -> dict[str, Any]:
        """Validate basic plugin specification structure.

        Args:
            spec: Plugin specification to validate

        Returns:
            Validation result
        """
        errors = []
        warnings = []

        # Required fields
        required_fields = ["name", "description", "class_name"]
        for field in required_fields:
            if not spec.get(field):
                errors.append(f"Missing required field: {field}")

        # Optional but recommended fields
        recommended_fields = ["services", "event_listeners", "dependencies"]
        for field in recommended_fields:
            if field not in spec:
                warnings.append(f"Missing recommended field: {field}")

        # Validate field types
        if "services" in spec and not isinstance(spec["services"], list):
            errors.append("Services must be a list")
        
        if "event_listeners" in spec and not isinstance(spec["event_listeners"], list):
            errors.append("Event listeners must be a list")
        
        if "dependencies" in spec and not isinstance(spec["dependencies"], list):
            errors.append("Dependencies must be a list")

        # Validate limits
        if len(spec.get("services", [])) > MAX_SERVICES_PER_PLUGIN:
            warnings.append(f"Too many services ({len(spec['services'])} > {MAX_SERVICES_PER_PLUGIN})")
        
        if len(spec.get("event_listeners", [])) > MAX_EVENT_LISTENERS_PER_PLUGIN:
            warnings.append(f"Too many event listeners ({len(spec['event_listeners'])} > {MAX_EVENT_LISTENERS_PER_PLUGIN})")
        
        if len(spec.get("dependencies", [])) > MAX_DEPENDENCIES_PER_PLUGIN:
            warnings.append(f"Too many dependencies ({len(spec['dependencies'])} > {MAX_DEPENDENCIES_PER_PLUGIN})")

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings,
            "score": max(0.0, 1.0 - (len(errors) * 0.3) - (len(warnings) * 0.1))
        }

    async def _enhance_plugin_spec(
        self,
        spec: dict[str, Any],
        prompt_context: dict[str, Any]
    ) -> dict[str, Any]:
        """Enhance plugin specification with defaults and improvements.

        Args:
            spec: Basic plugin specification
            prompt_context: Original prompt context

        Returns:
            Enhanced plugin specification
        """
        enhanced = spec.copy()

        # Add version if missing
        if "version" not in enhanced:
            enhanced["version"] = DEFAULT_PLUGIN_VERSION

        # Ensure services have proper structure
        if "services" in enhanced:
            enhanced["services"] = [
                self._normalize_service(service) for service in enhanced["services"]
            ]

        # Ensure event listeners have proper structure
        if "event_listeners" in enhanced:
            enhanced["event_listeners"] = [
                self._normalize_event_listener(event) for event in enhanced["event_listeners"]
            ]

        # Ensure dependencies have proper structure
        if "dependencies" in enhanced:
            enhanced["dependencies"] = [
                self._normalize_dependency(dep) for dep in enhanced["dependencies"]
            ]

        # Add common dependencies based on plugin type
        plugin_type = prompt_context.get("plugin_type", "general")
        enhanced["dependencies"] = self._add_common_dependencies(
            enhanced.get("dependencies", []), plugin_type
        )

        # Add metadata
        enhanced["metadata"] = {
            "plugin_type": plugin_type,
            "complexity_score": prompt_context.get("complexity_score", 0.5),
            "generated_at": "2024-12-05T00:00:00Z",
            "generator_version": "1.0.0"
        }

        return enhanced

    def _normalize_service(self, service: Any) -> dict[str, Any]:
        """Normalize service specification."""
        if isinstance(service, str):
            return {
                "name": service,
                "description": f"Service: {service}",
                "timeout_seconds": 30.0
            }
        elif isinstance(service, dict):
            normalized = service.copy()
            if "timeout_seconds" not in normalized:
                normalized["timeout_seconds"] = 30.0
            return normalized
        else:
            return {
                "name": "unknown_service",
                "description": "Unknown service",
                "timeout_seconds": 30.0
            }

    def _normalize_event_listener(self, event: Any) -> dict[str, Any]:
        """Normalize event listener specification."""
        if isinstance(event, str):
            return {
                "event": event,
                "handler": f"handle_{event.replace('.', '_')}"
            }
        elif isinstance(event, dict):
            return event
        else:
            return {
                "event": "unknown.event",
                "handler": "handle_unknown_event"
            }

    def _normalize_dependency(self, dependency: Any) -> dict[str, Any]:
        """Normalize dependency specification."""
        if isinstance(dependency, str):
            return {
                "name": dependency,
                "version": ">=1.0.0",
                "optional": False
            }
        elif isinstance(dependency, dict):
            normalized = dependency.copy()
            if "version" not in normalized:
                normalized["version"] = ">=1.0.0"
            if "optional" not in normalized:
                normalized["optional"] = False
            return normalized
        else:
            return {
                "name": "unknown_dependency",
                "version": ">=1.0.0",
                "optional": True
            }

    def _add_common_dependencies(
        self,
        dependencies: list[dict[str, Any]],
        plugin_type: str
    ) -> list[dict[str, Any]]:
        """Add common dependencies based on plugin type."""
        existing_names = {dep["name"] for dep in dependencies}
        
        # Common dependencies for all plugins
        common_deps = ["logger"]
        
        # Type-specific dependencies
        if plugin_type == "service":
            common_deps.extend(["config"])
        elif plugin_type == "auth":
            common_deps.extend(["config", "security"])
        elif plugin_type == "storage":
            common_deps.extend(["config", "database"])

        # Add missing common dependencies
        for dep_name in common_deps:
            if dep_name not in existing_names:
                dependencies.append({
                    "name": dep_name,
                    "version": ">=1.0.0",
                    "optional": False
                })

        return dependencies

    async def _validate_enhanced_spec(
        self,
        spec: dict[str, Any],
        validation_options: dict[str, Any] | None
    ) -> dict[str, Any]:
        """Validate enhanced plugin specification."""
        options = validation_options or {}
        
        validation_errors = []
        validation_warnings = []

        # Validate naming conventions
        if not self._is_valid_plugin_name(spec.get("name", "")):
            validation_errors.append("Invalid plugin name format")

        # Validate class name
        if not self._is_valid_class_name(spec.get("class_name", "")):
            validation_errors.append("Invalid class name format")

        # Validate services
        for service in spec.get("services", []):
            if not self._is_valid_service_name(service.get("name", "")):
                validation_warnings.append(f"Invalid service name: {service.get('name')}")

        # Validate dependencies
        for dep in spec.get("dependencies", []):
            if not self._is_valid_dependency_name(dep.get("name", "")):
                validation_warnings.append(f"Invalid dependency name: {dep.get('name')}")

        return {
            "valid": len(validation_errors) == 0,
            "errors": validation_errors,
            "warnings": validation_warnings,
            "quality_score": self._calculate_quality_score(spec),
            "recommendations": self._generate_recommendations(spec)
        }

    async def _generate_spec_metadata(
        self,
        spec: dict[str, Any],
        prompt_context: dict[str, Any]
    ) -> dict[str, Any]:
        """Generate metadata for plugin specification."""
        return {
            "generation_method": "llm_enhanced",
            "prompt_complexity": prompt_context.get("complexity_score", 0.5),
            "service_count": len(spec.get("services", [])),
            "event_count": len(spec.get("event_listeners", [])),
            "dependency_count": len(spec.get("dependencies", [])),
            "estimated_lines_of_code": self._estimate_code_size(spec),
            "integration_complexity": self._calculate_integration_complexity(spec)
        }

    def _extract_plugin_name(self, text: str) -> str:
        """Extract plugin name from text."""
        # Simple extraction logic
        words = text.lower().split()
        if "plugin" in words:
            idx = words.index("plugin")
            if idx > 0:
                return f"{words[idx-1]}_plugin"
        
        # Fallback
        return "generated_plugin"

    def _extract_description(self, text: str) -> str:
        """Extract description from text."""
        # Take first sentence or first 100 characters
        sentences = text.split(".")
        if sentences:
            return sentences[0].strip()[:100]
        return "Generated plugin"

    def _extract_services_from_text(self, text: str) -> list[dict[str, Any]]:
        """Extract services from text."""
        services = []
        text_lower = text.lower()
        
        # Look for service keywords
        service_keywords = ["send", "get", "create", "update", "delete", "process"]
        for keyword in service_keywords:
            if keyword in text_lower:
                services.append({
                    "name": f"{keyword}_data",
                    "description": f"Service to {keyword} data",
                    "timeout_seconds": 30.0
                })
        
        return services[:3]  # Limit to 3 services

    def _extract_events_from_text(self, text: str) -> list[dict[str, Any]]:
        """Extract events from text."""
        events = []
        text_lower = text.lower()
        
        if "startup" in text_lower:
            events.append({"event": "app.startup", "handler": "handle_startup"})
        if "shutdown" in text_lower:
            events.append({"event": "app.shutdown", "handler": "handle_shutdown"})
        
        return events

    def _is_valid_plugin_name(self, name: str) -> bool:
        """Validate plugin name format."""
        return bool(name and name.replace("_", "").isalnum() and not name.startswith("_"))

    def _is_valid_class_name(self, name: str) -> bool:
        """Validate class name format."""
        return bool(name and name.isidentifier() and name[0].isupper())

    def _is_valid_service_name(self, name: str) -> bool:
        """Validate service name format."""
        return bool(name and name.replace("_", "").isalnum())

    def _is_valid_dependency_name(self, name: str) -> bool:
        """Validate dependency name format."""
        return bool(name and name.replace("_", "").isalnum())

    def _calculate_quality_score(self, spec: dict[str, Any]) -> float:
        """Calculate quality score for specification."""
        score = 1.0
        
        # Deduct for missing descriptions
        if not spec.get("description"):
            score -= 0.2
        
        # Deduct for empty services
        if not spec.get("services"):
            score -= 0.1
        
        # Bonus for good structure
        if spec.get("metadata"):
            score += 0.1
        
        return max(0.0, min(1.0, score))

    def _generate_recommendations(self, spec: dict[str, Any]) -> list[str]:
        """Generate recommendations for improving specification."""
        recommendations = []
        
        if len(spec.get("services", [])) == 0:
            recommendations.append("Consider adding at least one service")
        
        if len(spec.get("dependencies", [])) == 0:
            recommendations.append("Consider adding logger dependency")
        
        if not spec.get("description") or len(spec["description"]) < 20:
            recommendations.append("Add a more detailed description")
        
        return recommendations

    def _estimate_code_size(self, spec: dict[str, Any]) -> int:
        """Estimate lines of code for specification."""
        base_size = 50  # Base plugin structure
        service_size = len(spec.get("services", [])) * 15  # ~15 lines per service
        event_size = len(spec.get("event_listeners", [])) * 10  # ~10 lines per event
        
        return base_size + service_size + event_size

    def _calculate_integration_complexity(self, spec: dict[str, Any]) -> str:
        """Calculate integration complexity."""
        dependency_count = len(spec.get("dependencies", []))
        service_count = len(spec.get("services", []))
        
        total_complexity = dependency_count + service_count
        
        if total_complexity <= 3:
            return "low"
        elif total_complexity <= 7:
            return "medium"
        else:
            return "high"

    def _get_applied_enhancements(
        self,
        original: dict[str, Any],
        enhanced: dict[str, Any]
    ) -> list[str]:
        """Get list of applied enhancements."""
        enhancements = []
        
        if "version" not in original and "version" in enhanced:
            enhancements.append("Added version")
        
        if "metadata" not in original and "metadata" in enhanced:
            enhancements.append("Added metadata")
        
        if len(enhanced.get("dependencies", [])) > len(original.get("dependencies", [])):
            enhancements.append("Added common dependencies")
        
        return enhancements
