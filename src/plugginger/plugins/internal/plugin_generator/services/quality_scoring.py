"""
Quality scoring service for generated plugins.

Provides comprehensive quality assessment, best practices validation,
and improvement recommendations for generated plugins.
"""

import logging
import re
from typing import Any

from plugginger.core.exceptions import PluggingerValidationError

logger = logging.getLogger(__name__)

# --- Quality Scoring Constants ---

# Minimum quality score for acceptable plugins
MIN_ACCEPTABLE_QUALITY_SCORE: float = 0.7

# Maximum quality score (perfect plugin)
MAX_QUALITY_SCORE: float = 1.0

# Weight factors for different quality aspects
QUALITY_WEIGHTS: dict[str, float] = {
    "code_structure": 0.25,
    "documentation": 0.20,
    "type_safety": 0.20,
    "best_practices": 0.15,
    "test_coverage": 0.10,
    "performance": 0.10
}

# Minimum lines of documentation per service
MIN_DOCS_LINES_PER_SERVICE: int = 3


class QualityScoring:
    """Quality assessment and scoring for generated plugins."""

    def __init__(self, app: Any) -> None:
        """Initialize quality scoring service.

        Args:
            app: Plugginger application instance
        """
        self.app = app
        self.logger = logger

    async def assess_plugin_quality(
        self,
        plugin_spec: dict[str, Any],
        generated_code: str,
        test_code: str,
        manifest_code: str,
        assessment_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Assess overall quality of generated plugin.

        Args:
            plugin_spec: Plugin specification
            generated_code: Generated plugin code
            test_code: Generated test code
            manifest_code: Generated manifest code
            assessment_options: Quality assessment options

        Returns:
            Comprehensive quality assessment with scores and recommendations

        Example:
            result = await scorer.assess_plugin_quality(
                plugin_spec={"name": "email_service", "services": [...]},
                generated_code="class EmailPlugin...",
                test_code="class TestEmailPlugin...",
                manifest_code="name: email_service...",
                assessment_options={"strict_mode": True}
            )
        """
        self.logger.info(f"Assessing quality for plugin: {plugin_spec.get('name', 'unknown')}")

        try:
            options = assessment_options or {}
            
            # Assess code structure quality
            structure_assessment = await self._assess_code_structure(
                generated_code, plugin_spec, options
            )
            
            # Assess documentation quality
            documentation_assessment = await self._assess_documentation_quality(
                generated_code, plugin_spec, options
            )
            
            # Assess type safety
            type_safety_assessment = await self._assess_type_safety(
                generated_code, plugin_spec, options
            )
            
            # Assess best practices compliance
            best_practices_assessment = await self._assess_best_practices(
                generated_code, plugin_spec, options
            )
            
            # Assess test coverage
            test_coverage_assessment = await self._assess_test_coverage(
                test_code, plugin_spec, options
            )
            
            # Assess performance considerations
            performance_assessment = await self._assess_performance(
                generated_code, plugin_spec, options
            )
            
            # Calculate overall quality score
            overall_score = await self._calculate_overall_score({
                "code_structure": structure_assessment,
                "documentation": documentation_assessment,
                "type_safety": type_safety_assessment,
                "best_practices": best_practices_assessment,
                "test_coverage": test_coverage_assessment,
                "performance": performance_assessment
            })
            
            # Generate improvement recommendations
            recommendations = await self._generate_improvement_recommendations({
                "code_structure": structure_assessment,
                "documentation": documentation_assessment,
                "type_safety": type_safety_assessment,
                "best_practices": best_practices_assessment,
                "test_coverage": test_coverage_assessment,
                "performance": performance_assessment
            }, overall_score)
            
            # Determine quality grade
            quality_grade = self._determine_quality_grade(overall_score)
            
            return {
                "success": True,
                "plugin_name": plugin_spec.get("name"),
                "overall_score": overall_score,
                "quality_grade": quality_grade,
                "assessments": {
                    "code_structure": structure_assessment,
                    "documentation": documentation_assessment,
                    "type_safety": type_safety_assessment,
                    "best_practices": best_practices_assessment,
                    "test_coverage": test_coverage_assessment,
                    "performance": performance_assessment
                },
                "recommendations": recommendations,
                "quality_metadata": {
                    "assessment_timestamp": "2024-12-05T00:00:00Z",
                    "min_acceptable_score": MIN_ACCEPTABLE_QUALITY_SCORE,
                    "passes_quality_gate": overall_score >= MIN_ACCEPTABLE_QUALITY_SCORE,
                    "assessment_options": options
                }
            }

        except Exception as e:
            self.logger.error(f"Quality assessment error: {e}")
            raise PluggingerValidationError(f"Quality assessment failed: {e}") from e

    async def _assess_code_structure(
        self,
        code: str,
        plugin_spec: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Assess code structure quality.

        Args:
            code: Generated code to assess
            plugin_spec: Plugin specification
            options: Assessment options

        Returns:
            Code structure assessment
        """
        self.logger.debug("Assessing code structure")

        issues = []
        score = 1.0
        
        # Check class definition
        if not re.search(r'class\s+\w+Plugin\(PluginBase\):', code):
            issues.append("Missing proper plugin class definition")
            score -= 0.3
        
        # Check plugin decorator
        if not re.search(r'@plugin\(name=', code):
            issues.append("Missing @plugin decorator")
            score -= 0.2
        
        # Check __init__ method
        if not re.search(r'def __init__\(self,.*\*\*injected_dependencies', code):
            issues.append("Missing proper __init__ method with DI support")
            score -= 0.2
        
        # Check service decorators
        expected_services = len(plugin_spec.get("services", []))
        actual_services = len(re.findall(r'@service\(name=', code))
        if actual_services != expected_services:
            issues.append(f"Service count mismatch: expected {expected_services}, found {actual_services}")
            score -= 0.1
        
        # Check imports
        required_imports = ["PluginBase", "plugin", "service"]
        for import_item in required_imports:
            if import_item not in code:
                issues.append(f"Missing required import: {import_item}")
                score -= 0.05
        
        # Check line length
        long_lines = [i for i, line in enumerate(code.split('\n'), 1) if len(line) > 88]
        if long_lines:
            issues.append(f"Lines too long (>88 chars): {len(long_lines)} lines")
            score -= min(0.1, len(long_lines) * 0.01)
        
        return {
            "score": max(0.0, score),
            "issues": issues,
            "metrics": {
                "class_definition_found": "class" in code and "PluginBase" in code,
                "plugin_decorator_found": "@plugin" in code,
                "init_method_proper": "__init__" in code and "injected_dependencies" in code,
                "service_count_match": actual_services == expected_services,
                "long_lines_count": len(long_lines),
                "total_lines": len(code.split('\n'))
            }
        }

    async def _assess_documentation_quality(
        self,
        code: str,
        plugin_spec: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Assess documentation quality.

        Args:
            code: Generated code to assess
            plugin_spec: Plugin specification
            options: Assessment options

        Returns:
            Documentation assessment
        """
        self.logger.debug("Assessing documentation quality")

        issues = []
        score = 1.0
        
        # Check module docstring
        if not re.search(r'"""[\s\S]*?"""', code):
            issues.append("Missing module docstring")
            score -= 0.2
        
        # Check class docstring
        if not re.search(r'class.*?:\s*"""[\s\S]*?"""', code, re.DOTALL):
            issues.append("Missing class docstring")
            score -= 0.2
        
        # Check method docstrings
        methods = re.findall(r'def\s+(\w+)\(', code)
        documented_methods = len(re.findall(r'def\s+\w+\([^)]*\):\s*"""', code))
        
        if methods:
            documentation_ratio = documented_methods / len(methods)
            if documentation_ratio < 0.8:
                issues.append(f"Low method documentation ratio: {documentation_ratio:.1%}")
                score -= (0.8 - documentation_ratio) * 0.3
        
        # Check service documentation
        services = plugin_spec.get("services", [])
        for service in services:
            service_name = service.get("name") if isinstance(service, dict) else service
            service_pattern = rf'def\s+{service_name}\([^)]*\):\s*"""[\s\S]*?"""'
            if not re.search(service_pattern, code, re.DOTALL):
                issues.append(f"Missing documentation for service: {service_name}")
                score -= 0.1
        
        # Check for example usage in docstrings
        example_count = len(re.findall(r'Example:', code))
        expected_examples = len(services)
        if example_count < expected_examples:
            issues.append(f"Missing usage examples: {example_count}/{expected_examples}")
            score -= 0.1
        
        return {
            "score": max(0.0, score),
            "issues": issues,
            "metrics": {
                "has_module_docstring": '"""' in code,
                "has_class_docstring": bool(re.search(r'class.*?:\s*"""', code)),
                "method_documentation_ratio": documentation_ratio if methods else 1.0,
                "service_documentation_complete": len(issues) == 0,
                "example_count": example_count,
                "expected_examples": expected_examples
            }
        }

    async def _assess_type_safety(
        self,
        code: str,
        plugin_spec: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Assess type safety.

        Args:
            code: Generated code to assess
            plugin_spec: Plugin specification
            options: Assessment options

        Returns:
            Type safety assessment
        """
        self.logger.debug("Assessing type safety")

        issues = []
        score = 1.0
        
        # Check typing imports
        if "from typing import" not in code:
            issues.append("Missing typing imports")
            score -= 0.2
        
        # Check method return type annotations
        methods = re.findall(r'def\s+\w+\([^)]*\)', code)
        typed_methods = re.findall(r'def\s+\w+\([^)]*\)\s*->', code)
        
        if methods:
            typing_ratio = len(typed_methods) / len(methods)
            if typing_ratio < 0.9:
                issues.append(f"Low method typing ratio: {typing_ratio:.1%}")
                score -= (0.9 - typing_ratio) * 0.3
        
        # Check parameter type annotations
        untyped_params = re.findall(r'def\s+\w+\([^)]*[^:]\s*,', code)
        if untyped_params:
            issues.append(f"Found {len(untyped_params)} untyped parameters")
            score -= min(0.2, len(untyped_params) * 0.05)
        
        # Check for Any usage (should be minimal)
        any_usage = len(re.findall(r'\bAny\b', code))
        if any_usage > 3:  # Allow some Any usage for flexibility
            issues.append(f"Excessive Any usage: {any_usage} occurrences")
            score -= min(0.1, (any_usage - 3) * 0.02)
        
        # Check for proper exception typing
        if "Exception" in code and "PluggingerValidationError" not in code:
            issues.append("Using generic Exception instead of specific exception types")
            score -= 0.1
        
        return {
            "score": max(0.0, score),
            "issues": issues,
            "metrics": {
                "has_typing_imports": "from typing import" in code,
                "method_typing_ratio": typing_ratio if methods else 1.0,
                "untyped_parameters": len(untyped_params),
                "any_usage_count": any_usage,
                "uses_specific_exceptions": "PluggingerValidationError" in code
            }
        }

    async def _assess_best_practices(
        self,
        code: str,
        plugin_spec: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Assess best practices compliance.

        Args:
            code: Generated code to assess
            plugin_spec: Plugin specification
            options: Assessment options

        Returns:
            Best practices assessment
        """
        self.logger.debug("Assessing best practices")

        issues = []
        score = 1.0
        
        # Check logging usage
        if "self.logger" not in code:
            issues.append("Missing logger usage")
            score -= 0.2
        
        # Check error handling
        try_except_count = len(re.findall(r'try:', code))
        service_count = len(plugin_spec.get("services", []))
        if try_except_count < service_count:
            issues.append("Insufficient error handling in services")
            score -= 0.2
        
        # Check constants usage
        if "DEFAULT_" not in code and service_count > 0:
            issues.append("Missing named constants")
            score -= 0.1
        
        # Check async/await usage
        async_methods = len(re.findall(r'async def', code))
        await_usage = len(re.findall(r'await', code))
        if async_methods > 0 and await_usage == 0:
            issues.append("Async methods without await usage")
            score -= 0.1
        
        # Check super() call in __init__
        if "__init__" in code and "super().__init__" not in code:
            issues.append("Missing super().__init__ call")
            score -= 0.1
        
        # Check for hardcoded values
        hardcoded_strings = re.findall(r'"[^"]{10,}"', code)  # Long strings might be hardcoded
        if len(hardcoded_strings) > 3:
            issues.append(f"Potential hardcoded values: {len(hardcoded_strings)}")
            score -= 0.05
        
        return {
            "score": max(0.0, score),
            "issues": issues,
            "metrics": {
                "uses_logger": "self.logger" in code,
                "error_handling_ratio": try_except_count / max(1, service_count),
                "uses_constants": "DEFAULT_" in code,
                "async_await_balance": await_usage >= async_methods if async_methods > 0 else True,
                "proper_inheritance": "super().__init__" in code,
                "hardcoded_values_count": len(hardcoded_strings)
            }
        }

    async def _assess_test_coverage(
        self,
        test_code: str,
        plugin_spec: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Assess test coverage.

        Args:
            test_code: Generated test code
            plugin_spec: Plugin specification
            options: Assessment options

        Returns:
            Test coverage assessment
        """
        self.logger.debug("Assessing test coverage")

        issues = []
        score = 1.0
        
        if not test_code or len(test_code.strip()) == 0:
            issues.append("No test code generated")
            return {"score": 0.0, "issues": issues, "metrics": {}}
        
        # Check test class definition
        if not re.search(r'class Test\w+:', test_code):
            issues.append("Missing test class definition")
            score -= 0.3
        
        # Check test methods
        test_methods = len(re.findall(r'def test_\w+', test_code))
        services = plugin_spec.get("services", [])
        expected_tests = len(services) + 3  # Services + init + setup + teardown
        
        if test_methods < expected_tests:
            issues.append(f"Insufficient test methods: {test_methods}/{expected_tests}")
            score -= min(0.4, (expected_tests - test_methods) * 0.1)
        
        # Check pytest usage
        if "pytest" not in test_code:
            issues.append("Missing pytest imports")
            score -= 0.1
        
        # Check async test support
        if "pytest.mark.asyncio" not in test_code and "async def" in test_code:
            issues.append("Missing async test decorators")
            score -= 0.1
        
        # Check assertions
        assertion_count = len(re.findall(r'assert\s+', test_code))
        if assertion_count < test_methods:
            issues.append("Insufficient assertions in tests")
            score -= 0.1
        
        return {
            "score": max(0.0, score),
            "issues": issues,
            "metrics": {
                "has_test_class": "class Test" in test_code,
                "test_method_count": test_methods,
                "expected_test_count": expected_tests,
                "test_coverage_ratio": test_methods / max(1, expected_tests),
                "uses_pytest": "pytest" in test_code,
                "async_test_support": "pytest.mark.asyncio" in test_code,
                "assertion_count": assertion_count
            }
        }

    async def _assess_performance(
        self,
        code: str,
        plugin_spec: dict[str, Any],
        options: dict[str, Any]
    ) -> dict[str, Any]:
        """Assess performance considerations.

        Args:
            code: Generated code to assess
            plugin_spec: Plugin specification
            options: Assessment options

        Returns:
            Performance assessment
        """
        self.logger.debug("Assessing performance")

        issues = []
        score = 1.0
        
        # Check timeout configuration
        if "timeout" not in code.lower():
            issues.append("Missing timeout configuration")
            score -= 0.2
        
        # Check for potential blocking operations
        blocking_patterns = ["time.sleep", "requests.get", "open("]
        for pattern in blocking_patterns:
            if pattern in code:
                issues.append(f"Potential blocking operation: {pattern}")
                score -= 0.1
        
        # Check async usage for I/O operations
        if "def " in code and "async def" not in code and len(plugin_spec.get("services", [])) > 0:
            issues.append("Services should be async for better performance")
            score -= 0.2
        
        # Check for resource cleanup
        if "teardown" not in code and len(plugin_spec.get("services", [])) > 0:
            issues.append("Missing resource cleanup (teardown method)")
            score -= 0.1
        
        return {
            "score": max(0.0, score),
            "issues": issues,
            "metrics": {
                "has_timeout_config": "timeout" in code.lower(),
                "blocking_operations_count": sum(1 for pattern in blocking_patterns if pattern in code),
                "uses_async": "async def" in code,
                "has_cleanup": "teardown" in code
            }
        }

    async def _calculate_overall_score(self, assessments: dict[str, dict[str, Any]]) -> float:
        """Calculate overall quality score.

        Args:
            assessments: Individual assessment results

        Returns:
            Overall quality score (0.0 to 1.0)
        """
        weighted_score = 0.0
        
        for category, weight in QUALITY_WEIGHTS.items():
            if category in assessments:
                category_score = assessments[category].get("score", 0.0)
                weighted_score += category_score * weight
        
        return min(MAX_QUALITY_SCORE, weighted_score)

    async def _generate_improvement_recommendations(
        self,
        assessments: dict[str, dict[str, Any]],
        overall_score: float
    ) -> list[dict[str, Any]]:
        """Generate improvement recommendations.

        Args:
            assessments: Individual assessment results
            overall_score: Overall quality score

        Returns:
            List of improvement recommendations
        """
        recommendations = []
        
        # High priority recommendations (score < 0.7)
        if overall_score < MIN_ACCEPTABLE_QUALITY_SCORE:
            recommendations.append({
                "priority": "high",
                "category": "overall",
                "recommendation": "Plugin quality is below acceptable threshold",
                "action": "Review and address critical issues before deployment"
            })
        
        # Category-specific recommendations
        for category, assessment in assessments.items():
            if assessment.get("score", 1.0) < 0.8:
                for issue in assessment.get("issues", []):
                    recommendations.append({
                        "priority": "medium" if assessment.get("score", 1.0) > 0.6 else "high",
                        "category": category,
                        "recommendation": issue,
                        "action": self._suggest_action_for_issue(issue, category)
                    })
        
        # Sort by priority
        priority_order = {"high": 3, "medium": 2, "low": 1}
        recommendations.sort(key=lambda x: priority_order.get(x["priority"], 0), reverse=True)
        
        return recommendations

    def _determine_quality_grade(self, score: float) -> str:
        """Determine quality grade from score.

        Args:
            score: Quality score (0.0 to 1.0)

        Returns:
            Quality grade (A, B, C, D, F)
        """
        if score >= 0.9:
            return "A"
        elif score >= 0.8:
            return "B"
        elif score >= 0.7:
            return "C"
        elif score >= 0.6:
            return "D"
        else:
            return "F"

    def _suggest_action_for_issue(self, issue: str, category: str) -> str:
        """Suggest action for specific issue.

        Args:
            issue: Issue description
            category: Issue category

        Returns:
            Suggested action
        """
        action_map = {
            "Missing": "Add the missing component",
            "Insufficient": "Increase coverage or implementation",
            "Excessive": "Reduce usage or refactor",
            "Low": "Improve implementation quality"
        }
        
        for keyword, action in action_map.items():
            if keyword in issue:
                return action
        
        return "Review and improve implementation"
