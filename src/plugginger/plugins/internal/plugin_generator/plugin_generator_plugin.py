"""
Plugin Generator Internal Plugin.

Framework-internal plugin generation and scaffolding services.
"""

import logging
from typing import Any

from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.core.exceptions import PluggingerValidationError
from plugginger.plugins.internal.plugin_generator.services.generation_service import (
    PluginGenerationService,
)

logger = logging.getLogger(__name__)


@plugin(name="plugin_generator", version="1.0.0")
class PluginGeneratorPlugin(PluginBase):
    """Internal plugin for plugin generation and scaffolding services."""

    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize plugin generator plugin.

        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger

        # Initialize services
        self.generation_service = PluginGenerationService(self.app)

        # Configuration from manifest
        self.default_template = "basic"
        self.output_directory = "./plugins"
        self.enable_ai_generation = True
        self.enable_validation = True
        self.include_tests = True
        self.include_documentation = True
        self.code_style = "pep8"
        self.max_generation_time = 300.0

    async def setup(self, plugin_config: Any = None) -> None:
        """Setup the plugin generator plugin."""
        self.logger.info("Plugin Generator plugin initializing")

        # Load configuration from plugin_config if provided
        if plugin_config:
            if hasattr(plugin_config, 'default_template'):
                self.default_template = plugin_config.default_template
            if hasattr(plugin_config, 'output_directory'):
                self.output_directory = plugin_config.output_directory
            if hasattr(plugin_config, 'enable_ai_generation'):
                self.enable_ai_generation = plugin_config.enable_ai_generation
            if hasattr(plugin_config, 'enable_validation'):
                self.enable_validation = plugin_config.enable_validation
            if hasattr(plugin_config, 'include_tests'):
                self.include_tests = plugin_config.include_tests
            if hasattr(plugin_config, 'include_documentation'):
                self.include_documentation = plugin_config.include_documentation
            if hasattr(plugin_config, 'code_style'):
                self.code_style = plugin_config.code_style
            if hasattr(plugin_config, 'max_generation_time'):
                self.max_generation_time = plugin_config.max_generation_time

        self.logger.info(f"Plugin Generator configured: template={self.default_template}, "
                        f"ai_generation={self.enable_ai_generation}")

    async def teardown(self) -> None:
        """Cleanup the plugin generator plugin."""
        self.logger.info("Plugin Generator plugin shutting down")

    @service(name="generate_plugin")
    async def generate_plugin(
        self,
        plugin_spec: dict[str, Any],
        output_directory: str | None = None,
        template: str | None = None,
        enable_ai_generation: bool | None = None,
        include_tests: bool | None = None,
        include_documentation: bool | None = None
    ) -> dict[str, Any]:
        """Generate complete plugin from specification.

        Args:
            plugin_spec: Plugin specification
            output_directory: Output directory for generated plugin
            template: Template to use for generation
            enable_ai_generation: Enable AI-powered code generation
            include_tests: Generate test files
            include_documentation: Generate documentation

        Returns:
            Plugin generation result

        Example:
            result = await app.call_service("plugin_generator.generate_plugin", {
                "plugin_spec": {
                    "name": "my_plugin",
                    "version": "1.0.0",
                    "description": "My custom plugin",
                    "services": [{"name": "my_service", "description": "My service"}]
                },
                "template": "service"
            })
        """
        output_directory = output_directory or self.output_directory
        template = template or self.default_template
        enable_ai_generation = enable_ai_generation if enable_ai_generation is not None else self.enable_ai_generation
        include_tests = include_tests if include_tests is not None else self.include_tests
        include_documentation = include_documentation if include_documentation is not None else self.include_documentation

        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Generating plugin: {plugin_name}")

        try:
            result = await self.generation_service.generate_plugin(
                plugin_spec=plugin_spec,
                output_directory=output_directory,
                template=template,
                enable_ai_generation=enable_ai_generation,
                include_tests=include_tests,
                include_documentation=include_documentation
            )

            self.logger.info(f"Plugin generation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin generation error: {e}")
            raise PluggingerValidationError(f"Plugin generation failed: {e}") from e

    @service(name="scaffold_plugin_structure")
    async def scaffold_plugin_structure(
        self,
        plugin_name: str,
        output_directory: str | None = None,
        template: str | None = None
    ) -> dict[str, Any]:
        """Create plugin directory structure and files.

        Args:
            plugin_name: Name of the plugin
            output_directory: Output directory
            template: Template to use

        Returns:
            Scaffolding result

        Example:
            result = await app.call_service("plugin_generator.scaffold_plugin_structure", {
                "plugin_name": "my_plugin",
                "template": "full"
            })
        """
        output_directory = output_directory or self.output_directory
        template = template or self.default_template

        self.logger.info(f"Scaffolding plugin structure: {plugin_name}")

        try:
            result = await self.generation_service.scaffold_plugin_structure(
                plugin_name=plugin_name,
                output_directory=output_directory,
                template=template
            )

            self.logger.info(f"Plugin scaffolding {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin scaffolding error: {e}")
            raise PluggingerValidationError(f"Plugin scaffolding failed: {e}") from e

    @service(name="generate_plugin_code")
    async def generate_plugin_code(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str,
        enable_ai_generation: bool | None = None
    ) -> dict[str, Any]:
        """Generate plugin implementation code.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Path to plugin directory
            enable_ai_generation: Enable AI-powered code generation

        Returns:
            Code generation result

        Example:
            result = await app.call_service("plugin_generator.generate_plugin_code", {
                "plugin_spec": {"name": "my_plugin", "services": [...]},
                "plugin_path": "./plugins/my_plugin"
            })
        """
        enable_ai_generation = enable_ai_generation if enable_ai_generation is not None else self.enable_ai_generation

        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Generating code for plugin: {plugin_name}")

        try:
            if enable_ai_generation:
                result = await self.generation_service._generate_ai_plugin_code(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path,
                    template=self.default_template
                )
            else:
                result = await self.generation_service._generate_template_plugin_code(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path,
                    template=self.default_template
                )

            self.logger.info(f"Plugin code generation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin code generation error: {e}")
            raise PluggingerValidationError(f"Plugin code generation failed: {e}") from e

    @service(name="validate_plugin_structure")
    async def validate_plugin_structure(
        self,
        plugin_path: str
    ) -> dict[str, Any]:
        """Validate generated plugin structure and code.

        Args:
            plugin_path: Path to plugin directory

        Returns:
            Validation result

        Example:
            result = await app.call_service("plugin_generator.validate_plugin_structure", {
                "plugin_path": "./plugins/my_plugin"
            })
        """
        self.logger.info(f"Validating plugin structure: {plugin_path}")

        try:
            result = await self.generation_service.validate_plugin_structure(
                plugin_path=plugin_path
            )

            self.logger.info(f"Plugin validation {'passed' if result.get('valid') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin validation error: {e}")
            raise PluggingerValidationError(f"Plugin validation failed: {e}") from e

    @service(name="create_plugin_manifest")
    async def create_plugin_manifest(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str
    ) -> dict[str, Any]:
        """Generate plugin manifest.yaml file.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Path to plugin directory

        Returns:
            Manifest creation result

        Example:
            result = await app.call_service("plugin_generator.create_plugin_manifest", {
                "plugin_spec": {"name": "my_plugin", "version": "1.0.0", ...},
                "plugin_path": "./plugins/my_plugin"
            })
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Creating manifest for plugin: {plugin_name}")

        try:
            result = await self.generation_service.create_plugin_manifest(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path
            )

            self.logger.info(f"Plugin manifest creation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin manifest creation error: {e}")
            raise PluggingerValidationError(f"Plugin manifest creation failed: {e}") from e

    async def get_available_templates(self) -> dict[str, Any]:
        """Get list of available plugin templates.

        Returns:
            Available templates information
        """
        templates = {
            "basic": {
                "name": "basic",
                "description": "Basic plugin with minimal structure",
                "directories": ["services"],
                "includes_tests": False,
                "includes_docs": False
            },
            "service": {
                "name": "service",
                "description": "Service-oriented plugin with test structure",
                "directories": ["services", "tests"],
                "includes_tests": True,
                "includes_docs": False
            },
            "event": {
                "name": "event",
                "description": "Event-driven plugin with handlers",
                "directories": ["services", "handlers", "tests"],
                "includes_tests": True,
                "includes_docs": False
            },
            "full": {
                "name": "full",
                "description": "Complete plugin with all features",
                "directories": ["services", "tests", "docs", "examples"],
                "includes_tests": True,
                "includes_docs": True
            }
        }

        return {
            "templates": templates,
            "default_template": self.default_template,
            "total_templates": len(templates)
        }

    async def get_generation_status(self) -> dict[str, Any]:
        """Get current generation status and configuration.

        Returns:
            Generation status information
        """
        return {
            "plugin_name": "plugin_generator",
            "status": "active",
            "configuration": {
                "default_template": self.default_template,
                "output_directory": self.output_directory,
                "enable_ai_generation": self.enable_ai_generation,
                "enable_validation": self.enable_validation,
                "include_tests": self.include_tests,
                "include_documentation": self.include_documentation,
                "code_style": self.code_style,
                "max_generation_time": self.max_generation_time
            },
            "capabilities": [
                "plugin_generation",
                "structure_scaffolding",
                "code_generation",
                "manifest_creation",
                "validation",
                "ai_powered_generation",
                "template_based_generation"
            ],
            "dependencies": [
                "ai_orchestrator",
                "llm_provider",
                "json_validator",
                "wiring_analyzer"
            ]
        }
