"""
Plugin Generator Internal Plugin.

Framework-internal plugin generation and scaffolding services.
"""

import logging
from typing import Any

from plugginger.api.plugin import PluginBase, plugin
from plugginger.api.service import service
from plugginger.core.exceptions import PluggingerValidationError
from plugginger.plugins.internal.plugin_generator.services.generation_service import (
    PluginGenerationService,
)
from plugginger.plugins.internal.plugin_generator.services.prompt_processor import PromptProcessor
from plugginger.plugins.internal.plugin_generator.services.plugin_spec_generator import PluginSpecGenerator
from plugginger.plugins.internal.plugin_generator.services.code_template_engine import CodeTemplateEngine
from plugginger.plugins.internal.plugin_generator.services.wiring_integration import WiringIntegration
from plugginger.plugins.internal.plugin_generator.services.quality_scoring import QualityScoring

logger = logging.getLogger(__name__)


@plugin(name="plugin_generator", version="1.0.0")
class PluginGeneratorPlugin(PluginBase):
    """Internal plugin for plugin generation and scaffolding services."""

    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize plugin generator plugin.

        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger

        # Initialize services
        self.generation_service = PluginGenerationService(self.app)
        self.prompt_processor = PromptProcessor(self.app)
        self.spec_generator = PluginSpecGenerator(self.app)
        self.code_engine = CodeTemplateEngine(self.app)
        self.wiring_integration = WiringIntegration(self.app)
        self.quality_scoring = QualityScoring(self.app)

        # Configuration from manifest
        self.default_template = "basic"
        self.output_directory = "./plugins"
        self.enable_ai_generation = True
        self.enable_validation = True
        self.include_tests = True
        self.include_documentation = True
        self.code_style = "pep8"
        self.max_generation_time = 300.0

    async def setup(self, plugin_config: Any = None) -> None:
        """Setup the plugin generator plugin."""
        self.logger.info("Plugin Generator plugin initializing")

        # Load configuration from plugin_config if provided
        if plugin_config:
            if hasattr(plugin_config, 'default_template'):
                self.default_template = plugin_config.default_template
            if hasattr(plugin_config, 'output_directory'):
                self.output_directory = plugin_config.output_directory
            if hasattr(plugin_config, 'enable_ai_generation'):
                self.enable_ai_generation = plugin_config.enable_ai_generation
            if hasattr(plugin_config, 'enable_validation'):
                self.enable_validation = plugin_config.enable_validation
            if hasattr(plugin_config, 'include_tests'):
                self.include_tests = plugin_config.include_tests
            if hasattr(plugin_config, 'include_documentation'):
                self.include_documentation = plugin_config.include_documentation
            if hasattr(plugin_config, 'code_style'):
                self.code_style = plugin_config.code_style
            if hasattr(plugin_config, 'max_generation_time'):
                self.max_generation_time = plugin_config.max_generation_time

        self.logger.info(f"Plugin Generator configured: template={self.default_template}, "
                        f"ai_generation={self.enable_ai_generation}")

    async def teardown(self) -> None:
        """Cleanup the plugin generator plugin."""
        self.logger.info("Plugin Generator plugin shutting down")

    @service(name="generate_plugin")
    async def generate_plugin(
        self,
        plugin_spec: dict[str, Any],
        output_directory: str | None = None,
        template: str | None = None,
        enable_ai_generation: bool | None = None,
        include_tests: bool | None = None,
        include_documentation: bool | None = None
    ) -> dict[str, Any]:
        """Generate complete plugin from specification.

        Args:
            plugin_spec: Plugin specification
            output_directory: Output directory for generated plugin
            template: Template to use for generation
            enable_ai_generation: Enable AI-powered code generation
            include_tests: Generate test files
            include_documentation: Generate documentation

        Returns:
            Plugin generation result

        Example:
            result = await app.call_service("plugin_generator.generate_plugin", {
                "plugin_spec": {
                    "name": "my_plugin",
                    "version": "1.0.0",
                    "description": "My custom plugin",
                    "services": [{"name": "my_service", "description": "My service"}]
                },
                "template": "service"
            })
        """
        output_directory = output_directory or self.output_directory
        template = template or self.default_template
        enable_ai_generation = enable_ai_generation if enable_ai_generation is not None else self.enable_ai_generation
        include_tests = include_tests if include_tests is not None else self.include_tests
        include_documentation = include_documentation if include_documentation is not None else self.include_documentation

        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Generating plugin: {plugin_name}")

        try:
            result = await self.generation_service.generate_plugin(
                plugin_spec=plugin_spec,
                output_directory=output_directory,
                template=template,
                enable_ai_generation=enable_ai_generation,
                include_tests=include_tests,
                include_documentation=include_documentation
            )

            self.logger.info(f"Plugin generation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin generation error: {e}")
            raise PluggingerValidationError(f"Plugin generation failed: {e}") from e

    @service(name="scaffold_plugin_structure")
    async def scaffold_plugin_structure(
        self,
        plugin_name: str,
        output_directory: str | None = None,
        template: str | None = None
    ) -> dict[str, Any]:
        """Create plugin directory structure and files.

        Args:
            plugin_name: Name of the plugin
            output_directory: Output directory
            template: Template to use

        Returns:
            Scaffolding result

        Example:
            result = await app.call_service("plugin_generator.scaffold_plugin_structure", {
                "plugin_name": "my_plugin",
                "template": "full"
            })
        """
        output_directory = output_directory or self.output_directory
        template = template or self.default_template

        self.logger.info(f"Scaffolding plugin structure: {plugin_name}")

        try:
            result = await self.generation_service.scaffold_plugin_structure(
                plugin_name=plugin_name,
                output_directory=output_directory,
                template=template
            )

            self.logger.info(f"Plugin scaffolding {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin scaffolding error: {e}")
            raise PluggingerValidationError(f"Plugin scaffolding failed: {e}") from e

    @service(name="generate_plugin_code")
    async def generate_plugin_code(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str,
        enable_ai_generation: bool | None = None
    ) -> dict[str, Any]:
        """Generate plugin implementation code.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Path to plugin directory
            enable_ai_generation: Enable AI-powered code generation

        Returns:
            Code generation result

        Example:
            result = await app.call_service("plugin_generator.generate_plugin_code", {
                "plugin_spec": {"name": "my_plugin", "services": [...]},
                "plugin_path": "./plugins/my_plugin"
            })
        """
        enable_ai_generation = enable_ai_generation if enable_ai_generation is not None else self.enable_ai_generation

        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Generating code for plugin: {plugin_name}")

        try:
            if enable_ai_generation:
                result = await self.generation_service._generate_ai_plugin_code(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path,
                    template=self.default_template
                )
            else:
                result = await self.generation_service._generate_template_plugin_code(
                    plugin_spec=plugin_spec,
                    plugin_path=plugin_path,
                    template=self.default_template
                )

            self.logger.info(f"Plugin code generation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin code generation error: {e}")
            raise PluggingerValidationError(f"Plugin code generation failed: {e}") from e

    @service(name="process_user_prompt")
    async def process_user_prompt(
        self,
        user_prompt: str,
        app_context: dict[str, Any] | None = None,
        generation_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Process user prompt for intelligent plugin generation.

        Args:
            user_prompt: Raw user prompt describing desired plugin
            app_context: Application context for intelligent integration
            generation_options: Additional generation options

        Returns:
            Processed prompt with enhanced context and specifications

        Example:
            result = await app.call_service("plugin_generator.process_user_prompt", {
                "user_prompt": "Create an email service with authentication",
                "app_context": {"services": ["auth", "logger"]},
                "generation_options": {"include_tests": True}
            })
        """
        self.logger.info(f"Processing user prompt: {user_prompt[:50]}...")

        try:
            result = await self.prompt_processor.process_user_prompt(
                user_prompt=user_prompt,
                app_context=app_context,
                generation_options=generation_options
            )

            self.logger.info(f"Prompt processing {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Prompt processing error: {e}")
            raise PluggingerValidationError(f"Prompt processing failed: {e}") from e

    @service(name="generate_plugin_spec")
    async def generate_plugin_spec(
        self,
        llm_output: str,
        prompt_context: dict[str, Any],
        validation_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Generate structured plugin specification from LLM output.

        Args:
            llm_output: Raw LLM output containing plugin specification
            prompt_context: Original prompt context for validation
            validation_options: Additional validation options

        Returns:
            Structured and validated plugin specification

        Example:
            result = await app.call_service("plugin_generator.generate_plugin_spec", {
                "llm_output": '{"name": "email_service", "description": "Email handling"}',
                "prompt_context": {"plugin_type": "service"},
                "validation_options": {"strict_validation": True}
            })
        """
        self.logger.info("Generating plugin specification from LLM output")

        try:
            result = await self.spec_generator.generate_plugin_spec(
                llm_output=llm_output,
                prompt_context=prompt_context,
                validation_options=validation_options
            )

            self.logger.info(f"Plugin spec generation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin spec generation error: {e}")
            raise PluggingerValidationError(f"Plugin spec generation failed: {e}") from e

    @service(name="generate_plugin_code_advanced")
    async def generate_plugin_code_advanced(
        self,
        plugin_spec: dict[str, Any],
        generation_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Generate complete plugin code using advanced template engine.

        Args:
            plugin_spec: Plugin specification
            generation_options: Code generation options

        Returns:
            Generated plugin code with metadata

        Example:
            result = await app.call_service("plugin_generator.generate_plugin_code_advanced", {
                "plugin_spec": {"name": "email_service", "services": [...]},
                "generation_options": {"include_docstrings": True}
            })
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Generating advanced code for plugin: {plugin_name}")

        try:
            result = await self.code_engine.generate_plugin_code(
                plugin_spec=plugin_spec,
                generation_options=generation_options
            )

            self.logger.info(f"Advanced code generation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Advanced code generation error: {e}")
            raise PluggingerValidationError(f"Advanced code generation failed: {e}") from e

    @service(name="analyze_integration_opportunities")
    async def analyze_integration_opportunities(
        self,
        plugin_spec: dict[str, Any],
        app_context: dict[str, Any] | None = None,
        analysis_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Analyze integration opportunities for plugin.

        Args:
            plugin_spec: Plugin specification to analyze
            app_context: Current application context
            analysis_options: Analysis configuration options

        Returns:
            Integration analysis with suggestions and recommendations

        Example:
            result = await app.call_service("plugin_generator.analyze_integration_opportunities", {
                "plugin_spec": {"name": "email_service", "services": [...]},
                "app_context": {"available_services": ["logger", "config"]},
                "analysis_options": {"confidence_threshold": 0.8}
            })
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Analyzing integration opportunities for: {plugin_name}")

        try:
            result = await self.wiring_integration.analyze_integration_opportunities(
                plugin_spec=plugin_spec,
                app_context=app_context,
                analysis_options=analysis_options
            )

            self.logger.info(f"Integration analysis {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Integration analysis error: {e}")
            raise PluggingerValidationError(f"Integration analysis failed: {e}") from e

    @service(name="assess_plugin_quality")
    async def assess_plugin_quality(
        self,
        plugin_spec: dict[str, Any],
        generated_code: str,
        test_code: str,
        manifest_code: str,
        assessment_options: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """Assess overall quality of generated plugin.

        Args:
            plugin_spec: Plugin specification
            generated_code: Generated plugin code
            test_code: Generated test code
            manifest_code: Generated manifest code
            assessment_options: Quality assessment options

        Returns:
            Comprehensive quality assessment with scores and recommendations

        Example:
            result = await app.call_service("plugin_generator.assess_plugin_quality", {
                "plugin_spec": {"name": "email_service", "services": [...]},
                "generated_code": "class EmailPlugin...",
                "test_code": "class TestEmailPlugin...",
                "manifest_code": "name: email_service...",
                "assessment_options": {"strict_mode": True}
            })
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Assessing quality for plugin: {plugin_name}")

        try:
            result = await self.quality_scoring.assess_plugin_quality(
                plugin_spec=plugin_spec,
                generated_code=generated_code,
                test_code=test_code,
                manifest_code=manifest_code,
                assessment_options=assessment_options
            )

            quality_grade = result.get("quality_grade", "F")
            self.logger.info(f"Quality assessment complete: Grade {quality_grade}")
            return result

        except Exception as e:
            self.logger.error(f"Quality assessment error: {e}")
            raise PluggingerValidationError(f"Quality assessment failed: {e}") from e

    @service(name="validate_plugin_structure")
    async def validate_plugin_structure(
        self,
        plugin_path: str
    ) -> dict[str, Any]:
        """Validate generated plugin structure and code.

        Args:
            plugin_path: Path to plugin directory

        Returns:
            Validation result

        Example:
            result = await app.call_service("plugin_generator.validate_plugin_structure", {
                "plugin_path": "./plugins/my_plugin"
            })
        """
        self.logger.info(f"Validating plugin structure: {plugin_path}")

        try:
            result = await self.generation_service.validate_plugin_structure(
                plugin_path=plugin_path
            )

            self.logger.info(f"Plugin validation {'passed' if result.get('valid') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin validation error: {e}")
            raise PluggingerValidationError(f"Plugin validation failed: {e}") from e

    @service(name="create_plugin_manifest")
    async def create_plugin_manifest(
        self,
        plugin_spec: dict[str, Any],
        plugin_path: str
    ) -> dict[str, Any]:
        """Generate plugin manifest.yaml file.

        Args:
            plugin_spec: Plugin specification
            plugin_path: Path to plugin directory

        Returns:
            Manifest creation result

        Example:
            result = await app.call_service("plugin_generator.create_plugin_manifest", {
                "plugin_spec": {"name": "my_plugin", "version": "1.0.0", ...},
                "plugin_path": "./plugins/my_plugin"
            })
        """
        plugin_name = plugin_spec.get("name", "unknown_plugin")
        self.logger.info(f"Creating manifest for plugin: {plugin_name}")

        try:
            result = await self.generation_service.create_plugin_manifest(
                plugin_spec=plugin_spec,
                plugin_path=plugin_path
            )

            self.logger.info(f"Plugin manifest creation {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin manifest creation error: {e}")
            raise PluggingerValidationError(f"Plugin manifest creation failed: {e}") from e

    async def get_available_templates(self) -> dict[str, Any]:
        """Get list of available plugin templates.

        Returns:
            Available templates information
        """
        templates = {
            "basic": {
                "name": "basic",
                "description": "Basic plugin with minimal structure",
                "directories": ["services"],
                "includes_tests": False,
                "includes_docs": False
            },
            "service": {
                "name": "service",
                "description": "Service-oriented plugin with test structure",
                "directories": ["services", "tests"],
                "includes_tests": True,
                "includes_docs": False
            },
            "event": {
                "name": "event",
                "description": "Event-driven plugin with handlers",
                "directories": ["services", "handlers", "tests"],
                "includes_tests": True,
                "includes_docs": False
            },
            "full": {
                "name": "full",
                "description": "Complete plugin with all features",
                "directories": ["services", "tests", "docs", "examples"],
                "includes_tests": True,
                "includes_docs": True
            }
        }

        return {
            "templates": templates,
            "default_template": self.default_template,
            "total_templates": len(templates)
        }

    async def get_generation_status(self) -> dict[str, Any]:
        """Get current generation status and configuration.

        Returns:
            Generation status information
        """
        return {
            "plugin_name": "plugin_generator",
            "status": "active",
            "configuration": {
                "default_template": self.default_template,
                "output_directory": self.output_directory,
                "enable_ai_generation": self.enable_ai_generation,
                "enable_validation": self.enable_validation,
                "include_tests": self.include_tests,
                "include_documentation": self.include_documentation,
                "code_style": self.code_style,
                "max_generation_time": self.max_generation_time
            },
            "capabilities": [
                "plugin_generation",
                "structure_scaffolding",
                "code_generation",
                "manifest_creation",
                "validation",
                "ai_powered_generation",
                "template_based_generation"
            ],
            "dependencies": [
                "ai_orchestrator",
                "llm_provider",
                "json_validator",
                "wiring_analyzer"
            ]
        }
