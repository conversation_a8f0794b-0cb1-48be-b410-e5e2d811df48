"""
AI Orchestrator Internal Plugin.

Framework-internal AI coordination and orchestration services.
"""

import logging
from typing import Any

from plugginger.api.plugin import PluginBase
from plugginger.api.service import service
from plugginger.core.exceptions import PluggingerValidationError
from plugginger.plugins.internal.ai_orchestrator.services.orchestration_service import (
    AIOrchestrationService,
)
from plugginger.plugins.internal.ai_orchestrator.services.validation_service import (
    AIValidationService,
)

logger = logging.getLogger(__name__)


class AIOrchestratorPlugin(PluginBase):
    """Internal plugin for AI coordination and orchestration services."""

    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize AI orchestrator plugin.

        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger

        # Initialize services
        self.orchestration_service = AIOrchestrationService(self.app)
        self.validation_service = AIValidationService(self.app)

        # Configuration from manifest
        self.max_retry_attempts = 3
        self.default_confidence_threshold = 0.7
        self.enable_parallel_processing = True
        self.workflow_timeout_seconds = 180.0
        self.enable_caching = True
        self.cache_ttl_seconds = 3600

    async def setup(self, plugin_config: Any = None) -> None:
        """Setup the AI orchestrator plugin."""
        self.logger.info("AI Orchestrator plugin initializing")

        # Load configuration from plugin_config if provided
        if plugin_config:
            if hasattr(plugin_config, 'max_retry_attempts'):
                self.max_retry_attempts = plugin_config.max_retry_attempts
            if hasattr(plugin_config, 'default_confidence_threshold'):
                self.default_confidence_threshold = plugin_config.default_confidence_threshold
            if hasattr(plugin_config, 'enable_parallel_processing'):
                self.enable_parallel_processing = plugin_config.enable_parallel_processing
            if hasattr(plugin_config, 'workflow_timeout_seconds'):
                self.workflow_timeout_seconds = plugin_config.workflow_timeout_seconds
            if hasattr(plugin_config, 'enable_caching'):
                self.enable_caching = plugin_config.enable_caching
            if hasattr(plugin_config, 'cache_ttl_seconds'):
                self.cache_ttl_seconds = plugin_config.cache_ttl_seconds

        self.logger.info(f"AI Orchestrator configured: retries={self.max_retry_attempts}, "
                        f"threshold={self.default_confidence_threshold}")

    async def teardown(self) -> None:
        """Cleanup the AI orchestrator plugin."""
        self.logger.info("AI Orchestrator plugin shutting down")

    @service(name="orchestrate_plugin_generation")
    async def orchestrate_plugin_generation(
        self,
        plugin_request: dict[str, Any],
        app_context: dict[str, Any] | None = None,
        max_retry_attempts: int | None = None,
        confidence_threshold: float | None = None
    ) -> dict[str, Any]:
        """Coordinate AI-powered plugin generation workflow.

        Args:
            plugin_request: Plugin generation request
            app_context: Optional app context for analysis
            max_retry_attempts: Maximum retry attempts
            confidence_threshold: Confidence threshold for validation

        Returns:
            Complete plugin generation result

        Example:
            result = await app.call_service("ai_orchestrator.orchestrate_plugin_generation", {
                "plugin_request": {"name": "email_plugin", "description": "Send emails"},
                "app_context": {"app_path": "myapp:create_app"}
            })
        """
        max_retry_attempts = max_retry_attempts or self.max_retry_attempts
        confidence_threshold = confidence_threshold or self.default_confidence_threshold

        self.logger.info(f"Orchestrating plugin generation: {plugin_request.get('name', 'unknown')}")

        try:
            # Emit start event
            await self.app.emit_event("ai.orchestration.started", {
                "type": "plugin_generation",
                "plugin_name": plugin_request.get("name", "unknown")
            })

            result = await self.orchestration_service.orchestrate_plugin_generation(
                plugin_request=plugin_request,
                app_context=app_context,
                max_retry_attempts=max_retry_attempts,
                confidence_threshold=confidence_threshold
            )

            self.logger.info(f"Plugin generation orchestration {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin generation orchestration error: {e}")
            raise PluggingerValidationError(f"Plugin generation orchestration failed: {e}") from e

    @service(name="analyze_and_suggest")
    async def analyze_and_suggest(
        self,
        app_path: str,
        analysis_depth: str = "detailed",
        confidence_threshold: float | None = None
    ) -> dict[str, Any]:
        """Analyze app context and suggest plugin improvements.

        Args:
            app_path: Path to app for analysis
            analysis_depth: Depth of analysis
            confidence_threshold: Confidence threshold for suggestions

        Returns:
            Analysis results with AI-powered suggestions

        Example:
            result = await app.call_service("ai_orchestrator.analyze_and_suggest", {
                "app_path": "myapp.main:create_app",
                "analysis_depth": "comprehensive"
            })
        """
        confidence_threshold = confidence_threshold or self.default_confidence_threshold

        self.logger.info(f"Analyzing app and generating suggestions: {app_path}")

        try:
            result = await self.orchestration_service.analyze_and_suggest(
                app_path=app_path,
                analysis_depth=analysis_depth,
                confidence_threshold=confidence_threshold
            )

            self.logger.info(f"App analysis and suggestion {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"App analysis and suggestion error: {e}")
            raise PluggingerValidationError(f"App analysis and suggestion failed: {e}") from e

    @service(name="validate_ai_output")
    async def validate_ai_output(
        self,
        content: str,
        content_type: str,
        validation_rules: dict[str, Any] | None = None,
        confidence_threshold: float | None = None
    ) -> dict[str, Any]:
        """Validate AI-generated content using multiple validators.

        Args:
            content: AI-generated content to validate
            content_type: Type of content (json, yaml, python, text)
            validation_rules: Optional validation rules
            confidence_threshold: Confidence threshold for validation

        Returns:
            Comprehensive validation result

        Example:
            result = await app.call_service("ai_orchestrator.validate_ai_output", {
                "content": '{"name": "test", "version": "1.0.0"}',
                "content_type": "json",
                "validation_rules": {"schema": {"type": "object"}}
            })
        """
        confidence_threshold = confidence_threshold or self.default_confidence_threshold

        self.logger.info(f"Validating AI output: {content_type}")

        try:
            result = await self.validation_service.validate_ai_output(
                content=content,
                content_type=content_type,
                validation_rules=validation_rules,
                confidence_threshold=confidence_threshold
            )

            self.logger.info(f"AI output validation {'passed' if result.get('valid') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"AI output validation error: {e}")
            raise PluggingerValidationError(f"AI output validation failed: {e}") from e

    @service(name="coordinate_services")
    async def coordinate_services(
        self,
        service_calls: list[dict[str, Any]],
        execution_mode: str = "parallel",
        timeout_seconds: float | None = None
    ) -> dict[str, Any]:
        """Coordinate multiple AI service calls.

        Args:
            service_calls: List of service call specifications
            execution_mode: Execution mode (parallel, sequential)
            timeout_seconds: Timeout for coordination

        Returns:
            Coordinated service results

        Example:
            result = await app.call_service("ai_orchestrator.coordinate_services", {
                "service_calls": [
                    {"service": "llm_provider.generate_text", "parameters": {"prompt": "Hello"}},
                    {"service": "json_validator.validate_json", "parameters": {"data": "{}"}}
                ],
                "execution_mode": "parallel"
            })
        """
        timeout_seconds = timeout_seconds or self.workflow_timeout_seconds

        self.logger.info(f"Coordinating {len(service_calls)} AI services in {execution_mode} mode")

        try:
            result = await self.orchestration_service.coordinate_services(
                service_calls=service_calls,
                execution_mode=execution_mode,
                timeout_seconds=timeout_seconds
            )

            self.logger.info(f"Service coordination {'successful' if result.get('success') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Service coordination error: {e}")
            raise PluggingerValidationError(f"Service coordination failed: {e}") from e

    async def validate_plugin_specification(
        self,
        plugin_spec: dict[str, Any],
        strict_mode: bool = True
    ) -> dict[str, Any]:
        """Validate AI-generated plugin specification.

        Args:
            plugin_spec: Plugin specification to validate
            strict_mode: Enable strict validation mode

        Returns:
            Plugin specification validation result
        """
        self.logger.info(f"Validating plugin specification: {plugin_spec.get('name', 'unknown')}")

        try:
            result = await self.validation_service.validate_plugin_specification(
                plugin_spec=plugin_spec,
                strict_mode=strict_mode
            )

            self.logger.info(f"Plugin specification validation {'passed' if result.get('valid') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Plugin specification validation error: {e}")
            return {
                "valid": False,
                "plugin_name": plugin_spec.get("name", "unknown"),
                "error": str(e)
            }

    async def validate_generated_code(
        self,
        code: str,
        language: str = "python",
        check_syntax: bool = True,
        check_style: bool = True
    ) -> dict[str, Any]:
        """Validate AI-generated code.

        Args:
            code: Generated code to validate
            language: Programming language
            check_syntax: Enable syntax checking
            check_style: Enable style checking

        Returns:
            Code validation result
        """
        self.logger.info(f"Validating generated {language} code")

        try:
            result = await self.validation_service.validate_generated_code(
                code=code,
                language=language,
                check_syntax=check_syntax,
                check_style=check_style
            )

            self.logger.info(f"Generated code validation {'passed' if result.get('valid') else 'failed'}")
            return result

        except Exception as e:
            self.logger.error(f"Generated code validation error: {e}")
            return {
                "valid": False,
                "language": language,
                "error": str(e)
            }

    async def get_orchestration_status(self) -> dict[str, Any]:
        """Get current orchestration status and statistics.

        Returns:
            Orchestration status information
        """
        return {
            "plugin_name": "ai_orchestrator",
            "status": "active",
            "configuration": {
                "max_retry_attempts": self.max_retry_attempts,
                "default_confidence_threshold": self.default_confidence_threshold,
                "enable_parallel_processing": self.enable_parallel_processing,
                "workflow_timeout_seconds": self.workflow_timeout_seconds,
                "enable_caching": self.enable_caching,
                "cache_ttl_seconds": self.cache_ttl_seconds
            },
            "capabilities": [
                "plugin_generation_orchestration",
                "app_analysis_and_suggestions",
                "ai_output_validation",
                "service_coordination",
                "parallel_processing",
                "workflow_management"
            ],
            "dependencies": [
                "llm_provider",
                "json_validator",
                "wiring_analyzer"
            ]
        }
