# src/plugginger/config/models.py

"""
Pydantic models for Plugginger configuration structures.

This module defines the core configuration models used throughout
the Plugginger framework for type-safe configuration management.
"""

from __future__ import annotations

from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field, field_validator

from plugginger.core.config import Event<PERSON><PERSON><PERSON>FaultPolicy, LogLevel
from plugginger.core.types import PluginConfigData


class ExecutorConfig(BaseModel):
    """Configuration for a named ThreadPoolExecutor."""

    name: str = Field(
        description="Unique name for this executor configuration"
    )
    max_workers: int = Field(
        default=4,
        ge=1,
        description="Maximum number of worker threads in the pool"
    )
    thread_name_prefix: str = Field(
        default="PluggingerWorker",
        description="Prefix for worker thread names"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class TypedPluginConfigs(BaseModel):
    """
    Type-safe container for plugin configurations.

    This model provides enhanced type safety for plugin configurations
    by validating that each configuration matches its plugin's declared schema.
    """

    configs: dict[str, PluginConfigData] = Field(
        default_factory=dict,
        description="Plugin configurations keyed by plugin instance_id"
    )

    @field_validator('configs')
    @classmethod
    def validate_configs_structure(cls, v: dict[str, PluginConfigData]) -> dict[str, PluginConfigData]:
        """Validate that all configurations are dictionaries."""
        for plugin_id, config in v.items():
            if not isinstance(config, dict):
                raise ValueError(
                    f"Configuration for plugin '{plugin_id}' must be a dictionary, "
                    f"got {type(config).__name__}"
                )
        return v

    def get(self, plugin_id: str, default: PluginConfigData | None = None) -> PluginConfigData:
        """
        Get configuration for a specific plugin (dict-like interface).

        Args:
            plugin_id: Plugin instance ID
            default: Default value if plugin not found

        Returns:
            Plugin configuration dictionary
        """
        if default is None:
            default = {}
        return self.configs.get(plugin_id, default)

    def get_config(self, plugin_id: str) -> PluginConfigData:
        """
        Get configuration for a specific plugin.

        Args:
            plugin_id: Plugin instance ID

        Returns:
            Plugin configuration dictionary
        """
        return self.configs.get(plugin_id, {})

    def set_config(self, plugin_id: str, config: PluginConfigData) -> None:
        """
        Set configuration for a specific plugin.

        Args:
            plugin_id: Plugin instance ID
            config: Plugin configuration dictionary
        """
        if not isinstance(config, dict):
            raise ValueError(
                f"Configuration for plugin '{plugin_id}' must be a dictionary, "
                f"got {type(config).__name__}"
            )
        self.configs[plugin_id] = config

    def has_config(self, plugin_id: str) -> bool:
        """
        Check if configuration exists for a plugin.

        Args:
            plugin_id: Plugin instance ID

        Returns:
            True if configuration exists, False otherwise
        """
        return plugin_id in self.configs

    def remove_config(self, plugin_id: str) -> bool:
        """
        Remove configuration for a plugin.

        Args:
            plugin_id: Plugin instance ID

        Returns:
            True if configuration was removed, False if it didn't exist
        """
        if plugin_id in self.configs:
            del self.configs[plugin_id]
            return True
        return False

    def get_all_plugin_ids(self) -> list[str]:
        """Get list of all plugin IDs with configurations."""
        return list(self.configs.keys())

    def __len__(self) -> int:
        """Get number of configured plugins."""
        return len(self.configs)

    def __contains__(self, plugin_id: str) -> bool:
        """Check if plugin has configuration."""
        return plugin_id in self.configs

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class GlobalAppConfig(BaseModel):
    """
    Global configuration for a PluggingerAppInstance.

    This model defines the main application configuration that is
    validated by PluggingerAppBuilder.build().
    """

    app_name: str = Field(
        default="PluggingerApp",
        min_length=1,
        description="The name of the application instance"
    )

    log_level: LogLevel = Field(
        default=LogLevel.INFO,
        description="Logging level for the application"
    )

    event_listener_fault_policy: EventListenerFaultPolicy = Field(
        default=EventListenerFaultPolicy.FAIL_FAST,
        description="Strategy for handling errors in event listeners"
    )

    default_event_listener_timeout_seconds: float = Field(
        default=5.0,
        gt=0.0,
        description="Default timeout for event listeners in seconds"
    )

    executors: list[ExecutorConfig] = Field(
        default_factory=lambda: [ExecutorConfig(name="default")],
        description="Thread pool executor configurations"
    )

    plugin_configs: TypedPluginConfigs = Field(
        default_factory=TypedPluginConfigs,
        description="Type-safe plugin-specific configurations"
    )

    max_fractal_depth: int = Field(
        default=10,
        ge=1,
        description="Maximum nesting depth for fractal AppPlugin composition"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


# --- Lockfile Models ---

class PluginLockInfo(BaseModel):
    """Information about a locked plugin in the lockfile."""

    name: str = Field(description="Plugin name")
    version: str = Field(description="Plugin version")
    instance_id: str = Field(description="Unique instance identifier")
    source_location: str | None = Field(
        default=None,
        description="Source location (file path, git repo, etc.)"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class PythonPackageLockInfo(BaseModel):
    """Information about a locked Python package dependency."""

    name: str = Field(description="Package name")
    version: str = Field(description="Exact package version")
    source: Literal["pypi", "git", "local", "other"] = Field(
        default="pypi",
        description="Source of the package"
    )
    integrity_hash: str | None = Field(
        default=None,
        description="Integrity hash for verification"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class PluggingerLockfileMetadata(BaseModel):
    """Metadata for the Plugginger lockfile."""

    lockfile_version: str = Field(
        default="1.0.0",
        description="Version of the lockfile format"
    )
    created_at: datetime = Field(
        default_factory=datetime.now,
        description="When the lockfile was created"
    )
    created_by: str = Field(
        default="plugginger-cli",
        description="Tool that created the lockfile"
    )
    python_version: str | None = Field(
        default=None,
        description="Python version used during freeze"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }


class PluggingerLockfile(BaseModel):
    """
    Complete structure of a plugginger.lock.json file.

    This model defines the schema for lockfiles generated by
    the 'plugginger core freeze' command.
    """

    metadata: PluggingerLockfileMetadata = Field(
        default_factory=PluggingerLockfileMetadata,
        description="Lockfile metadata"
    )

    app_name: str = Field(
        description="Name of the application this lockfile pertains to"
    )

    plugins: list[PluginLockInfo] = Field(
        default_factory=list,
        description="List of all plugins in the locked application"
    )

    resolved_python_packages: list[PythonPackageLockInfo] = Field(
        default_factory=list,
        description="Resolved Python package dependencies"
    )

    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }
