# src/plugginger/core/types.py

"""
Core type definitions with zero external dependencies.

This module contains fundamental type definitions that are used throughout
the Plugginger framework. These types have no dependencies on other parts
of the system and can be imported safely.
"""

from __future__ import annotations

from collections.abc import Awaitable, Callable, Sequence
from dataclasses import dataclass, field
from typing import Any, ParamSpec, TypeVar

# --- Generic Type Variables ---

# Generic type variable for return types
R = TypeVar("R")

# Parameter specification for generic callables
P = ParamSpec("P")

# --- Core Type Aliases ---

# Type alias for logger functions
LoggerCallable = Callable[[str], None]

# Type alias for service methods (async callables)
ServiceMethodType = Callable[P, Awaitable[R]]

# Type alias for event handlers (async callables that return None)
EventHandlerType = Callable[..., Awaitable[None]]

# Type alias for event pattern inputs
EventPatternInput = str | Sequence[str]

# --- Event Bridge Types for AppPluginBase ---

# Type for event data transformation functions
EventDataTransformer = Callable[[dict[str, Any], str], dict[str, Any]]

# Type for event bridge configuration entries
EventBridgeConfigEntryType = dict[str, Any]

# Type for list of event bridge configurations
EventBridgeListType = list[EventBridgeConfigEntryType]



# --- Configuration Types ---



# --- Instance ID Types ---

# Type for plugin instance IDs
PluginInstanceId = str

# Type for service names (fully qualified)
ServiceName = str

# Type for event type strings
EventType = str

# --- Event System Types ---

# Type for event data payloads (commonly used throughout event system)
EventData = dict[str, Any]

# Type for event pattern strings (used for pattern matching)
EventPattern = str

# Type for event pattern lists (used in listener registration)
EventPatternList = list[EventPattern]

# Type for event listener method names
EventListenerMethodName = str



# --- Metadata Types ---

# Type for plugin metadata dictionaries
PluginMetadata = dict[str, Any]

# Type for service metadata dictionaries
ServiceMetadata = dict[str, Any]

# Type for event listener metadata dictionaries
EventListenerMetadata = dict[str, Any]

# Type for dependency metadata
DependencyMetadata = dict[str, Any]

# Type for configuration metadata
ConfigMetadata = dict[str, Any]

# --- Utility Types ---

# Type for optional values
Optional = TypeVar("Optional")

# Type for JSON-serializable values
JsonValue = str | int | float | bool | None | dict[str, Any] | list[Any]

# Type for configuration values
ConfigValue = JsonValue

# --- Collection Types ---

# Type for string lists (commonly used for patterns, names, etc.)
StringList = list[str]

# Type for string to string mappings (commonly used for simple configs)
StringDict = dict[str, str]


# --- Validation Types ---

@dataclass
class ValidationResult:
    """Generic validation result with errors, warnings, and metadata."""

    valid: bool
    errors: list[str] = field(default_factory=list)
    warnings: list[str] = field(default_factory=list)
    metadata: dict[str, Any] = field(default_factory=dict)

# Type for string to any mappings (most common dict type in the codebase)
AnyDict = dict[str, Any]

# Type for lists of any values
AnyList = list[Any]

# Type for tuples of string and any (commonly used in registrations)
StringAnyTuple = tuple[str, Any]

# Type for lists of tuples (commonly used for tracking)
TupleList = list[tuple[str, Any]]

# --- Plugin System Types ---

# Type for plugin class references
PluginClass = type[Any]

# Type for plugin instance references
PluginInstance = Any  # Forward reference to avoid circular imports

# Type for plugin name lists
PluginNameList = list[str]

# Type for plugin dependency lists
PluginDependencyList = list[Any]  # Forward reference for Depends objects

# --- Forward Reference Strings ---
# These are used in type hints to avoid circular imports

# Forward reference for PluginBase
PLUGIN_BASE_TYPE = "PluginBase"

# Forward reference for PluggingerAppInstance
APP_INSTANCE_TYPE = "PluggingerAppInstance"

# Forward reference for PluggingerAppBuilder
APP_BUILDER_TYPE = "PluggingerAppBuilder"

# Forward reference for DIContainer
DI_CONTAINER_TYPE = "DIContainer"
