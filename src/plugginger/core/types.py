# src/plugginger/core/types.py

"""
Core type definitions with zero external dependencies.

This module contains fundamental type definitions that are used throughout
the Plugginger framework. These types have no dependencies on other parts
of the system and can be imported safely.
"""

from __future__ import annotations

from collections.abc import Awaitable, Callable, Sequence
from dataclasses import dataclass, field
from typing import TYPE_CHECKING, Any, ParamSpec, Protocol, TypeVar, runtime_checkable

if TYPE_CHECKING:
    from plugginger.api.depends import Depends
    from plugginger.api.plugin import PluginBase

# --- Generic Type Variables ---

# Generic type variable for return types
R = TypeVar("R")

# Parameter specification for generic callables
P = ParamSpec("P")

# --- Core Type Aliases ---

# Type alias for logger functions
LoggerCallable = Callable[[str], None]

# Type alias for service methods (async callables)
ServiceMethodType = Callable[P, Awaitable[R]]

# Type alias for event handlers (async callables that return None)
EventHandlerType = Callable[..., Awaitable[None]]

# Type alias for event pattern inputs
EventPatternInput = str | Sequence[str]

# --- Event Bridge Types for AppPluginBase ---

# Type for event data transformation functions
EventDataTransformer = Callable[[dict[str, str | int | bool | list[str]], str], dict[str, str | int | bool | list[str]]]

# Type for event bridge configuration entries
EventBridgeConfigEntryType = dict[str, Any]  # Keep flexible for compatibility

# Type for list of event bridge configurations
EventBridgeListType = list[EventBridgeConfigEntryType]



# --- Configuration Types ---

# Improved configuration types to replace dict[str, Any] patterns


@runtime_checkable
class ConfigProtocol(Protocol):
    """Protocol for configuration objects."""
    def get(self, key: str, default: Any = None) -> Any: ...
    def set(self, key: str, value: Any) -> None: ...

@runtime_checkable
class PluginConfigProtocol(Protocol):
    """Protocol for plugin configuration objects."""
    name: str
    version: str
    enabled: bool

@runtime_checkable
class ServiceConfigProtocol(Protocol):
    """Protocol for service configuration objects."""
    service_name: str
    enabled: bool

# Type for plugin configuration data (replacing dict[str, Any])
PluginConfigData = dict[str, str | int | bool | list[str]]

# Type for service configuration data (replacing dict[str, Any])
ServiceConfigData = dict[str, str | int | bool]

# Type for application configuration data (replacing dict[str, Any])
AppConfigData = dict[str, str | int | bool | dict[str, Any]]

# --- Instance ID Types ---

# Type for plugin instance IDs
PluginInstanceId = str

# Type for service names (fully qualified)
ServiceName = str

# Type for event type strings
EventType = str

# --- Event System Types ---

# Type for event data payloads (commonly used throughout event system)
EventData = dict[str, Any]  # Keep flexible for event system compatibility

# Type for event pattern strings (used for pattern matching)
EventPattern = str

# Type for event pattern lists (used in listener registration)
EventPatternList = list[EventPattern]

# Type for event listener method names
EventListenerMethodName = str



# --- Metadata Types ---

# Improved metadata types to replace dict[str, Any] patterns

@dataclass
class PluginMetadataTyped:
    """Typed plugin metadata to replace dict[str, Any]."""
    name: str
    version: str
    description: str = ""
    author: str = ""
    dependencies: list[str] = field(default_factory=list)
    tags: list[str] = field(default_factory=list)

@dataclass
class ServiceMetadataTyped:
    """Typed service metadata to replace dict[str, Any]."""
    service_name: str
    plugin_name: str
    method_name: str
    description: str = ""

@dataclass
class EventListenerMetadataTyped:
    """Typed event listener metadata to replace dict[str, Any]."""
    listener_name: str
    plugin_name: str
    method_name: str
    patterns: list[str] = field(default_factory=list)

# Legacy types for backward compatibility (to be phased out)
PluginMetadata = dict[str, Any]  # TODO: Replace with PluginMetadataTyped
ServiceMetadata = dict[str, Any]  # TODO: Replace with ServiceMetadataTyped
EventListenerMetadata = dict[str, Any]  # TODO: Replace with EventListenerMetadataTyped
DependencyMetadata = dict[str, Any]  # TODO: Create DependencyMetadataTyped
ConfigMetadata = dict[str, Any]  # TODO: Create ConfigMetadataTyped

# --- Utility Types ---

# Type for optional values
Optional = TypeVar("Optional")

# Type for JSON-serializable values (recursive definition)
JsonValue = str | int | float | bool | None | dict[str, "JsonValue"] | list["JsonValue"]

# Type for configuration values
ConfigValue = JsonValue

# --- Collection Types ---

# Type for string lists (commonly used for patterns, names, etc.)
StringList = list[str]

# Type for string to string mappings (commonly used for simple configs)
StringDict = dict[str, str]


# --- Validation Types ---

@dataclass
class ValidationResult:
    """Generic validation result with errors, warnings, and metadata."""

    valid: bool
    errors: list[str] = field(default_factory=list)
    warnings: list[str] = field(default_factory=list)
    metadata: dict[str, Any] = field(default_factory=dict)

# Type for string to any mappings (most common dict type in the codebase)
# TODO: Replace with more specific types where possible
AnyDict = dict[str, Any]

# Type for lists of any values
# TODO: Replace with more specific types where possible
AnyList = list[Any]

# Type for tuples of string and any (commonly used in registrations)
# TODO: Replace with more specific types where possible
StringAnyTuple = tuple[str, Any]

# Type for lists of tuples (commonly used for tracking)
# TODO: Replace with more specific types where possible
TupleList = list[tuple[str, Any]]

# --- Plugin System Types ---

# Type for plugin class references (more specific than type[Any])
PluginClass = type["PluginBase"]

# Type for plugin instance references (forward reference to avoid circular imports)
PluginInstance = "PluginBase"  # String forward reference for type safety

# Type for plugin name lists
PluginNameList = list[str]

# Type for plugin dependency lists (forward reference for Depends objects)
PluginDependencyList = list["Depends"]  # String forward reference for type safety

# --- Forward Reference Strings ---
# These are used in type hints to avoid circular imports

# Forward reference for PluginBase
PLUGIN_BASE_TYPE = "PluginBase"

# Forward reference for PluggingerAppInstance
APP_INSTANCE_TYPE = "PluggingerAppInstance"

# Forward reference for PluggingerAppBuilder
APP_BUILDER_TYPE = "PluggingerAppBuilder"

# Forward reference for DIContainer
DI_CONTAINER_TYPE = "DIContainer"

# --- Type Safety Service Types ---

@dataclass
class MissingAnnotationTyped:
    """Typed missing annotation data for type safety analysis."""
    name: str
    annotation_type: str  # "return_annotation" | "parameter_annotation"
    line: int
    context: str

@dataclass
class AnyUsageTyped:
    """Typed Any usage data for type safety analysis."""
    line: int
    context: str
    usage_type: str = "generic"  # "generic" | "dict" | "list" | "return" | "parameter"

@dataclass
class InconsistentPatternTyped:
    """Typed inconsistent pattern data for type safety analysis."""
    pattern_type: str
    description: str
    severity: str  # "low" | "medium" | "high"
    line: int | None = None
    suggested_fix: str | None = None

@dataclass
class ProtocolViolationTyped:
    """Typed protocol violation data for type safety analysis."""
    violation_type: str
    description: str
    line: int
    expected: str
    actual: str

@dataclass
class TypeSafetyAnalysisTyped:
    """Typed type safety analysis results."""
    module_path: str
    missing_annotations: list[MissingAnnotationTyped]
    any_usage: list[AnyUsageTyped]
    inconsistent_patterns: list[InconsistentPatternTyped]
    protocol_violations: list[ProtocolViolationTyped]
    improvement_suggestions: list[str]
    error: str | None = None

@dataclass
class APIInconsistencyTyped:
    """Typed API inconsistency data."""
    inconsistency_type: str
    description: str
    severity: str  # "low" | "medium" | "high"
    module: str | None = None

@dataclass
class APIConsistencyAnalysisTyped:
    """Typed API consistency analysis results."""
    analyzed_modules: int
    api_patterns: dict[str, TypeSafetyAnalysisTyped]
    inconsistencies: list[APIInconsistencyTyped]
    consistency_score: float

@dataclass
class TypeImprovementSuggestionTyped:
    """Typed type improvement suggestion."""
    suggestion_type: str  # "add_type_annotation" | "replace_any_type" | "fix_inconsistency"
    location: MissingAnnotationTyped | AnyUsageTyped | InconsistentPatternTyped
    priority: str  # "low" | "medium" | "high"
    description: str
    suggested_fix: str

# --- Event System Types ---

# Type aliases for event system - more specific than dict[str, Any]
# EventData already defined above - using consistent definition
EventPayload = dict[str, Any]  # Event payload in dispatchers - keep flexible for compatibility
BridgeConfigData = dict[str, Any]  # Bridge configuration - keep flexible for compatibility
ServiceMetaData = dict[str, Any]  # Service metadata - keep flexible for compatibility
EventMetaData = dict[str, Any]  # Event metadata - keep flexible for compatibility

# --- Error Handling Types ---

@dataclass
class ErrorDataTyped:
    """Typed error data to replace dict[str, Any] in error handling."""
    error_code: str
    category: str
    message: str
    operation: str
    context: dict[str, Any] = field(default_factory=dict)
    operation_context: dict[str, Any] = field(default_factory=dict)
    suggestion: str = ""
    related_docs: str | None = None
    timestamp: str | None = None
    stack_trace: str | None = None

@dataclass
class ErrorSummaryTyped:
    """Typed error summary to replace dict[str, Any] in error reporting."""
    total_errors: int
    by_category: dict[str, int]
    recent_errors: list[ErrorDataTyped]
    suggestions: list[str]
    most_common_errors: list[CommonErrorTyped]

@dataclass
class CommonErrorTyped:
    """Typed common error data to replace dict[str, Any]."""
    error_code: str
    count: int
