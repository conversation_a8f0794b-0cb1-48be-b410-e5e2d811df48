# src/plugginger/_internal/runtime/lifecycle.py

"""
Plugin lifecycle management for the Plugginger framework.

This module provides the LifecycleManager class that orchestrates the setup
and teardown phases of plugins in the correct order with proper error handling.
"""

from __future__ import annotations

import logging
from collections.abc import Iterable
from typing import cast

from pydantic import BaseModel

from plugginger.api.plugin import PluginBase
from plugginger.core.exceptions import PluginTeardownError
from plugginger.core.types import LoggerCallable
from plugginger.services import ErrorService


class LifecycleManager:
    """
    Manager for orchestrating plugin lifecycle operations.

    This class handles the setup and teardown phases of plugins in the correct
    order, with comprehensive error handling and configuration management.

    Attributes:
        _plugins_in_setup_order: List of plugin instances in setup order
        _plugin_configs_for_setup: Mapping of instance IDs to configuration models
        _logger: Logger function for debugging and error reporting
        _plugins_were_setup: Flag indicating whether plugins have been set up
    """

    def __init__(
        self,
        plugins_in_setup_order: Iterable[PluginBase],
        plugin_configs_for_setup: dict[str, BaseModel],
        logger: LoggerCallable,
    ) -> None:
        """
        Initialize the lifecycle manager.

        Args:
            plugins_in_setup_order: Plugin instances in dependency-resolved setup order
            plugin_configs_for_setup: Mapping of plugin instance IDs to config models
            logger: Logger function for debugging and error reporting
        """
        self._plugins_in_setup_order = list(plugins_in_setup_order)
        self._plugin_configs_for_setup = plugin_configs_for_setup
        self._logger = logger
        self._plugins_were_setup = False

        # Initialize centralized error handling with proper logger
        # Convert LoggerCallable to actual Logger for ErrorService
        actual_logger = logging.getLogger("plugginger.lifecycle")
        self._error_service = ErrorService(actual_logger)

    async def setup_all_plugins(self) -> None:
        """
        Set up all plugins in the correct dependency order.

        This method iterates through all plugins and calls their setup() method
        with the appropriate configuration. If any plugin fails during setup,
        the entire process is aborted with a ConfigurationError.

        Raises:
            ConfigurationError: If any plugin fails during setup
        """
        if self._plugins_were_setup:
            self._logger("[Plugginger] Warning: Re-running setup on already set up plugins")
            # Allow re-setup with warning (useful for testing/development)

        self._logger("[Plugginger] Initiating setup sequence...")

        for plugin_instance in self._plugins_in_setup_order:
            instance_id = plugin_instance._plugginger_instance_id

            # Get or create plugin configuration
            plugin_config_model = self._plugin_configs_for_setup.get(instance_id)

            if plugin_config_model is None:
                # Try to create default configuration from schema
                schema = getattr(plugin_instance, "_plugginger_config_schema", None)
                if schema is not None:
                    plugin_config_model = schema()  # Create default instance
                    self._logger(
                        f"[Plugginger] Warning: No pre-validated config found for plugin '{instance_id}'. "
                        "Using default schema instance."
                    )
                else:
                    # No schema available, use empty dict cast to BaseModel for type consistency
                    plugin_config_model = cast(BaseModel, {})

            self._logger(f"[Plugginger] Setting up plugin '{instance_id}'...")

            try:
                # Call setup method if it exists and is callable
                if hasattr(plugin_instance, "setup") and callable(plugin_instance.setup):
                    await plugin_instance.setup(plugin_config_model)

                self._logger(f"[Plugginger] Plugin '{instance_id}' setup complete")

            except Exception as e:
                # Use centralized error handling for plugin setup failures
                self._error_service.handle_plugin_error(
                    error=e,
                    plugin_name=instance_id,
                    operation="setup",
                    context={
                        "plugin_class": type(plugin_instance).__name__,
                        "plugin_module": type(plugin_instance).__module__,
                        "config_provided": plugin_config_model is not None,
                        "setup_phase": "plugin_lifecycle"
                    }
                )

        self._plugins_were_setup = True
        self._logger("[Plugginger] All plugins successfully set up")

    async def teardown_all_plugins(self) -> None:
        """
        Tear down all plugins in reverse setup order.

        This method iterates through all plugins in reverse order and calls their
        teardown() method. Unlike setup, teardown continues even if individual
        plugins fail, collecting all errors and raising a PluginTeardownError
        at the end if any failures occurred.

        Raises:
            PluginTeardownError: If one or more plugins fail during teardown
        """
        if not self._plugins_were_setup:
            self._logger("[Plugginger] Teardown skipped - plugins were not set up")
            return

        self._logger("[Plugginger] Initiating teardown sequence...")

        collected_errors: list[BaseException] = []

        # Iterate in reverse order for teardown
        for plugin_instance in reversed(self._plugins_in_setup_order):
            instance_id = plugin_instance._plugginger_instance_id
            self._logger(f"[Plugginger] Tearing down plugin '{instance_id}'...")

            try:
                # Call teardown method if it exists and is callable
                if hasattr(plugin_instance, "teardown") and callable(plugin_instance.teardown):
                    await plugin_instance.teardown()

                self._logger(f"[Plugginger] Plugin '{instance_id}' teardown complete")

            except Exception as e:
                # Use centralized error handling for plugin teardown failures
                # Note: We don't re-raise here to continue teardown of other plugins
                try:
                    self._error_service.handle_plugin_error(
                        error=e,
                        plugin_name=instance_id,
                        operation="teardown",
                        context={
                            "plugin_class": type(plugin_instance).__name__,
                            "plugin_module": type(plugin_instance).__module__,
                            "teardown_phase": "plugin_lifecycle",
                            "continue_teardown": True
                        }
                    )
                except Exception:
                    # If error service fails, fall back to collecting the original error
                    error_msg = (
                        f"Plugin '{instance_id}' (class {type(plugin_instance).__name__}) "
                        f"failed during teardown"
                    )
                    self._logger(f"[Plugginger] ERROR: {error_msg}: {e!r}")

                    # Collect error but continue with other plugins
                    wrapped_error = RuntimeError(error_msg)
                    wrapped_error.__cause__ = e
                    collected_errors.append(wrapped_error)

        # Check if any errors occurred during teardown
        if collected_errors:
            raise PluginTeardownError(
                "One or more plugins failed during teardown.", individual_errors=collected_errors
            )

        self._plugins_were_setup = False
        self._logger("[Plugginger] All plugins successfully torn down")

    def get_plugins_were_setup_flag(self) -> bool:
        """
        Get the current setup status of plugins.

        Returns:
            True if plugins have been set up, False otherwise
        """
        return self._plugins_were_setup
