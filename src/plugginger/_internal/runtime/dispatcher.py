# src/plugginger/_internal/runtime/dispatcher.py

"""
Service and Event dispatcher implementations for the Plugginger framework.

This module provides the ServiceDispatcher and EventDispatcher classes that handle
service registration/execution and event listener registration/dispatch respectively.
"""

from __future__ import annotations

import asyncio
import fnmatch
import inspect
from collections import defaultdict
from collections.abc import Awaitable
from typing import Any

from plugginger._internal.runtime.fault_policy import FaultPolicyHandler
from plugginger.core.exceptions import (
    ServiceExecutionError,
    ServiceNameConflictError,
    ServiceNotFoundError,
)
from plugginger.core.types import (
    EventData,
    EventHandlerType,
    EventPayload,
    LoggerCallable,
    ServiceMethodType,
)


class EventPatternMatcher:
    """
    Handles pattern matching for event types against registered listener patterns.

    This class encapsulates the fnmatch-based pattern matching logic and provides
    caching for improved performance on frequently used event types.

    Attributes:
        _listeners: Dictionary mapping event patterns to lists of event handlers
        _pattern_cache: Cache for pattern matching results to improve performance
    """

    def __init__(self, listeners: dict[str, list[EventHandlerType]]) -> None:
        """
        Initialize the pattern matcher.

        Args:
            listeners: Dictionary mapping event patterns to handler lists
        """
        self._listeners = listeners
        self._pattern_cache: dict[str, list[tuple[str, EventHandlerType]]] = {}

    def find_matching_listeners(self, event_type: str) -> list[tuple[str, EventHandlerType]]:
        """
        Find all listeners whose patterns match the given event type.

        Uses fnmatch for pattern matching and caches results for performance.

        Args:
            event_type: The event type to match against patterns

        Returns:
            List of (pattern, handler) tuples for matching listeners
        """
        # Check cache first
        if event_type in self._pattern_cache:
            return self._pattern_cache[event_type]

        matching_listeners: list[tuple[str, EventHandlerType]] = []

        # Iterate over a copy to handle potential modifications during iteration
        for pattern, listeners_for_pattern in list(self._listeners.items()):
            if fnmatch.fnmatchcase(event_type, pattern):
                for listener in listeners_for_pattern:
                    matching_listeners.append((pattern, listener))

        # Cache the result for future use
        self._pattern_cache[event_type] = matching_listeners
        return matching_listeners

    def get_all_patterns(self) -> list[str]:
        """
        Get a list of all registered event patterns.

        Returns:
            List of event patterns that have registered listeners
        """
        return list(self._listeners.keys())

    def invalidate_cache(self) -> None:
        """
        Clear the pattern matching cache.

        Should be called when listeners are added or removed to ensure
        cache consistency.
        """
        self._pattern_cache.clear()

    def invalidate_cache_for_pattern(self, pattern: str) -> None:
        """
        Invalidate cache entries that might be affected by changes to a pattern.

        Args:
            pattern: The pattern that was modified
        """
        # For simplicity, clear entire cache when any pattern changes
        # Could be optimized to only clear affected entries
        self._pattern_cache.clear()


class ListenerTaskManager:
    """
    Manages the execution and lifecycle of event listener tasks.

    This class handles task creation, fault policy integration, timeout management,
    and cleanup of completed tasks for event listeners.

    Attributes:
        _fault_policy_handler: Handler for managing listener errors and isolation
        _logger: Logger for debugging and error reporting
        _default_timeout: Default timeout for listener execution
        _active_tasks: Set of currently running listener tasks
    """

    def __init__(
        self,
        fault_policy_handler: FaultPolicyHandler,
        logger: LoggerCallable,
        default_timeout: float
    ) -> None:
        """
        Initialize the task manager.

        Args:
            fault_policy_handler: Handler for managing listener errors and isolation
            logger: Logger for debugging and error reporting
            default_timeout: Default timeout for listener execution in seconds
        """
        self._fault_policy_handler = fault_policy_handler
        self._logger = logger
        self._default_timeout = default_timeout
        self._active_tasks: set[asyncio.Task[None]] = set()

    async def execute_listeners(
        self,
        matched_listeners: list[tuple[str, EventHandlerType]],
        event_type: str,
        event_data: EventData
    ) -> list[asyncio.Task[None]]:
        """
        Create and execute tasks for all matched listeners.

        Args:
            matched_listeners: List of (pattern, handler) tuples to execute
            event_type: The event type being emitted
            event_data: The event payload data

        Returns:
            List of created tasks for this event
        """
        tasks_created: list[asyncio.Task[None]] = []

        for pattern, listener in matched_listeners:
            # Check if listener should be invoked (fault policy)
            if not self._fault_policy_handler.should_invoke(listener):
                self._logger(
                    f"[Plugginger] Skipping isolated listener '{listener.__qualname__}'"
                )
                continue

            # Determine listener signature for event_type parameter
            pass_event_type_arg = self._should_pass_event_type(listener)

            # Create task for listener
            task_name = f"event_listener:{event_type}:{listener.__qualname__}"
            task = asyncio.create_task(
                self._safe_call_listener(listener, event_data, event_type, pass_event_type_arg),
                name=task_name,
            )
            tasks_created.append(task)
            self._active_tasks.add(task)

            self._logger(f"[Plugginger] Event '{event_type}' matched pattern '{pattern}'")

        return tasks_created

    def _should_pass_event_type(self, listener: EventHandlerType) -> bool:
        """
        Determine if the event_type should be passed to the listener.

        Args:
            listener: The event listener function

        Returns:
            True if event_type should be passed as a parameter
        """
        sig = inspect.signature(listener)
        params = list(sig.parameters.values())
        is_method = params and params[0].name in ("self", "cls")
        num_effective_params = len(params) - (1 if is_method else 0)
        return num_effective_params == 2

    async def _safe_call_listener(
        self,
        listener: EventHandlerType,
        payload: EventPayload,
        event_type_str: str,
        pass_event_type_to_handler: bool,
    ) -> None:
        """
        Safely execute an event listener with timeout and error handling.

        Args:
            listener: Event listener function to execute
            payload: Event data payload
            event_type_str: Event type string
            pass_event_type_to_handler: Whether to pass event_type as keyword argument
        """
        current_task = asyncio.current_task()

        try:
            # Prepare listener call based on signature
            listener_call_awaitable: Awaitable[None]
            if pass_event_type_to_handler:
                listener_call_awaitable = listener(payload, event_type=event_type_str)
            else:
                listener_call_awaitable = listener(payload)

            # Execute with timeout
            await asyncio.wait_for(listener_call_awaitable, timeout=self._default_timeout)

        except TimeoutError as exc:
            self._fault_policy_handler.handle_error(listener.__qualname__, id(listener), exc)
        except asyncio.CancelledError:
            self._logger(f"[Plugginger] Event listener '{listener.__qualname__}' was cancelled")
            raise  # Re-raise CancelledError to properly handle cancellation
        except Exception as exc:
            self._fault_policy_handler.handle_error(listener.__qualname__, id(listener), exc)
        finally:
            # Clean up task reference
            if current_task is not None and current_task in self._active_tasks:
                self._active_tasks.remove(current_task)

    def cleanup_completed_tasks(self) -> None:
        """
        Clean up completed listener tasks and log any exceptions.

        This method removes completed tasks from the active task set
        and logs any exceptions that occurred during task execution.
        """
        completed_tasks = {task for task in self._active_tasks if task.done()}

        for task in completed_tasks:
            self._active_tasks.remove(task)

            # Check for exceptions (but not CancelledError)
            try:
                exception = task.exception()
                if exception is not None and not isinstance(exception, asyncio.CancelledError):
                    self._logger(
                        f"[Plugginger] Background listener task '{task.get_name()}' failed: {exception!r}"
                    )
            except asyncio.CancelledError:
                # Task was cancelled, this is expected during shutdown
                pass

    def get_active_task_count(self) -> int:
        """
        Get the number of currently active listener tasks.

        Returns:
            Number of active tasks
        """
        return len(self._active_tasks)

    def cancel_all_tasks(self) -> set[asyncio.Task[None]]:
        """
        Cancel all active listener tasks.

        Returns:
            Set of cancelled tasks
        """
        active_tasks_copy = set(self._active_tasks)
        for task in active_tasks_copy:
            task.cancel()
        return active_tasks_copy

    def clear_all_tasks(self) -> None:
        """Clear the active task set."""
        self._active_tasks.clear()


class ServiceDispatcher:
    """
    Dispatcher for managing and executing plugin services.

    This class provides centralized service registration and execution with
    comprehensive error handling and logging. Services are identified by
    fully qualified names and must be async coroutines.

    Attributes:
        _logger: Logger function for debugging and error reporting
        _services: Dictionary mapping service names to service method implementations
    """

    def __init__(self, logger: LoggerCallable) -> None:
        """
        Initialize the service dispatcher.

        Args:
            logger: Logger function for debugging and error reporting
        """
        self._logger = logger
        self._services: dict[str, ServiceMethodType[..., Any]] = {}

    def add_service(
        self, fully_qualified_name: str, service_coroutine: ServiceMethodType[..., Any]
    ) -> None:
        """
        Register a new service with the given fully qualified name.

        Args:
            fully_qualified_name: Unique service identifier (e.g., "user.get_by_id")
            service_coroutine: Async service method implementation

        Raises:
            ServiceNameConflictError: If service name already exists
        """
        # Check for name conflicts
        if fully_qualified_name in self._services:
            error_msg = f"Service '{fully_qualified_name}' is already registered"
            self._logger(f"[Plugginger] ERROR: {error_msg}")
            raise ServiceNameConflictError(error_msg)

        # Register the service
        self._services[fully_qualified_name] = service_coroutine
        self._logger(f"[Plugginger] Registered service '{fully_qualified_name}'")

    async def call_service(self, fully_qualified_name: str, *args: Any, **kwargs: Any) -> Any:
        """
        Execute a registered service by name.

        Args:
            fully_qualified_name: Name of the service to call
            *args: Positional arguments to pass to the service
            **kwargs: Keyword arguments to pass to the service

        Returns:
            The result returned by the service method

        Raises:
            ServiceNotFoundError: If service is not registered
            ServiceExecutionError: If service execution fails
        """
        self._logger(f"[Plugginger] Calling service '{fully_qualified_name}'")

        # Retrieve service method
        try:
            service_method = self._services[fully_qualified_name]
        except KeyError:
            error_msg = f"Service '{fully_qualified_name}' not found"
            self._logger(f"[Plugginger] ERROR: {error_msg}")
            raise ServiceNotFoundError(error_msg) from None

        # Execute service method
        try:
            result = await service_method(*args, **kwargs)
            self._logger(f"[Plugginger] Service '{fully_qualified_name}' completed successfully")
            return result
        except Exception as exc:
            error_msg = f"Error executing service '{fully_qualified_name}': {exc!r}"
            self._logger(f"[Plugginger] ERROR: {error_msg}")
            raise ServiceExecutionError(
                f"Error executing service '{fully_qualified_name}'."
            ) from exc

    def has_service(self, name: str) -> bool:
        """
        Check if a service is registered.

        Args:
            name: Service name to check

        Returns:
            True if service exists, False otherwise
        """
        return name in self._services

    def list_services(self) -> list[str]:
        """
        Get a list of all registered service names.

        Returns:
            List of registered service names
        """
        return list(self._services.keys())

    def remove_service(self, name: str) -> bool:
        """
        Remove a registered service.

        Args:
            name: Name of the service to remove

        Returns:
            True if service was removed, False if it didn't exist
        """
        if name in self._services:
            del self._services[name]
            self._logger(f"[Plugginger] Removed service '{name}'")
            return True
        else:
            self._logger(f"[Plugginger] Service '{name}' not found for removal")
            return False


class EventDispatcher:
    """
    Dispatcher for managing event listeners and dispatching events.

    This class provides centralized event listener registration and event dispatch
    with pattern matching, fault handling, and concurrent task management.
    Uses specialized components for pattern matching and task management.

    Attributes:
        _fault_handler: Handler for managing listener errors and isolation
        _logger: Logger function for debugging and error reporting
        _max_concurrent_listener_tasks: Maximum number of concurrent listener tasks
        _pattern_to_listeners_map: Mapping of event patterns to listener lists
        _shutdown_requested: Event flag indicating shutdown is in progress
        _pattern_matcher: Component for handling event pattern matching
        _task_manager: Component for managing listener task execution
    """

    def __init__(
        self,
        fault_handler: FaultPolicyHandler,
        logger: LoggerCallable,
        default_listener_timeout: float,
        max_concurrent_listener_tasks: int,
    ) -> None:
        """
        Initialize the event dispatcher.

        Args:
            fault_handler: Handler for managing listener errors and isolation
            logger: Logger function for debugging and error reporting
            default_listener_timeout: Default timeout for listener execution in seconds
            max_concurrent_listener_tasks: Maximum number of concurrent listener tasks
        """
        self._fault_handler = fault_handler
        self._logger = logger
        self._max_concurrent_listener_tasks = max_concurrent_listener_tasks
        self._pattern_to_listeners_map: dict[str, list[EventHandlerType]] = defaultdict(list)
        self._shutdown_requested: asyncio.Event = asyncio.Event()

        # Initialize specialized components
        self._pattern_matcher = EventPatternMatcher(self._pattern_to_listeners_map)
        self._task_manager = ListenerTaskManager(fault_handler, logger, default_listener_timeout)

    def add_listener(self, event_pattern: str, listener_coroutine: EventHandlerType) -> None:
        """
        Register an event listener for a specific pattern.

        Args:
            event_pattern: Event pattern to listen for (supports wildcards)
            listener_coroutine: Async event handler function
        """
        self._pattern_to_listeners_map[event_pattern].append(listener_coroutine)
        # Invalidate pattern cache since we added a new listener
        self._pattern_matcher.invalidate_cache_for_pattern(event_pattern)
        self._logger(
            f"[Plugginger] Registered event listener '{listener_coroutine.__qualname__}' "
            f"for pattern '{event_pattern}'"
        )

    def remove_listener(self, event_pattern: str, listener_coroutine: EventHandlerType) -> bool:
        """
        Remove an event listener from a pattern.

        Args:
            event_pattern: Event pattern the listener is registered for
            listener_coroutine: Event handler function to remove

        Returns:
            True if listener was removed, False if it wasn't found
        """
        if event_pattern in self._pattern_to_listeners_map:
            listeners = self._pattern_to_listeners_map[event_pattern]
            if listener_coroutine in listeners:
                listeners.remove(listener_coroutine)
                # Invalidate pattern cache since we removed a listener
                self._pattern_matcher.invalidate_cache_for_pattern(event_pattern)
                self._logger(
                    f"[Plugginger] Removed event listener '{listener_coroutine.__qualname__}' "
                    f"from pattern '{event_pattern}'"
                )
                # Clean up empty pattern entries
                if not listeners:
                    del self._pattern_to_listeners_map[event_pattern]
                return True

        self._logger(
            f"[Plugginger] Event listener '{listener_coroutine.__qualname__}' "
            f"not found for pattern '{event_pattern}'"
        )
        return False

    def list_patterns(self) -> list[str]:
        """
        Get a list of all registered event patterns.

        Returns:
            List of event patterns that have registered listeners
        """
        return list(self._pattern_to_listeners_map.keys())

    async def emit_event(self, event_type: str, event_data: EventData) -> None:
        """
        Emit an event to all matching listeners.

        This method dispatches events to all registered listeners whose patterns
        match the event type. It uses specialized components for pattern matching
        and task management to reduce complexity and improve maintainability.

        Args:
            event_type: Type of event to emit
            event_data: Event payload data
        """
        # Check if shutdown is in progress
        if self._shutdown_requested.is_set():
            self._logger(f"[Plugginger] Ignoring event '{event_type}' - shutting down")
            return

        self._logger(f"[Plugginger] Emitting event: '{event_type}'")

        # Clean up completed tasks
        self._task_manager.cleanup_completed_tasks()

        # Handle backpressure - check concurrent task limit
        if self._task_manager.get_active_task_count() >= self._max_concurrent_listener_tasks:
            self._logger(
                f"[Plugginger] Warning: Max concurrent listener tasks reached "
                f"({self._max_concurrent_listener_tasks})"
            )
            # Brief wait for some tasks to complete (non-blocking backpressure simulation)
            await self._handle_backpressure()

        # Find matching listeners using pattern matcher
        matched_listeners = self._pattern_matcher.find_matching_listeners(event_type)

        # Execute listeners using task manager
        tasks_created = await self._task_manager.execute_listeners(
            matched_listeners, event_type, event_data
        )

        # Log task creation summary
        if not tasks_created:
            self._logger(f"[Plugginger] No active listeners for event '{event_type}'")
        else:
            self._logger(
                f"[Plugginger] Created {len(tasks_created)} tasks for event '{event_type}'"
            )

    async def _handle_backpressure(self) -> None:
        """
        Handle backpressure when too many concurrent tasks are running.

        Waits briefly for some tasks to complete to reduce load.
        """
        active_task_count = self._task_manager.get_active_task_count()
        if active_task_count > 0:
            try:
                # Get a sample of active tasks for waiting (we can't access them directly)
                # This is a simplified backpressure mechanism
                await asyncio.sleep(0.05)  # Brief pause to allow tasks to complete
                self._task_manager.cleanup_completed_tasks()
            except Exception:
                pass  # Continue even if backpressure handling fails

    async def shutdown(self) -> None:
        """
        Shutdown the event dispatcher and cancel all active listener tasks.

        This method sets the shutdown flag, cancels all running listener tasks,
        and waits for them to complete or be cancelled.
        """
        self._shutdown_requested.set()
        self._logger("[Plugginger] Event dispatcher shutdown initiated")

        # Cancel all active tasks using task manager
        active_tasks_copy = self._task_manager.cancel_all_tasks()

        # Wait for all tasks to complete or be cancelled
        if active_tasks_copy:
            self._logger(
                f"[Plugginger] Waiting for {len(active_tasks_copy)} listener tasks to complete"
            )
            await asyncio.gather(*active_tasks_copy, return_exceptions=True)

        # Clear the task set
        self._task_manager.clear_all_tasks()
        self._logger("[Plugginger] Event dispatcher shutdown completed")
