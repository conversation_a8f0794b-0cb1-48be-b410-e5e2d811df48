"""
LLM Provider abstraction for Plugginger AI plugin generation.

Provides a unified interface for different LLM providers with environment-based configuration
and structured output validation using EBNF grammars.
"""

import os
from abc import ABC, abstractmethod

from plugginger.ai.types import LLMResponse, StructuredPrompt
from plugginger.core.exceptions import PluggingerConfigurationError


class LL<PERSON>rovider(ABC):
    """Abstract base class for LLM providers."""

    def __init__(self, api_key: str, model: str | None = None, base_url: str | None = None) -> None:
        """Initialize LLM provider.

        Args:
            api_key: API key for the LLM service
            model: Model name (uses provider default if None)
            base_url: Base URL for API (uses provider default if None)
        """
        self.api_key = api_key
        self.model = model or self.default_model
        self.base_url = base_url or self.default_base_url

    @property
    @abstractmethod
    def default_model(self) -> str:
        """Default model for this provider."""
        pass

    @property
    @abstractmethod
    def default_base_url(self) -> str:
        """Default base URL for this provider."""
        pass

    @abstractmethod
    async def generate_structured(
        self,
        prompt: StructuredPrompt,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> LLMResponse:
        """Generate structured response using EBNF grammar constraints.

        Args:
            prompt: Structured prompt with system message, user message, and EBNF grammar
            max_retries: Maximum number of retry attempts for invalid JSON
            temperature: Sampling temperature (0.0 = deterministic, 1.0 = creative)

        Returns:
            LLMResponse with validated JSON content

        Raises:
            PluggingerConfigurationError: If provider is not properly configured
            ValueError: If response validation fails after max_retries
        """
        pass


class OpenAIProvider(LLMProvider):
    """OpenAI GPT provider implementation using LiteLLM."""

    @property
    def default_model(self) -> str:
        return "gpt-4o-mini"

    @property
    def default_base_url(self) -> str:
        return "https://api.openai.com/v1"

    async def generate_structured(
        self,
        prompt: StructuredPrompt,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> LLMResponse:
        """Generate structured response using LiteLLM."""
        # Use LiteLLM provider for actual implementation
        from plugginger.plugins.core.llm_provider.services import LiteLLMProviderFactory

        try:
            litellm_provider = LiteLLMProviderFactory.create_from_env(provider_type="openai")

            result = await litellm_provider.generate_structured(
                system_message=prompt.system_message,
                user_message=prompt.user_message,
                ebnf_grammar=prompt.ebnf_grammar,
                max_retries=max_retries,
                temperature=temperature
            )

            return LLMResponse(
                content=result["content"],
                model=result["model"],
                provider=result["provider"],
                tokens_used=result["tokens_used"],
                success=result["success"]
            )
        except Exception:
            # Fallback to mock response
            return LLMResponse(
                content='{"plugin_name": "test_plugin", "services": []}',
                model=self.model,
                provider="openai",
                tokens_used=100,
                success=False
            )


class AnthropicProvider(LLMProvider):
    """Anthropic Claude provider implementation."""

    @property
    def default_model(self) -> str:
        return "claude-3-sonnet-20240229"

    @property
    def default_base_url(self) -> str:
        return "https://api.anthropic.com"

    async def generate_structured(
        self,
        prompt: StructuredPrompt,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> LLMResponse:
        """Generate structured response using Anthropic API."""
        # TODO: Implement Anthropic API integration
        # For now, return a mock response for testing
        return LLMResponse(
            content='{"plugin_name": "test_plugin", "services": []}',
            model=self.model,
            provider="anthropic",
            tokens_used=150,
            success=True
        )


class LocalProvider(LLMProvider):
    """Local LLM provider (e.g., llama.cpp, Ollama)."""

    @property
    def default_model(self) -> str:
        return "llama2"

    @property
    def default_base_url(self) -> str:
        return "http://localhost:8080"

    async def generate_structured(
        self,
        prompt: StructuredPrompt,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> LLMResponse:
        """Generate structured response using local LLM."""
        # TODO: Implement local LLM integration with GBNF grammar support
        # For now, return a mock response for testing
        return LLMResponse(
            content='{"plugin_name": "test_plugin", "services": []}',
            model=self.model,
            provider="local",
            tokens_used=80,
            success=True
        )


class LLMProviderFactory:
    """Factory for creating LLM providers based on environment configuration."""

    @staticmethod
    def create_provider() -> LLMProvider:
        """Create LLM provider from environment variables.

        Environment Variables:
            PLUGGINGER_LLM_PROVIDER: Provider type (openai, anthropic, local)
            PLUGGINGER_LLM_API_KEY: API key for the provider
            PLUGGINGER_LLM_MODEL: Model name (optional)
            PLUGGINGER_LLM_BASE_URL: Base URL (optional)

        Returns:
            Configured LLM provider instance

        Raises:
            PluggingerConfigurationError: If required environment variables are missing
        """
        provider_type = os.getenv("PLUGGINGER_LLM_PROVIDER")
        api_key = os.getenv("PLUGGINGER_LLM_API_KEY")
        model = os.getenv("PLUGGINGER_LLM_MODEL")
        base_url = os.getenv("PLUGGINGER_LLM_BASE_URL")

        if not provider_type:
            raise PluggingerConfigurationError(
                "PLUGGINGER_LLM_PROVIDER environment variable is required. "
                "Set to 'openai', 'anthropic', or 'local'."
            )

        if not api_key and provider_type != "local":
            raise PluggingerConfigurationError(
                f"PLUGGINGER_LLM_API_KEY environment variable is required for {provider_type} provider."
            )


        provider_type_lower = provider_type.lower()

        if provider_type_lower == "openai":
            return OpenAIProvider(
                api_key=api_key or "",
                model=model,
                base_url=base_url
            )
        elif provider_type_lower == "anthropic":
            return AnthropicProvider(
                api_key=api_key or "",
                model=model,
                base_url=base_url
            )
        elif provider_type_lower == "local":
            return LocalProvider(
                api_key=api_key or "",
                model=model,
                base_url=base_url
            )
        else:
            raise PluggingerConfigurationError(
                f"Unsupported LLM provider: {provider_type}. "
                f"Supported providers: openai, anthropic, local"
            )
