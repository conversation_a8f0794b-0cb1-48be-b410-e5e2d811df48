# src/plugginger/services/error_service.py

"""
Centralized error handling service.

This service provides standardized error handling patterns across the entire
Plugginger framework, eliminating the 96.4% inconsistency in error handling.
"""

from __future__ import annotations

import logging
from typing import TYPE_CHECKING, Any, NoReturn

from plugginger.core.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from plugginger.core.exceptions import <PERSON>rrorCategory, PluggingerError

if TYPE_CHECKING:
    pass


class ErrorService:
    """
    Centralized error handling service for consistent error management.
    
    This service eliminates the 96.4% inconsistency in error handling by providing
    standardized methods for common error scenarios across the framework.
    """

    def __init__(self, logger: logging.Logger) -> None:
        """Initialize the ErrorService with a logger."""
        self._error_handler = ErrorHandler(logger)
        self._logger = logger

    def handle_plugin_error(
        self,
        error: Exception,
        plugin_name: str,
        operation: str,
        context: dict[str, Any] | None = None
    ) -> NoReturn:
        """
        Handle plugin-related errors with standardized context.
        
        Args:
            error: The exception that occurred
            plugin_name: Name of the plugin where error occurred
            operation: Operation being performed (e.g., "instantiation", "setup")
            context: Additional context information
            
        Raises:
            PluggingerError: Enhanced error with plugin context
        """
        plugin_context = {
            "plugin_name": plugin_name,
            "operation": operation,
            **(context or {})
        }
        
        self._error_handler.handle_error(
            error=error,
            operation=f"plugin_{operation}",
            context=plugin_context
        )

    def handle_dependency_error(
        self,
        error: Exception,
        dependency_name: str,
        dependent_plugin: str,
        context: dict[str, Any] | None = None
    ) -> NoReturn:
        """
        Handle dependency-related errors with standardized context.
        
        Args:
            error: The exception that occurred
            dependency_name: Name of the missing/failed dependency
            dependent_plugin: Plugin that depends on the failed dependency
            context: Additional context information
            
        Raises:
            PluggingerError: Enhanced error with dependency context
        """
        dependency_context = {
            "dependency_name": dependency_name,
            "dependent_plugin": dependent_plugin,
            "error_category": ErrorCategory.DEPENDENCY_ERROR.value,
            **(context or {})
        }
        
        self._error_handler.handle_error(
            error=error,
            operation="dependency_resolution",
            context=dependency_context
        )

    def handle_configuration_error(
        self,
        error: Exception,
        config_key: str,
        config_value: Any = None,
        context: dict[str, Any] | None = None
    ) -> NoReturn:
        """
        Handle configuration-related errors with standardized context.
        
        Args:
            error: The exception that occurred
            config_key: Configuration key that caused the error
            config_value: Configuration value (if safe to log)
            context: Additional context information
            
        Raises:
            PluggingerError: Enhanced error with configuration context
        """
        config_context = {
            "config_key": config_key,
            "error_category": ErrorCategory.CONFIGURATION.value,
            **(context or {})
        }
        
        # Only include config_value if it's safe to log (not sensitive)
        if config_value is not None and not self._is_sensitive_config(config_key):
            config_context["config_value"] = str(config_value)
        
        self._error_handler.handle_error(
            error=error,
            operation="configuration_processing",
            context=config_context
        )

    def handle_build_error(
        self,
        error: Exception,
        app_name: str,
        build_phase: str,
        context: dict[str, Any] | None = None
    ) -> NoReturn:
        """
        Handle application build errors with standardized context.
        
        Args:
            error: The exception that occurred
            app_name: Name of the application being built
            build_phase: Phase of build where error occurred
            context: Additional context information
            
        Raises:
            PluggingerError: Enhanced error with build context
        """
        build_context = {
            "app_name": app_name,
            "build_phase": build_phase,
            "error_category": ErrorCategory.RUNTIME_ERROR.value,
            **(context or {})
        }
        
        self._error_handler.handle_error(
            error=error,
            operation="app_build",
            context=build_context
        )

    def handle_runtime_error(
        self,
        error: Exception,
        operation: str,
        context: dict[str, Any] | None = None
    ) -> NoReturn:
        """
        Handle general runtime errors with standardized context.
        
        Args:
            error: The exception that occurred
            operation: Operation being performed when error occurred
            context: Additional context information
            
        Raises:
            PluggingerError: Enhanced error with runtime context
        """
        runtime_context = {
            "error_category": ErrorCategory.RUNTIME_ERROR.value,
            **(context or {})
        }
        
        self._error_handler.handle_error(
            error=error,
            operation=operation,
            context=runtime_context
        )

    def handle_validation_error(
        self,
        error: Exception,
        validation_target: str,
        validation_type: str,
        context: dict[str, Any] | None = None
    ) -> NoReturn:
        """
        Handle validation errors with standardized context.
        
        Args:
            error: The exception that occurred
            validation_target: What was being validated (e.g., "manifest", "config")
            validation_type: Type of validation (e.g., "schema", "type", "constraint")
            context: Additional context information
            
        Raises:
            PluggingerError: Enhanced error with validation context
        """
        validation_context = {
            "validation_target": validation_target,
            "validation_type": validation_type,
            "error_category": ErrorCategory.USER_INPUT.value,
            **(context or {})
        }
        
        self._error_handler.handle_error(
            error=error,
            operation="validation",
            context=validation_context
        )

    def get_error_summary(self) -> dict[str, Any]:
        """
        Get comprehensive error summary for debugging and analysis.
        
        Returns:
            Dictionary containing error statistics and recent errors
        """
        return self._error_handler.get_error_summary()

    def _is_sensitive_config(self, config_key: str) -> bool:
        """
        Check if a configuration key contains sensitive information.
        
        Args:
            config_key: Configuration key to check
            
        Returns:
            True if the key likely contains sensitive data
        """
        sensitive_patterns = [
            "password", "secret", "key", "token", "credential",
            "auth", "api_key", "private", "cert", "ssl"
        ]
        
        key_lower = config_key.lower()
        return any(pattern in key_lower for pattern in sensitive_patterns)
