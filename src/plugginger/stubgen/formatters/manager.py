"""Type formatter manager for orchestrating type annotation formatting.

This module replaces the monolithic TypeHintStringifier with a clean,
strategy-pattern-based approach using specialized formatters.
"""

from __future__ import annotations

import logging
from typing import Any

from plugginger.stubgen.formatters.base import (
    BaseTypeFormatter,
    FormatterError,
    is_empty_annotation,
    MAX_RECURSION_DEPTH,
)

logger = logging.getLogger(__name__)


class TypeFormatterManager:
    """Manager for type annotation formatting using the strategy pattern.
    
    This class orchestrates multiple specialized formatters to convert
    Python type annotations into string representations suitable for
    .pyi stub files.
    
    Replaces the monolithic TypeHintStringifier with a clean, modular approach.
    """

    def __init__(self) -> None:
        """Initialize the formatter manager with an empty formatter list."""
        self._formatters: list[BaseTypeFormatter] = []

    def register_formatter(self, formatter: BaseTypeFormatter) -> None:
        """Register a type formatter.
        
        Formatters are tried in registration order, so register more specific
        formatters before more general ones.
        
        Args:
            formatter: The formatter to register
        """
        self._formatters.append(formatter)

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format a type annotation into its string representation.
        
        Args:
            annotation: The type annotation to format
            recursion_depth: Current recursion depth to prevent infinite loops
            
        Returns:
            String representation suitable for .pyi stub files
            
        Raises:
            FormatterError: If no formatter can handle the annotation
        """
        # Handle empty annotations
        if is_empty_annotation(annotation):
            return "Any"

        # Check recursion depth
        if recursion_depth > MAX_RECURSION_DEPTH:
            logger.warning(
                f"Max recursion depth ({MAX_RECURSION_DEPTH}) reached for type: {annotation!r}. "
                "Returning '...'"
            )
            return "..."

        # Try each formatter in order
        for formatter in self._formatters:
            if formatter.can_format(annotation):
                try:
                    result = formatter.format(annotation, recursion_depth)
                    return str(result)  # Ensure it's a string
                except Exception as e:
                    logger.warning(
                        f"Formatter '{formatter.__class__.__name__}' failed for "
                        f"annotation '{annotation!r}': {e!r}. Trying next formatter."
                    )
                    continue

        # No formatter could handle this annotation
        logger.error(
            f"No formatter could handle type: {annotation!r}. "
            f"Available formatters: {[f.__class__.__name__ for f in self._formatters]}"
        )
        raise FormatterError(
            f"No formatter available for annotation: {annotation!r}",
            annotation=annotation
        )

    def get_registered_formatters(self) -> list[str]:
        """Get the names of all registered formatters.
        
        Returns:
            List of formatter class names in registration order
        """
        return [formatter.__class__.__name__ for formatter in self._formatters]


def create_default_formatter_manager() -> TypeFormatterManager:
    """Create a formatter manager with all default formatters registered.
    
    This function sets up the complete formatter chain in the correct order:
    1. Built-in types (int, str, etc.)
    2. TypeVar and ForwardRef
    3. Typing module generics (List, Dict, Union, etc.)
    4. User-defined classes
    5. Fallback formatter
    
    Returns:
        Configured TypeFormatterManager ready for use
    """
    from plugginger.stubgen.formatters.builtin import BuiltinTypeFormatter
    from plugginger.stubgen.formatters.typevar import TypeVarFormatter
    from plugginger.stubgen.formatters.forward_ref import ForwardRefFormatter
    from plugginger.stubgen.formatters.typing_union import TypingUnionFormatter
    from plugginger.stubgen.formatters.typing_callable import TypingCallableFormatter
    from plugginger.stubgen.formatters.typing_literal import TypingLiteralFormatter
    from plugginger.stubgen.formatters.typing_basic import TypingBasicFormatter
    from plugginger.stubgen.formatters.user_defined import UserDefinedTypeFormatter
    from plugginger.stubgen.formatters.fallback import FallbackFormatter

    manager = TypeFormatterManager()
    
    # Register formatters in order of specificity (most specific first)
    manager.register_formatter(BuiltinTypeFormatter(manager))
    manager.register_formatter(TypeVarFormatter(manager))
    manager.register_formatter(ForwardRefFormatter(manager))
    manager.register_formatter(TypingUnionFormatter(manager))
    manager.register_formatter(TypingCallableFormatter(manager))
    manager.register_formatter(TypingLiteralFormatter(manager))
    manager.register_formatter(TypingBasicFormatter(manager))
    manager.register_formatter(UserDefinedTypeFormatter(manager))
    manager.register_formatter(FallbackFormatter(manager))  # Always last
    
    return manager


# Global instance for backward compatibility
_default_manager: TypeFormatterManager | None = None


def get_default_manager() -> TypeFormatterManager:
    """Get the default formatter manager instance.
    
    Creates the manager on first access with all default formatters.
    
    Returns:
        The default TypeFormatterManager instance
    """
    global _default_manager
    if _default_manager is None:
        _default_manager = create_default_formatter_manager()
    return _default_manager


def format_type_annotation(annotation: Any) -> str:
    """Format a type annotation using the default formatter manager.
    
    This is a convenience function for simple type formatting.
    
    Args:
        annotation: The type annotation to format
        
    Returns:
        String representation suitable for .pyi stub files
    """
    return get_default_manager().format(annotation)
