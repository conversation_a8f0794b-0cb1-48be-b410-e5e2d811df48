"""Formatter for built-in Python types.

This module handles formatting of basic Python types like int, str, bool, etc.
"""

from __future__ import annotations

from typing import Any

from plugginger.stubgen.formatters.base import BaseTypeFormatter


class BuiltinTypeFormatter(BaseTypeFormatter):
    """Formatter for built-in Python types.
    
    Handles:
    - int -> int
    - str -> str
    - bool -> bool
    - float -> float
    - bytes -> bytes
    - None -> None
    - type(None) -> None
    - list -> list
    - dict -> dict
    - tuple -> tuple
    - set -> set
    """

    # Built-in types that should be formatted as-is
    BUILTIN_TYPES = {
        int: "int",
        str: "str", 
        bool: "bool",
        float: "float",
        bytes: "bytes",
        list: "list",
        dict: "dict",
        tuple: "tuple",
        set: "set",
        type(None): "None",
    }

    def can_format(self, annotation: Any) -> bool:
        """Check if this formatter can handle built-in types.
        
        Args:
            annotation: The type annotation to check
            
        Returns:
            True if this is a built-in type
        """
        # Handle None specially
        if annotation is None or annotation is type(None):
            return True
            
        # Check if it's in our known built-in types
        if annotation in self.BUILTIN_TYPES:
            return True
            
        # Check by module for built-in types
        module_name = getattr(annotation, "__module__", "")
        if module_name == "builtins":
            # Additional built-in types not in our explicit list
            name = getattr(annotation, "__name__", "")
            if name in ("object", "type", "property", "classmethod", "staticmethod"):
                return True
                
        return False

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format built-in type annotations.
        
        Args:
            annotation: The built-in type annotation to format
            recursion_depth: Current recursion depth (unused for built-ins)
            
        Returns:
            Formatted string representation
        """
        # Handle None specially
        if annotation is None or annotation is type(None):
            return "None"
            
        # Use our mapping for known types
        if annotation in self.BUILTIN_TYPES:
            return self.BUILTIN_TYPES[annotation]
            
        # Handle other built-in types by name
        module_name = getattr(annotation, "__module__", "")
        if module_name == "builtins":
            name = getattr(annotation, "__name__", "")
            if name:
                return name
                
        # Fallback (shouldn't happen if can_format is correct)
        return str(annotation)


def is_builtin_type(annotation: Any) -> bool:
    """Check if an annotation is a built-in type.
    
    Args:
        annotation: The annotation to check
        
    Returns:
        True if this is a built-in type
    """
    # Handle None specially
    if annotation is None or annotation is type(None):
        return True
        
    # Check if it's in our known built-in types
    builtin_types = {int, str, bool, float, bytes, list, dict, tuple, set, type(None)}
    if annotation in builtin_types:
        return True
        
    # Check by module for built-in types
    module_name = getattr(annotation, "__module__", "")
    return module_name == "builtins"
