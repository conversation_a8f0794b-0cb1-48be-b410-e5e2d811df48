"""Base formatter protocol and utilities for type annotation formatting.

This module provides the foundation for the modular type formatting system,
replacing the monolithic D-26 complexity TypingModuleFormatter with focused,
single-responsibility formatters.
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Protocol, TYPE_CHECKING

if TYPE_CHECKING:
    from plugginger.stubgen.formatters.manager import TypeFormatterManager

# Maximum recursion depth to prevent infinite loops in complex type hierarchies
MAX_RECURSION_DEPTH = 10


class TypeFormatterProtocol(Protocol):
    """Protocol defining the interface for type annotation formatters.
    
    Each formatter is responsible for handling a specific category of type
    annotations (e.g., built-ins, typing generics, user-defined classes).
    """

    def can_format(self, annotation: Any) -> bool:
        """Check if this formatter can handle the given type annotation.
        
        Args:
            annotation: The type annotation to check
            
        Returns:
            True if this formatter can handle the annotation, False otherwise
        """
        ...

    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format the type annotation into a string representation.
        
        Args:
            annotation: The type annotation to format
            recursion_depth: Current recursion depth to prevent infinite loops
            
        Returns:
            String representation suitable for .pyi stub files
            
        Raises:
            FormatterError: If formatting fails
        """
        ...


class BaseTypeFormatter(ABC):
    """Abstract base class for type annotation formatters.
    
    Provides common functionality and enforces the formatter interface.
    All concrete formatters should inherit from this class.
    """

    def __init__(self, manager: "TypeFormatterManager") -> None:
        """Initialize the formatter with a reference to the manager.
        
        Args:
            manager: The formatter manager for recursive formatting
        """
        self.manager = manager

    @abstractmethod
    def can_format(self, annotation: Any) -> bool:
        """Check if this formatter can handle the given type annotation."""
        ...

    @abstractmethod
    def format(self, annotation: Any, recursion_depth: int = 0) -> str:
        """Format the type annotation into a string representation."""
        ...

    def _check_recursion_depth(self, recursion_depth: int) -> None:
        """Check if recursion depth limit is exceeded.
        
        Args:
            recursion_depth: Current recursion depth
            
        Raises:
            FormatterError: If recursion depth exceeds limit
        """
        if recursion_depth > MAX_RECURSION_DEPTH:
            raise FormatterError(
                f"Max recursion depth ({MAX_RECURSION_DEPTH}) exceeded. "
                "This may indicate a circular type reference."
            )

    def _format_recursively(self, annotation: Any, recursion_depth: int) -> str:
        """Format an annotation recursively using the manager.
        
        Args:
            annotation: The type annotation to format
            recursion_depth: Current recursion depth
            
        Returns:
            Formatted string representation
        """
        return self.manager.format(annotation, recursion_depth + 1)


class FormatterError(Exception):
    """Exception raised when type formatting fails.
    
    This exception provides structured error information for debugging
    type formatting issues.
    """

    def __init__(self, message: str, annotation: Any = None, formatter: str | None = None) -> None:
        """Initialize the formatter error.
        
        Args:
            message: Error description
            annotation: The annotation that caused the error (optional)
            formatter: Name of the formatter that failed (optional)
        """
        super().__init__(message)
        self.annotation = annotation
        self.formatter = formatter

    def __str__(self) -> str:
        """Return a detailed error message."""
        parts = [super().__str__()]
        
        if self.formatter:
            parts.append(f"Formatter: {self.formatter}")
            
        if self.annotation is not None:
            parts.append(f"Annotation: {self.annotation!r}")
            
        return " | ".join(parts)


def is_empty_annotation(annotation: Any) -> bool:
    """Check if an annotation represents an empty/missing annotation.
    
    Args:
        annotation: The annotation to check
        
    Returns:
        True if the annotation is empty or missing
    """
    import inspect
    return annotation in (inspect.Parameter.empty, inspect.Signature.empty)


def get_annotation_module(annotation: Any) -> str:
    """Get the module name of a type annotation.
    
    Args:
        annotation: The type annotation
        
    Returns:
        Module name or empty string if not available
    """
    return getattr(annotation, "__module__", "")


def get_annotation_name(annotation: Any) -> str:
    """Get the name of a type annotation.
    
    Args:
        annotation: The type annotation
        
    Returns:
        Annotation name or string representation
    """
    # Try __qualname__ first for nested classes, then __name__
    if hasattr(annotation, "__qualname__"):
        return str(annotation.__qualname__)
    elif hasattr(annotation, "__name__"):
        return str(annotation.__name__)
    else:
        return str(annotation)
