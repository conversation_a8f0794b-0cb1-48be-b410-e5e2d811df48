# src/plugginger/cli/__init__.py

"""
Command-line interface for Plugginger framework.

This module provides CLI commands for project management, stub generation,
and application lifecycle operations.
"""

import argparse
import sys
from pathlib import Path

from plugginger.cli.cmd_core_freeze import cmd_core_freeze
from plugginger.cli.cmd_inspect import cmd_inspect
from plugginger.cli.cmd_new import cmd_new_plugin
from plugginger.cli.cmd_project_run import cmd_project_run
from plugginger.cli.cmd_schema import cmd_schema
from plugginger.cli.cmd_stubs_generate import cmd_stubs_generate


def main() -> None:
    """Main entry point for the Plugginger CLI."""
    parser = argparse.ArgumentParser(
        prog="plugginger",
        description="Plugginger Framework CLI Tools"
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # New command
    new_parser = subparsers.add_parser(
        "new",
        help="Create new Plugginger projects or plugins"
    )
    new_subparsers = new_parser.add_subparsers(dest="new_command", help="New commands")

    # New plugin command
    new_plugin_parser = new_subparsers.add_parser(
        "plugin",
        help="Create a new Plugginger plugin project"
    )
    new_plugin_parser.add_argument(
        "name",
        help="Name of the plugin to create"
    )
    new_plugin_parser.add_argument(
        "--output-dir",
        type=Path,
        default=Path.cwd(),
        help="Output directory for the new plugin (default: current directory)"
    )
    new_plugin_parser.add_argument(
        "--template",
        choices=["basic", "service", "event", "full"],
        default="basic",
        help="Template to use for plugin generation (default: basic)"
    )
    new_plugin_parser.add_argument(
        "--no-ai",
        action="store_true",
        help="Disable AI-powered code generation (use template-based generation)"
    )
    new_plugin_parser.add_argument(
        "--no-tests",
        action="store_true",
        help="Skip test file generation"
    )
    new_plugin_parser.add_argument(
        "--include-docs",
        action="store_true",
        help="Include documentation files"
    )

    # S4.5 - Intelligent Plugin Generation Options
    new_plugin_parser.add_argument(
        "--prompt",
        type=str,
        help="Natural language prompt for AI-powered plugin generation (e.g., 'Create an email service with authentication')"
    )
    new_plugin_parser.add_argument(
        "--context",
        type=Path,
        help="Path to existing application for context-aware plugin generation"
    )
    new_plugin_parser.add_argument(
        "--validate-wiring",
        action="store_true",
        help="Validate plugin wiring and integration compatibility"
    )
    new_plugin_parser.add_argument(
        "--suggest-integrations",
        action="store_true",
        help="Generate integration suggestions for better plugin compatibility"
    )
    new_plugin_parser.add_argument(
        "--quality-threshold",
        type=float,
        default=0.7,
        help="Minimum quality score for generated plugin (0.0-1.0, default: 0.7)"
    )
    new_plugin_parser.add_argument(
        "--max-retries",
        type=int,
        default=3,
        help="Maximum retry attempts for AI generation (default: 3)"
    )

    # Core freeze command
    freeze_parser = subparsers.add_parser(
        "freeze",
        help="Generate lockfile for reproducible builds"
    )
    freeze_parser.add_argument(
        "factory_path",
        help="Path to app factory function (module:function)"
    )
    freeze_parser.add_argument(
        "--output",
        type=Path,
        default=Path("plugginger.lock.json"),
        help="Output lockfile path (default: plugginger.lock.json)"
    )
    freeze_parser.add_argument(
        "--project-root",
        type=Path,
        default=Path.cwd(),
        help="Project root directory (default: current directory)"
    )

    # Stubs generate command
    stubs_parser = subparsers.add_parser(
        "stubs",
        help="Generate type stubs for plugin proxies"
    )
    stubs_parser.add_argument(
        "factory_path",
        help="Path to app factory function (module:function)"
    )
    stubs_parser.add_argument(
        "--output-dir",
        type=Path,
        help="Output directory for stubs (default: src/plugginger_stubs/)"
    )
    stubs_parser.add_argument(
        "--watch",
        action="store_true",
        help="Watch for changes and regenerate stubs"
    )

    # Project run command
    run_parser = subparsers.add_parser(
        "run",
        help="Run a Plugginger application"
    )
    run_parser.add_argument(
        "factory_path",
        help="Path to app factory function (module:function)"
    )
    run_parser.add_argument(
        "--config",
        type=Path,
        help="Path to configuration file"
    )

    # Inspect command
    inspect_parser = subparsers.add_parser(
        "inspect",
        help="Analyze and export application structure"
    )
    inspect_parser.add_argument(
        "factory_path",
        help="Path to app factory function (module:function)"
    )
    inspect_parser.add_argument(
        "--json",
        action="store_true",
        help="Output in JSON format (default)"
    )
    inspect_parser.add_argument(
        "--output",
        type=str,
        help="Output file path (default: stdout)"
    )

    # Schema command
    schema_parser = subparsers.add_parser(
        "schema",
        help="Export JSON schemas for Plugginger data formats"
    )
    schema_parser.add_argument(
        "schema_type",
        choices=["app-graph"],
        help="Type of schema to export"
    )
    schema_parser.add_argument(
        "--output",
        type=Path,
        help="Write schema to file instead of stdout"
    )

    # Parse arguments
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    try:
        if args.command == "freeze":
            cmd_core_freeze(args.factory_path, args.output, args.project_root)
        elif args.command == "stubs":
            cmd_stubs_generate(args.factory_path, args.output_dir, args.watch)
        elif args.command == "run":
            cmd_project_run(args.factory_path, args.config)
        elif args.command == "inspect":
            output_format = "json" if args.json or not hasattr(args, 'json') else "json"
            cmd_inspect(args.factory_path, output_format, args.output)
        elif args.command == "schema":
            cmd_schema(args.schema_type, args.output)
        elif args.command == "new":
            if args.new_command == "plugin":
                cmd_new_plugin(
                    plugin_name=args.name,
                    output_dir=args.output_dir,
                    template=args.template,
                    enable_ai=not args.no_ai,
                    include_tests=not args.no_tests,
                    include_docs=args.include_docs,
                    # S4.5 - Intelligent Plugin Generation Options
                    prompt=getattr(args, 'prompt', None),
                    context_path=getattr(args, 'context', None),
                    validate_wiring=getattr(args, 'validate_wiring', False),
                    suggest_integrations=getattr(args, 'suggest_integrations', False),
                    quality_threshold=getattr(args, 'quality_threshold', 0.7),
                    max_retries=getattr(args, 'max_retries', 3)
                )
            else:
                new_parser.print_help()
                sys.exit(1)
        else:
            parser.print_help()
            sys.exit(1)

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(130)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
