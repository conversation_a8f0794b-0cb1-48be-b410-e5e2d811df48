"""
CLI command for creating new Plugginger projects and plugins.

Updated to use the new Plugin-Generator Internal-Plugin with graceful fallback.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Literal

from plugginger.api.builder import PluggingerAppBuilder

ServiceTemplateType = Literal["basic", "service", "event", "full"]

logger = logging.getLogger(__name__)





def cmd_new_plugin(
    plugin_name: str,
    output_dir: Path,
    template: str = "basic",
    enable_ai: bool = True,
    include_tests: bool = True,
    include_docs: bool = False,
    # S4.5 - Intelligent Plugin Generation Options
    prompt: str | None = None,
    context_path: Path | None = None,
    validate_wiring: bool = False,
    suggest_integrations: bool = False,
    quality_threshold: float = 0.7,
    max_retries: int = 3
) -> None:
    """
    Creates a new Plugginger plugin project using the Plugin-Generator service.

    Args:
        plugin_name: The name of the plugin to create.
        output_dir: The directory where the plugin project will be created.
        template: Template to use (basic, service, event, full).
        enable_ai: Enable AI-powered code generation.
        include_tests: Include test files.
        include_docs: Include documentation files.
        prompt: Natural language prompt for AI-powered plugin generation.
        context_path: Path to existing application for context-aware generation.
        validate_wiring: Validate plugin wiring and integration compatibility.
        suggest_integrations: Generate integration suggestions.
        quality_threshold: Minimum quality score for generated plugin (0.0-1.0).
        max_retries: Maximum retry attempts for AI generation.
    """
    # S4.5 - Intelligent Plugin Generation
    if prompt:
        print(f"🤖 Creating intelligent plugin '{plugin_name}' from prompt...")
        print(f"💬 Prompt: {prompt}")

        # Use intelligent generation pipeline
        try:
            result = asyncio.run(_generate_intelligent_plugin(
                plugin_name=plugin_name,
                output_dir=output_dir,
                prompt=prompt,
                context_path=context_path,
                validate_wiring=validate_wiring,
                suggest_integrations=suggest_integrations,
                quality_threshold=quality_threshold,
                max_retries=max_retries,
                include_tests=include_tests,
                include_docs=include_docs
            ))

            if result.get("success", False):
                _print_intelligent_generation_success(result, plugin_name)
                return
            else:
                error_msg = result.get("error", "Unknown error")
                print(f"⚠️  Intelligent generation failed: {error_msg}")
                print("🔄 Falling back to standard generation...")

        except Exception as e:
            print(f"⚠️  Intelligent generation failed: {e}")
            print("🔄 Falling back to standard generation...")

    print(f"Creating plugin '{plugin_name}' using template '{template}'...")

    # Try service-based generation first
    try:
        result = asyncio.run(_generate_plugin_with_service(
            plugin_name=plugin_name,
            output_dir=output_dir,
            template=template,
            enable_ai=enable_ai,
            include_tests=include_tests,
            include_docs=include_docs
        ))

        if result.get("success", False):
            plugin_path = result.get("plugin_path", str(output_dir / plugin_name))
            print(f"✅ Successfully created plugin '{plugin_name}' at {plugin_path}")
            print(f"📁 Template: {template}")
            print(f"🤖 AI Generation: {'enabled' if enable_ai else 'disabled'}")
            print(f"🧪 Tests: {'included' if include_tests else 'not included'}")
            print(f"📚 Docs: {'included' if include_docs else 'not included'}")
            print("\nTo get started:")
            print(f"  cd {plugin_path}")
            print("  poetry install")
            print("  poetry run pytest")
            return
        else:
            error_msg = result.get("error", "Unknown error")
            print(f"⚠️  Service-based generation failed: {error_msg}")
            print("🔄 Falling back to template-based generation...")

    except Exception as e:
        print(f"⚠️  Service-based generation failed: {e}")
        print("🔄 Falling back to template-based generation...")

    # Fallback to template-based generation
    _generate_plugin_with_template(plugin_name, output_dir, template)


async def _generate_plugin_with_service(
    plugin_name: str,
    output_dir: Path,
    template: str,
    enable_ai: bool,
    include_tests: bool,
    include_docs: bool
) -> dict[str, Any]:
    """Generate plugin using the Plugin-Generator service.

    Args:
        plugin_name: Name of the plugin
        output_dir: Output directory
        template: Template to use
        enable_ai: Enable AI generation
        include_tests: Include tests
        include_docs: Include documentation

    Returns:
        Generation result
    """
    try:
        # Create minimal app with plugin-generator
        builder = PluggingerAppBuilder(app_name="cli_plugin_generator")

        # Load internal plugins (including plugin_generator and its dependencies)
        from plugginger.plugins.internal_loader import load_internal_plugins
        load_internal_plugins(["plugin_generator", "ai_orchestrator"], builder)

        # Load core plugins (for AI functionality)
        from plugginger.plugins.core_loader import load_core_plugins
        load_core_plugins(["llm_provider", "json_validator", "wiring_analyzer"], builder)

        # Build app
        app = builder.build()

        # Create plugin specification
        plugin_spec = {
            "name": plugin_name.replace('-', '_'),
            "version": "1.0.0",
            "description": f"Generated {plugin_name} plugin",
            "author": "Plugin Generator",
            "services": [
                {
                    "name": "hello",
                    "description": "Basic greeting service",
                    "timeout_seconds": 30.0
                }
            ],
            "dependencies": []
        }

        # Call plugin generator service
        result = await app.call_service("plugin_generator.generate_plugin", {
            "plugin_spec": plugin_spec,
            "output_directory": str(output_dir),
            "template": template,
            "enable_ai_generation": enable_ai,
            "include_tests": include_tests,
            "include_documentation": include_docs
        })

        return dict(result) if result else {"success": False, "error": "No result returned"}

    except Exception as e:
        logger.error(f"Service-based plugin generation failed: {e}")
        return {"success": False, "error": str(e)}


def _generate_plugin_with_template(plugin_name: str, output_dir: Path, template: str) -> None:
    """Generate plugin using fallback template-based approach.

    Args:
        plugin_name: Name of the plugin
        output_dir: Output directory
        template: Template to use
    """
    project_path = output_dir / plugin_name
    project_path.mkdir(parents=True, exist_ok=True)

    # Create __init__.py
    (project_path / "__init__.py").touch()

    # Create manifest.yaml with new format
    manifest_content = f"""name: {plugin_name.replace('-', '_')}
version: 1.0.0
description: Generated {plugin_name} plugin
author: Plugin Generator
license: MIT
plugin_type: user
execution_mode: thread
services:
  - name: hello
    description: Basic greeting service
    timeout_seconds: 30.0
metadata:
  tags:
    - generated
    - user-plugin
  category: user
  stability: experimental
  created_at: "2025-01-27T16:00:00Z"
  updated_at: "2025-01-27T16:00:00Z"
"""
    (project_path / "manifest.yaml").write_text(manifest_content.strip())

    # Create pyproject.toml
    pyproject_content = f"""[tool.poetry]
name = "plugginger-{plugin_name}"
version = "1.0.0"
description = "Generated {plugin_name} plugin"
authors = ["Plugin Generator <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.11,<4.0"
plugginger = ">=1.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-asyncio = "^1.0.0"
mypy = "^1.15.0"
ruff = "^0.11.11"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
pythonpath = "."

[tool.mypy]
strict = true

[tool.ruff]
line-length = 120
target-version = "py311"
"""
    (project_path / "pyproject.toml").write_text(pyproject_content.strip())

    # Create README.md
    readme_content = f"""# {plugin_name}

Generated {plugin_name} plugin using Plugginger Framework.

## Installation

```bash
poetry install
```

## Testing

```bash
poetry run pytest
```

## Usage

```python
from plugginger.api.builder import PluggingerAppBuilder

builder = PluggingerAppBuilder(app_name="my_app")
builder.discover_and_include_plugins(directory=".", pattern="manifest.yaml")
app = builder.build()

# Use the plugin service
result = await app.call_service("{plugin_name.replace('-', '_')}.hello")
print(result)
```
"""
    (project_path / "README.md").write_text(readme_content.strip())

    # Create plugin file with new PluginBase pattern
    plugin_file_content = f'''"""
{plugin_name.replace('-', '_').title().replace('_', '')}Plugin

Generated plugin implementation

AI_METADATA:
complexity: low
category: generated
tags: [generated, basic, plugin]

Examples:
    >>> from {plugin_name.replace('-', '_')} import {plugin_name.replace('-', '_').title().replace('_', '')}Plugin
    >>> plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin()
    >>> result = await plugin.hello("World")
    >>> assert result["message"] == "Hello, World!"
"""

import logging
from typing import Any

from plugginger.api import plugin, service, PluginBase

logger = logging.getLogger(__name__)


@plugin(name="{plugin_name.replace('-', '_')}", version="1.0.0")
class {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(PluginBase):
    """Generated {plugin_name} plugin

    AI_METADATA:
    complexity: low
    category: generated
    tags: [generated, basic, plugin]

    Examples:
        >>> plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin()
        >>> result = await plugin.hello("World")
        >>> assert result["message"] == "Hello, World!"
    """

    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize {plugin_name} plugin.

        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger

    async def setup(self, plugin_config: Any = None) -> None:
        """Setup the {plugin_name} plugin."""
        self.logger.info("{plugin_name.replace('-', '_').title().replace('_', '')}Plugin initializing")

    async def teardown(self) -> None:
        """Cleanup the {plugin_name} plugin."""
        self.logger.info("{plugin_name.replace('-', '_').title().replace('_', '')}Plugin shutting down")

    @service()
    async def hello(self, name: str = "World") -> dict[str, Any]:
        """Hello service

        AI_METADATA:
        complexity: low
        category: greeting
        tags: [hello, greeting, basic]

        Args:
            name: Name to greet

        Returns:
            Greeting message

        Examples:
            >>> plugin = TestBasicPlugin()
            >>> result = await plugin.hello("World")
            >>> assert result["message"] == "Hello, World!"
        """
        message = f"Hello, {{name}}!"
        self.logger.info(f"Generated greeting: {{message}}")

        return {{
            "message": message,
            "plugin": "{plugin_name.replace('-', '_')}",
            "timestamp": "2025-01-27T16:00:00Z"
        }}
'''
    (project_path / f"{plugin_name.replace('-', '_')}.py").write_text(plugin_file_content.strip())

    # Create tests directory and files
    tests_path = project_path / "tests"
    tests_path.mkdir(exist_ok=True)
    (tests_path / "__init__.py").touch()

    # Create test file
    test_content = f'''"""
Tests for {plugin_name.replace('-', '_').title().replace('_', '')}Plugin.
"""

import pytest
from unittest.mock import Mock

from {plugin_name.replace('-', '_')} import {plugin_name.replace('-', '_').title().replace('_', '')}Plugin


class Test{plugin_name.replace('-', '_').title().replace('_', '')}Plugin:
    """Tests for {plugin_name.replace('-', '_').title().replace('_', '')}Plugin class."""

    def test_initialization(self) -> None:
        """Test plugin initialization."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        assert plugin.app == mock_app
        assert plugin.logger is not None

    @pytest.mark.asyncio
    async def test_setup(self) -> None:
        """Test plugin setup."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        # Should not raise any exceptions
        await plugin.setup()

    @pytest.mark.asyncio
    async def test_teardown(self) -> None:
        """Test plugin teardown."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        # Should not raise any exceptions
        await plugin.teardown()

    @pytest.mark.asyncio
    async def test_hello_service(self) -> None:
        """Test hello service."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        result = await plugin.hello("Test")

        assert result["message"] == "Hello, Test!"
        assert result["plugin"] == "{plugin_name.replace('-', '_')}"
        assert "timestamp" in result

    @pytest.mark.asyncio
    async def test_hello_service_default_name(self) -> None:
        """Test hello service with default name."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        result = await plugin.hello()

        assert result["message"] == "Hello, World!"
        assert result["plugin"] == "{plugin_name.replace('-', '_')}"
'''
    (tests_path / f"test_{plugin_name.replace('-', '_')}.py").write_text(test_content.strip())

    print(f"✅ Successfully created plugin '{plugin_name}' at {project_path}")
    print(f"📁 Template: {template} (fallback)")
    print("🧪 Tests: included")
    print("\nTo get started:")
    print(f"  cd {project_path}")
    print("  poetry install")
    print("  poetry run pytest")


# S4.5 - Intelligent Plugin Generation Functions

async def _generate_intelligent_plugin(
    plugin_name: str,
    output_dir: Path,
    prompt: str,
    context_path: Path | None = None,
    validate_wiring: bool = False,
    suggest_integrations: bool = False,
    quality_threshold: float = 0.7,
    max_retries: int = 3,
    include_tests: bool = True,
    include_docs: bool = False
) -> dict[str, Any]:
    """Generate plugin using intelligent AI-powered pipeline.

    Args:
        plugin_name: Name of the plugin
        output_dir: Output directory
        prompt: Natural language prompt
        context_path: Path to existing application for context
        validate_wiring: Validate plugin wiring
        suggest_integrations: Generate integration suggestions
        quality_threshold: Minimum quality score
        max_retries: Maximum retry attempts
        include_tests: Include tests
        include_docs: Include documentation

    Returns:
        Generation result
    """
    try:
        # Create minimal app with intelligent plugin generator
        builder = PluggingerAppBuilder(app_name="intelligent_plugin_generator")

        # Load internal plugins (including plugin_generator and its dependencies)
        from plugginger.plugins.internal_loader import load_internal_plugins
        load_internal_plugins(["plugin_generator", "ai_orchestrator"], builder)

        # Load core plugins (for AI functionality)
        from plugginger.plugins.core_loader import load_core_plugins
        load_core_plugins(["llm_provider", "json_validator", "wiring_analyzer"], builder)

        # Build app
        app = builder.build()

        # Step 1: Process user prompt
        print("🔍 Processing user prompt...")
        app_context = await _extract_app_context(context_path) if context_path else {}

        prompt_result = await app.call_service("plugin_generator.process_user_prompt", {
            "user_prompt": prompt,
            "app_context": app_context,
            "generation_options": {
                "include_tests": include_tests,
                "include_docs": include_docs,
                "quality_threshold": quality_threshold
            }
        })

        if not prompt_result.get("success", False):
            return {"success": False, "error": "Prompt processing failed"}

        print(f"✅ Prompt processed - Plugin type: {prompt_result.get('prompt_analysis', {}).get('plugin_type', 'unknown')}")

        # Step 2: Generate plugin specification with retries
        print("📋 Generating plugin specification...")

        for attempt in range(max_retries):
            try:
                # Simulate LLM call (in real implementation, this would call actual LLM)
                llm_output = await _simulate_llm_generation(prompt_result, plugin_name)

                spec_result = await app.call_service("plugin_generator.generate_plugin_spec", {
                    "llm_output": llm_output,
                    "prompt_context": prompt_result.get("enhanced_context", {}),
                    "validation_options": {"strict_validation": True}
                })

                if spec_result.get("success", False):
                    plugin_spec = spec_result["plugin_spec"]
                    print(f"✅ Plugin specification generated (attempt {attempt + 1})")
                    break
            except Exception as e:
                print(f"⚠️  Attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    return {"success": False, "error": f"Specification generation failed after {max_retries} attempts"}
        else:
            return {"success": False, "error": "Failed to generate valid specification"}

        # Step 3: Analyze integration opportunities (if requested)
        integration_analysis = None
        if suggest_integrations:
            print("🔗 Analyzing integration opportunities...")
            integration_result = await app.call_service("plugin_generator.analyze_integration_opportunities", {
                "plugin_spec": plugin_spec,
                "app_context": app_context,
                "analysis_options": {"confidence_threshold": 0.7}
            })

            if integration_result.get("success", False):
                integration_analysis = integration_result
                print(f"✅ Found {len(integration_result.get('integration_suggestions', []))} integration suggestions")

        # Step 4: Generate plugin code
        print("💻 Generating plugin code...")
        code_result = await app.call_service("plugin_generator.generate_plugin_code_advanced", {
            "plugin_spec": plugin_spec,
            "generation_options": {
                "include_docstrings": True,
                "include_tests": include_tests,
                "include_docs": include_docs,
                "code_style": "pep8"
            }
        })

        if not code_result.get("success", False):
            return {"success": False, "error": "Code generation failed"}

        print("✅ Plugin code generated")

        # Step 5: Assess quality
        print("🎯 Assessing plugin quality...")
        quality_result = await app.call_service("plugin_generator.assess_plugin_quality", {
            "plugin_spec": plugin_spec,
            "generated_code": code_result["plugin_code"],
            "test_code": code_result["test_code"],
            "manifest_code": code_result["manifest_code"],
            "assessment_options": {"strict_mode": True}
        })

        if not quality_result.get("success", False):
            return {"success": False, "error": "Quality assessment failed"}

        quality_score = quality_result.get("overall_score", 0.0)
        quality_grade = quality_result.get("quality_grade", "F")

        print(f"✅ Quality assessment complete - Score: {quality_score:.2f} (Grade: {quality_grade})")

        # Check quality threshold
        if quality_score < quality_threshold:
            print(f"⚠️  Quality score {quality_score:.2f} below threshold {quality_threshold}")
            recommendations = quality_result.get("recommendations", [])
            if recommendations:
                print("💡 Improvement recommendations:")
                for rec in recommendations[:3]:  # Show top 3
                    print(f"   - {rec.get('recommendation', 'Unknown')}")

        # Step 6: Validate wiring (if requested)
        wiring_validation = None
        if validate_wiring:
            print("🔧 Validating plugin wiring...")
            # Use integration analysis for wiring validation
            if integration_analysis:
                wiring_validation = {
                    "valid": True,
                    "issues": [],
                    "suggestions": integration_analysis.get("integration_suggestions", [])
                }
                print("✅ Wiring validation complete")

        # Step 7: Write files to disk
        print("💾 Writing plugin files...")
        plugin_path = output_dir / plugin_name.replace('-', '_')
        plugin_path.mkdir(parents=True, exist_ok=True)

        # Write main plugin file
        (plugin_path / f"{plugin_name.replace('-', '_')}.py").write_text(
            code_result["plugin_code"], encoding="utf-8"
        )

        # Write test file
        if include_tests and code_result.get("test_code"):
            (plugin_path / f"test_{plugin_name.replace('-', '_')}.py").write_text(
                code_result["test_code"], encoding="utf-8"
            )

        # Write manifest
        (plugin_path / "manifest.yaml").write_text(
            code_result["manifest_code"], encoding="utf-8"
        )

        # Write additional files
        additional_files = code_result.get("additional_files", {})
        for filename, content in additional_files.items():
            (plugin_path / filename).write_text(content, encoding="utf-8")

        print(f"✅ Plugin files written to {plugin_path}")

        return {
            "success": True,
            "plugin_path": str(plugin_path),
            "plugin_spec": plugin_spec,
            "quality_assessment": quality_result,
            "integration_analysis": integration_analysis,
            "wiring_validation": wiring_validation,
            "generation_metadata": {
                "prompt": prompt,
                "quality_score": quality_score,
                "quality_grade": quality_grade,
                "generation_method": "intelligent_ai_pipeline"
            }
        }

    except Exception as e:
        return {"success": False, "error": str(e)}


async def _extract_app_context(context_path: Path) -> dict[str, Any]:
    """Extract application context from existing app.

    Args:
        context_path: Path to existing application

    Returns:
        Application context information
    """
    try:
        # Try to find manifest files or plugin structure
        context: dict[str, Any] = {
            "available_services": [],
            "available_events": [],
            "available_dependencies": [],
            "app_structure": {}
        }

        if context_path.is_file() and context_path.name == "manifest.yaml":
            # Single manifest file
            import yaml
            with open(context_path, encoding='utf-8') as f:
                manifest = yaml.safe_load(f)

            context["available_services"] = [
                service.get("name", "") for service in manifest.get("services", [])
            ]
            context["available_dependencies"] = manifest.get("dependencies", [])

        elif context_path.is_dir():
            # Directory with potential plugins
            manifest_files = list(context_path.rglob("manifest.yaml"))

            for manifest_file in manifest_files:
                try:
                    import yaml
                    with open(manifest_file, encoding='utf-8') as f:
                        manifest = yaml.safe_load(f)

                    # Collect services
                    for service in manifest.get("services", []):
                        service_name = service.get("name", "")
                        if service_name:
                            context["available_services"].append(service_name)

                    # Collect dependencies
                    for dep in manifest.get("dependencies", []):
                        if isinstance(dep, str):
                            context["available_dependencies"].append(dep)
                        elif isinstance(dep, dict):
                            dep_name = dep.get("name", "")
                            if dep_name:
                                context["available_dependencies"].append(dep_name)

                except Exception:
                    continue  # Skip invalid manifests

        # Add common events
        context["available_events"] = [
            "app.startup", "app.shutdown", "app.ready",
            "user.login", "user.logout", "user.created",
            "service.called", "service.completed", "service.failed"
        ]

        return context

    except Exception:
        return {
            "available_services": [],
            "available_events": [],
            "available_dependencies": [],
            "app_structure": {}
        }


async def _simulate_llm_generation(prompt_result: dict[str, Any], plugin_name: str) -> str:
    """Simulate LLM generation for plugin specification.

    Args:
        prompt_result: Processed prompt result
        plugin_name: Name of the plugin

    Returns:
        Simulated LLM output as JSON string
    """
    # In real implementation, this would call actual LLM
    # For now, generate a reasonable plugin spec based on prompt analysis

    prompt_analysis = prompt_result.get("prompt_analysis", {})
    plugin_type = prompt_analysis.get("plugin_type", "service")
    service_requirements = prompt_analysis.get("service_requirements", ["hello"])

    # Generate services based on requirements
    services = []
    for req in service_requirements[:3]:  # Limit to 3 services
        services.append({
            "name": req.lower().replace(" ", "_"),
            "description": f"{req.title()} service",
            "timeout_seconds": 30.0
        })

    # If no services detected, add a default one
    if not services:
        services.append({
            "name": "process",
            "description": "Main processing service",
            "timeout_seconds": 30.0
        })

    # Generate basic plugin specification
    spec: dict[str, Any] = {
        "name": plugin_name.replace('-', '_'),
        "description": f"Generated {plugin_name} plugin",
        "class_name": f"{plugin_name.replace('-', '_').title().replace('_', '')}Plugin",
        "version": "1.0.0",
        "services": services,
        "event_listeners": [],
        "dependencies": ["logger"]
    }

    # Add type-specific enhancements
    if plugin_type == "auth":
        spec["dependencies"].extend(["config", "security"])
        spec["services"].append({
            "name": "authenticate",
            "description": "Authentication service",
            "timeout_seconds": 10.0
        })
    elif plugin_type == "storage":
        spec["dependencies"].extend(["config"])
        spec["services"].extend([
            {
                "name": "store",
                "description": "Store data",
                "timeout_seconds": 30.0
            },
            {
                "name": "retrieve",
                "description": "Retrieve data",
                "timeout_seconds": 30.0
            }
        ])

    import json
    return json.dumps(spec, indent=2)


def _print_intelligent_generation_success(result: dict[str, Any], plugin_name: str) -> None:
    """Print success message for intelligent plugin generation.

    Args:
        result: Generation result
        plugin_name: Name of the plugin
    """
    plugin_path = result.get("plugin_path", "")
    metadata = result.get("generation_metadata", {})
    quality_assessment = result.get("quality_assessment", {})
    integration_analysis = result.get("integration_analysis", {})

    print(f"\n🎉 Successfully created intelligent plugin '{plugin_name}'!")
    print(f"📁 Location: {plugin_path}")
    print(f"💬 Generated from prompt: {metadata.get('prompt', 'N/A')}")

    # Quality information
    quality_score = metadata.get("quality_score", 0.0)
    quality_grade = metadata.get("quality_grade", "F")
    print(f"🎯 Quality Score: {quality_score:.2f} (Grade: {quality_grade})")

    # Integration suggestions
    if integration_analysis:
        suggestions = integration_analysis.get("integration_suggestions", [])
        if suggestions:
            print(f"🔗 Integration Suggestions: {len(suggestions)} found")
            for i, suggestion in enumerate(suggestions[:3], 1):
                print(f"   {i}. {suggestion.get('description', 'Unknown suggestion')}")

    # Quality recommendations
    recommendations = quality_assessment.get("recommendations", [])
    if recommendations:
        print("💡 Quality Recommendations:")
        for i, rec in enumerate(recommendations[:3], 1):
            print(f"   {i}. {rec.get('recommendation', 'Unknown recommendation')}")

    print("\n🚀 To get started:")
    print(f"   cd {plugin_path}")
    print("   poetry install")
    print("   poetry run pytest")

    print(f"\n✨ Generated using: {metadata.get('generation_method', 'unknown')} method")
