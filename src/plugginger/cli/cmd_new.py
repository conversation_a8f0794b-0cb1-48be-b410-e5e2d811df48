"""
CLI command for creating new Plugginger projects and plugins.

Updated to use the new Plugin-Generator Internal-Plugin with graceful fallback.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Literal

from plugginger.api.builder import PluggingerAppBuilder

ServiceTemplateType = Literal["basic", "service", "event", "full"]

logger = logging.getLogger(__name__)





def cmd_new_plugin(
    plugin_name: str,
    output_dir: Path,
    template: str = "basic",
    enable_ai: bool = True,
    include_tests: bool = True,
    include_docs: bool = False
) -> None:
    """
    Creates a new Plugginger plugin project using the Plugin-Generator service.

    Args:
        plugin_name: The name of the plugin to create.
        output_dir: The directory where the plugin project will be created.
        template: Template to use (basic, service, event, full).
        enable_ai: Enable AI-powered code generation.
        include_tests: Include test files.
        include_docs: Include documentation files.
    """
    print(f"Creating plugin '{plugin_name}' using template '{template}'...")

    # Try service-based generation first
    try:
        result = asyncio.run(_generate_plugin_with_service(
            plugin_name=plugin_name,
            output_dir=output_dir,
            template=template,
            enable_ai=enable_ai,
            include_tests=include_tests,
            include_docs=include_docs
        ))

        if result.get("success", False):
            plugin_path = result.get("plugin_path", str(output_dir / plugin_name))
            print(f"✅ Successfully created plugin '{plugin_name}' at {plugin_path}")
            print(f"📁 Template: {template}")
            print(f"🤖 AI Generation: {'enabled' if enable_ai else 'disabled'}")
            print(f"🧪 Tests: {'included' if include_tests else 'not included'}")
            print(f"📚 Docs: {'included' if include_docs else 'not included'}")
            print("\nTo get started:")
            print(f"  cd {plugin_path}")
            print("  poetry install")
            print("  poetry run pytest")
            return
        else:
            error_msg = result.get("error", "Unknown error")
            print(f"⚠️  Service-based generation failed: {error_msg}")
            print("🔄 Falling back to template-based generation...")

    except Exception as e:
        print(f"⚠️  Service-based generation failed: {e}")
        print("🔄 Falling back to template-based generation...")

    # Fallback to template-based generation
    _generate_plugin_with_template(plugin_name, output_dir, template)


async def _generate_plugin_with_service(
    plugin_name: str,
    output_dir: Path,
    template: str,
    enable_ai: bool,
    include_tests: bool,
    include_docs: bool
) -> dict[str, Any]:
    """Generate plugin using the Plugin-Generator service.

    Args:
        plugin_name: Name of the plugin
        output_dir: Output directory
        template: Template to use
        enable_ai: Enable AI generation
        include_tests: Include tests
        include_docs: Include documentation

    Returns:
        Generation result
    """
    try:
        # Create minimal app with plugin-generator
        builder = PluggingerAppBuilder(app_name="cli_plugin_generator")

        # Load internal plugins (including plugin_generator)
        from plugginger.plugins.internal_loader import load_internal_plugins
        load_internal_plugins(["plugin_generator"], builder)

        # Build app
        app = builder.build()

        # Create plugin specification
        plugin_spec = {
            "name": plugin_name.replace('-', '_'),
            "version": "1.0.0",
            "description": f"Generated {plugin_name} plugin",
            "author": "Plugin Generator",
            "services": [
                {
                    "name": "hello",
                    "description": "Basic greeting service",
                    "timeout_seconds": 30.0
                }
            ],
            "dependencies": []
        }

        # Call plugin generator service
        result = await app.call_service("plugin_generator.generate_plugin", {
            "plugin_spec": plugin_spec,
            "output_directory": str(output_dir),
            "template": template,
            "enable_ai_generation": enable_ai,
            "include_tests": include_tests,
            "include_documentation": include_docs
        })

        return dict(result) if result else {"success": False, "error": "No result returned"}

    except Exception as e:
        logger.error(f"Service-based plugin generation failed: {e}")
        return {"success": False, "error": str(e)}


def _generate_plugin_with_template(plugin_name: str, output_dir: Path, template: str) -> None:
    """Generate plugin using fallback template-based approach.

    Args:
        plugin_name: Name of the plugin
        output_dir: Output directory
        template: Template to use
    """
    project_path = output_dir / plugin_name
    project_path.mkdir(parents=True, exist_ok=True)

    # Create __init__.py
    (project_path / "__init__.py").touch()

    # Create manifest.yaml with new format
    manifest_content = f"""name: {plugin_name.replace('-', '_')}
version: 1.0.0
description: Generated {plugin_name} plugin
author: Plugin Generator
license: MIT
plugin_type: user
execution_mode: thread
services:
  - name: hello
    description: Basic greeting service
    timeout_seconds: 30.0
metadata:
  tags:
    - generated
    - user-plugin
  category: user
  stability: experimental
  created_at: "2025-01-27T16:00:00Z"
  updated_at: "2025-01-27T16:00:00Z"
"""
    (project_path / "manifest.yaml").write_text(manifest_content.strip())

    # Create pyproject.toml
    pyproject_content = f"""[tool.poetry]
name = "plugginger-{plugin_name}"
version = "1.0.0"
description = "Generated {plugin_name} plugin"
authors = ["Plugin Generator <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.11,<4.0"
plugginger = ">=1.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-asyncio = "^1.0.0"
mypy = "^1.15.0"
ruff = "^0.11.11"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
pythonpath = "."

[tool.mypy]
strict = true

[tool.ruff]
line-length = 120
target-version = "py311"
"""
    (project_path / "pyproject.toml").write_text(pyproject_content.strip())

    # Create README.md
    readme_content = f"""# {plugin_name}

Generated {plugin_name} plugin using Plugginger Framework.

## Installation

```bash
poetry install
```

## Testing

```bash
poetry run pytest
```

## Usage

```python
from plugginger.api.builder import PluggingerAppBuilder

builder = PluggingerAppBuilder(app_name="my_app")
builder.discover_and_include_plugins(directory=".", pattern="manifest.yaml")
app = builder.build()

# Use the plugin service
result = await app.call_service("{plugin_name.replace('-', '_')}.hello")
print(result)
```
"""
    (project_path / "README.md").write_text(readme_content.strip())

    # Create plugin file with new PluginBase pattern
    plugin_file_content = f'''"""
{plugin_name.replace('-', '_').title().replace('_', '')}Plugin.

Generated plugin implementation.
"""

import logging
from typing import Any

from plugginger.api.plugin import PluginBase
from plugginger.api.service import service

logger = logging.getLogger(__name__)


class {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(PluginBase):
    """Generated {plugin_name} plugin."""

    def __init__(self, **injected_dependencies: Any) -> None:
        """Initialize {plugin_name} plugin.

        Args:
            **injected_dependencies: Injected dependencies from DI container
        """
        super().__init__(**injected_dependencies)
        self.logger = logger

    async def setup(self, plugin_config: Any = None) -> None:
        """Setup the {plugin_name} plugin."""
        self.logger.info("{plugin_name.replace('-', '_').title().replace('_', '')}Plugin initializing")

    async def teardown(self) -> None:
        """Cleanup the {plugin_name} plugin."""
        self.logger.info("{plugin_name.replace('-', '_').title().replace('_', '')}Plugin shutting down")

    @service(name="hello")
    async def hello(self, name: str = "World") -> dict[str, Any]:
        """Say hello.

        Args:
            name: Name to greet

        Returns:
            Greeting message
        """
        message = f"Hello, {{name}}!"
        self.logger.info(f"Generated greeting: {{message}}")

        return {{
            "message": message,
            "plugin": "{plugin_name.replace('-', '_')}",
            "timestamp": "2025-01-27T16:00:00Z"
        }}
'''
    (project_path / f"{plugin_name.replace('-', '_')}_plugin.py").write_text(plugin_file_content.strip())

    # Create tests directory and files
    tests_path = project_path / "tests"
    tests_path.mkdir(exist_ok=True)
    (tests_path / "__init__.py").touch()

    # Create test file
    test_content = f'''"""
Tests for {plugin_name.replace('-', '_').title().replace('_', '')}Plugin.
"""

import pytest
from unittest.mock import Mock

from {plugin_name.replace('-', '_')}_plugin import {plugin_name.replace('-', '_').title().replace('_', '')}Plugin


class Test{plugin_name.replace('-', '_').title().replace('_', '')}Plugin:
    """Tests for {plugin_name.replace('-', '_').title().replace('_', '')}Plugin class."""

    def test_initialization(self) -> None:
        """Test plugin initialization."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        assert plugin.app == mock_app
        assert plugin.logger is not None

    @pytest.mark.asyncio
    async def test_setup(self) -> None:
        """Test plugin setup."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        # Should not raise any exceptions
        await plugin.setup()

    @pytest.mark.asyncio
    async def test_teardown(self) -> None:
        """Test plugin teardown."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        # Should not raise any exceptions
        await plugin.teardown()

    @pytest.mark.asyncio
    async def test_hello_service(self) -> None:
        """Test hello service."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        result = await plugin.hello("Test")

        assert result["message"] == "Hello, Test!"
        assert result["plugin"] == "{plugin_name.replace('-', '_')}"
        assert "timestamp" in result

    @pytest.mark.asyncio
    async def test_hello_service_default_name(self) -> None:
        """Test hello service with default name."""
        mock_app = Mock()
        plugin = {plugin_name.replace('-', '_').title().replace('_', '')}Plugin(app=mock_app)

        result = await plugin.hello()

        assert result["message"] == "Hello, World!"
        assert result["plugin"] == "{plugin_name.replace('-', '_')}"
'''
    (tests_path / f"test_{plugin_name.replace('-', '_')}_plugin.py").write_text(test_content.strip())

    print(f"✅ Successfully created plugin '{plugin_name}' at {project_path}")
    print(f"📁 Template: {template} (fallback)")
    print("🧪 Tests: included")
    print("\nTo get started:")
    print(f"  cd {project_path}")
    print("  poetry install")
    print("  poetry run pytest")
