<?xml version="1.0" ?>
<coverage version="7.8.2" timestamp="1749148862293" lines-valid="3759" lines-covered="1689" line-rate="0.4493" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.8.2 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>/home/<USER>/Python/plugginger/src/plugginger</source>
	</sources>
	<packages>
		<package name="." line-rate="0.3333" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="__init__.py" complexity="0" line-rate="0.3333" branch-rate="0">
					<methods/>
					<lines>
						<line number="57" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="63" hits="1"/>
						<line number="67" hits="1"/>
						<line number="111" hits="1"/>
						<line number="114" hits="1"/>
						<line number="131" hits="0"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="137" hits="0"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="148" hits="0"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="153" hits="0"/>
						<line number="156" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="_internal" line-rate="0.5829" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="_internal/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="14" hits="1"/>
					</lines>
				</class>
				<class name="graph.py" filename="_internal/graph.py" complexity="0" line-rate="0.7222" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="43" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="58" hits="1"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="80" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="0"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="114" hits="1"/>
						<line number="121" hits="1"/>
						<line number="123" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="144" hits="1"/>
						<line number="145" hits="1"/>
						<line number="147" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="153" hits="1"/>
						<line number="154" hits="1"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0"/>
						<line number="157" hits="0"/>
						<line number="160" hits="1"/>
						<line number="161" hits="0"/>
						<line number="164" hits="1"/>
						<line number="166" hits="0"/>
						<line number="167" hits="0"/>
						<line number="171" hits="1"/>
						<line number="173" hits="1"/>
						<line number="175" hits="1"/>
						<line number="177" hits="1"/>
						<line number="179" hits="0"/>
					</lines>
				</class>
				<class name="proxy.py" filename="_internal/proxy.py" complexity="0" line-rate="0.2143" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="0"/>
						<line number="20" hits="1"/>
						<line number="37" hits="1"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="57" hits="1"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="70" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="89" hits="0"/>
						<line number="91" hits="1"/>
						<line number="104" hits="0"/>
						<line number="106" hits="0"/>
						<line number="109" hits="0"/>
						<line number="111" hits="0"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="116" hits="0"/>
						<line number="118" hits="0"/>
						<line number="120" hits="1"/>
						<line number="139" hits="0"/>
						<line number="140" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="146" hits="0"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="151" hits="0"/>
						<line number="154" hits="0"/>
						<line number="156" hits="1"/>
						<line number="167" hits="0"/>
						<line number="169" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="175" hits="1"/>
						<line number="190" hits="0"/>
						<line number="191" hits="0"/>
						<line number="208" hits="1"/>
						<line number="215" hits="0"/>
						<line number="216" hits="0"/>
						<line number="217" hits="0"/>
					</lines>
				</class>
				<class name="runtime_facade.py" filename="_internal/runtime_facade.py" complexity="0" line-rate="0.71" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="45" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="76" hits="1"/>
						<line number="78" hits="1"/>
						<line number="80" hits="1"/>
						<line number="82" hits="1"/>
						<line number="84" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="1"/>
						<line number="89" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="94" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="99" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="0"/>
						<line number="117" hits="1"/>
						<line number="119" hits="1"/>
						<line number="135" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="143" hits="1"/>
						<line number="148" hits="1"/>
						<line number="151" hits="1"/>
						<line number="153" hits="0"/>
						<line number="155" hits="1"/>
						<line number="157" hits="0"/>
						<line number="159" hits="1"/>
						<line number="161" hits="0"/>
						<line number="163" hits="1"/>
						<line number="165" hits="1"/>
						<line number="167" hits="1"/>
						<line number="169" hits="0"/>
						<line number="172" hits="1"/>
						<line number="174" hits="0"/>
						<line number="176" hits="1"/>
						<line number="178" hits="0"/>
						<line number="180" hits="1"/>
						<line number="182" hits="0"/>
						<line number="184" hits="1"/>
						<line number="186" hits="0"/>
						<line number="189" hits="1"/>
						<line number="191" hits="0"/>
						<line number="193" hits="1"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="200" hits="1"/>
						<line number="202" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="206" hits="1"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="212" hits="1"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="216" hits="0"/>
						<line number="218" hits="1"/>
						<line number="228" hits="0"/>
						<line number="230" hits="1"/>
						<line number="240" hits="0"/>
						<line number="242" hits="1"/>
						<line number="252" hits="0"/>
						<line number="255" hits="1"/>
						<line number="262" hits="0"/>
						<line number="265" hits="0"/>
						<line number="268" hits="0"/>
						<line number="270" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="_internal.builder_phases" line-rate="0.5973" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="_internal/builder_phases/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
					</lines>
				</class>
				<class name="app_config_resolver.py" filename="_internal/builder_phases/app_config_resolver.py" complexity="0" line-rate="0.5185" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="30" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="80" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="93" hits="0"/>
					</lines>
				</class>
				<class name="dependency_orchestrator.py" filename="_internal/builder_phases/dependency_orchestrator.py" complexity="0" line-rate="0.7895" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="0"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="44" hits="1"/>
						<line number="51" hits="1"/>
						<line number="53" hits="1"/>
						<line number="70" hits="1"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="83" hits="0"/>
						<line number="86" hits="0"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="94" hits="1"/>
						<line number="124" hits="1"/>
						<line number="129" hits="1"/>
						<line number="131" hits="1"/>
						<line number="134" hits="1"/>
						<line number="137" hits="1"/>
						<line number="139" hits="1"/>
						<line number="140" hits="1"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
					</lines>
				</class>
				<class name="interface_registrar.py" filename="_internal/builder_phases/interface_registrar.py" complexity="0" line-rate="0.4512" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="0"/>
						<line number="22" hits="0"/>
						<line number="25" hits="1"/>
						<line number="28" hits="1"/>
						<line number="37" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="62" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="0"/>
						<line number="81" hits="1"/>
						<line number="83" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="105" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="0"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="0"/>
						<line number="135" hits="1"/>
						<line number="145" hits="0"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="151" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="156" hits="0"/>
						<line number="159" hits="0"/>
						<line number="164" hits="1"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0"/>
						<line number="181" hits="1"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="199" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="206" hits="0"/>
						<line number="207" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0"/>
						<line number="220" hits="0"/>
						<line number="224" hits="0"/>
						<line number="226" hits="1"/>
						<line number="234" hits="0"/>
						<line number="237" hits="0"/>
						<line number="239" hits="0"/>
						<line number="240" hits="0"/>
						<line number="247" hits="0"/>
						<line number="249" hits="0"/>
						<line number="254" hits="1"/>
						<line number="261" hits="0"/>
						<line number="264" hits="0"/>
						<line number="265" hits="0"/>
						<line number="266" hits="0"/>
						<line number="270" hits="0"/>
						<line number="274" hits="0"/>
						<line number="277" hits="0"/>
						<line number="279" hits="0"/>
						<line number="284" hits="0"/>
						<line number="286" hits="0"/>
					</lines>
				</class>
				<class name="plugin_instantiator.py" filename="_internal/builder_phases/plugin_instantiator.py" complexity="0" line-rate="0.6618" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
						<line number="40" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="81" hits="1"/>
						<line number="82" hits="1"/>
						<line number="83" hits="1"/>
						<line number="86" hits="1"/>
						<line number="95" hits="1"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="107" hits="1"/>
						<line number="114" hits="1"/>
						<line number="116" hits="1"/>
						<line number="118" hits="1"/>
						<line number="120" hits="1"/>
						<line number="129" hits="1"/>
						<line number="131" hits="1"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="136" hits="0"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="148" hits="1"/>
						<line number="150" hits="1"/>
						<line number="158" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="0"/>
						<line number="163" hits="0"/>
						<line number="168" hits="0"/>
						<line number="170" hits="1"/>
						<line number="179" hits="1"/>
						<line number="182" hits="1"/>
						<line number="184" hits="1"/>
						<line number="185" hits="0"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0"/>
						<line number="188" hits="0"/>
						<line number="189" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="1"/>
						<line number="197" hits="0"/>
						<line number="202" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="205" hits="0"/>
						<line number="207" hits="1"/>
						<line number="208" hits="0"/>
						<line number="210" hits="1"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="_internal.runtime" line-rate="0.3508" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="_internal/runtime/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
					</lines>
				</class>
				<class name="dispatcher.py" filename="_internal/runtime/dispatcher.py" complexity="0" line-rate="0.3048" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="1"/>
						<line number="25" hits="1"/>
						<line number="36" hits="1"/>
						<line number="39" hits="1"/>
						<line number="51" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="61" hits="1"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="77" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="86" hits="0"/>
						<line number="87" hits="0"/>
						<line number="89" hits="1"/>
						<line number="96" hits="0"/>
						<line number="98" hits="1"/>
						<line number="105" hits="0"/>
						<line number="107" hits="1"/>
						<line number="116" hits="0"/>
						<line number="119" hits="1"/>
						<line number="133" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="152" hits="1"/>
						<line number="169" hits="0"/>
						<line number="171" hits="0"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="177" hits="0"/>
						<line number="180" hits="0"/>
						<line number="183" hits="0"/>
						<line number="184" hits="0"/>
						<line number="188" hits="0"/>
						<line number="189" hits="0"/>
						<line number="191" hits="0"/>
						<line number="193" hits="0"/>
						<line number="195" hits="1"/>
						<line number="205" hits="0"/>
						<line number="206" hits="0"/>
						<line number="207" hits="0"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="211" hits="1"/>
						<line number="227" hits="0"/>
						<line number="229" hits="0"/>
						<line number="232" hits="0"/>
						<line number="233" hits="0"/>
						<line number="235" hits="0"/>
						<line number="238" hits="0"/>
						<line number="240" hits="0"/>
						<line number="241" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="245" hits="0"/>
						<line number="246" hits="0"/>
						<line number="249" hits="0"/>
						<line number="250" hits="0"/>
						<line number="252" hits="1"/>
						<line number="259" hits="0"/>
						<line number="261" hits="0"/>
						<line number="262" hits="0"/>
						<line number="265" hits="0"/>
						<line number="266" hits="0"/>
						<line number="267" hits="0"/>
						<line number="268" hits="0"/>
						<line number="271" hits="0"/>
						<line number="273" hits="0"/>
						<line number="275" hits="1"/>
						<line number="282" hits="0"/>
						<line number="284" hits="1"/>
						<line number="291" hits="0"/>
						<line number="292" hits="0"/>
						<line number="293" hits="0"/>
						<line number="294" hits="0"/>
						<line number="296" hits="1"/>
						<line number="298" hits="0"/>
						<line number="301" hits="1"/>
						<line number="314" hits="1"/>
						<line number="321" hits="1"/>
						<line number="322" hits="1"/>
						<line number="324" hits="1"/>
						<line number="338" hits="0"/>
						<line number="339" hits="0"/>
						<line number="340" hits="0"/>
						<line number="341" hits="0"/>
						<line number="344" hits="0"/>
						<line number="345" hits="0"/>
						<line number="347" hits="1"/>
						<line number="363" hits="0"/>
						<line number="366" hits="0"/>
						<line number="367" hits="0"/>
						<line number="368" hits="0"/>
						<line number="369" hits="0"/>
						<line number="370" hits="0"/>
						<line number="371" hits="0"/>
						<line number="374" hits="0"/>
						<line number="375" hits="0"/>
						<line number="376" hits="0"/>
						<line number="377" hits="0"/>
						<line number="378" hits="0"/>
						<line number="379" hits="0"/>
						<line number="380" hits="0"/>
						<line number="381" hits="0"/>
						<line number="385" hits="1"/>
						<line number="395" hits="0"/>
						<line number="397" hits="1"/>
						<line number="404" hits="1"/>
						<line number="406" hits="1"/>
						<line number="416" hits="0"/>
						<line number="417" hits="0"/>
						<line number="418" hits="0"/>
						<line number="419" hits="0"/>
						<line number="421" hits="0"/>
						<line number="422" hits="0"/>
						<line number="425" hits="1"/>
						<line number="443" hits="1"/>
						<line number="459" hits="1"/>
						<line number="460" hits="1"/>
						<line number="461" hits="1"/>
						<line number="462" hits="1"/>
						<line number="463" hits="1"/>
						<line number="466" hits="1"/>
						<line number="467" hits="1"/>
						<line number="469" hits="1"/>
						<line number="477" hits="0"/>
						<line number="479" hits="0"/>
						<line number="480" hits="0"/>
						<line number="485" hits="1"/>
						<line number="496" hits="0"/>
						<line number="497" hits="0"/>
						<line number="498" hits="0"/>
						<line number="499" hits="0"/>
						<line number="501" hits="0"/>
						<line number="502" hits="0"/>
						<line number="507" hits="0"/>
						<line number="508" hits="0"/>
						<line number="509" hits="0"/>
						<line number="511" hits="0"/>
						<line number="515" hits="0"/>
						<line number="517" hits="1"/>
						<line number="524" hits="0"/>
						<line number="526" hits="1"/>
						<line number="539" hits="0"/>
						<line number="540" hits="0"/>
						<line number="541" hits="0"/>
						<line number="543" hits="0"/>
						<line number="546" hits="0"/>
						<line number="549" hits="0"/>
						<line number="550" hits="0"/>
						<line number="555" hits="0"/>
						<line number="558" hits="0"/>
						<line number="561" hits="0"/>
						<line number="566" hits="0"/>
						<line number="567" hits="0"/>
						<line number="569" hits="0"/>
						<line number="573" hits="1"/>
						<line number="579" hits="0"/>
						<line number="580" hits="0"/>
						<line number="581" hits="0"/>
						<line number="584" hits="0"/>
						<line number="585" hits="0"/>
						<line number="586" hits="0"/>
						<line number="587" hits="0"/>
						<line number="589" hits="1"/>
						<line number="596" hits="0"/>
						<line number="597" hits="0"/>
						<line number="600" hits="0"/>
						<line number="603" hits="0"/>
						<line number="604" hits="0"/>
						<line number="607" hits="0"/>
						<line number="610" hits="0"/>
						<line number="611" hits="0"/>
					</lines>
				</class>
				<class name="executors.py" filename="_internal/runtime/executors.py" complexity="0" line-rate="0.5435" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="35" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="70" hits="1"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="75" hits="1"/>
						<line number="77" hits="1"/>
						<line number="80" hits="1"/>
						<line number="82" hits="0"/>
						<line number="84" hits="1"/>
						<line number="87" hits="1"/>
						<line number="88" hits="0"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="96" hits="1"/>
						<line number="98" hits="1"/>
						<line number="103" hits="0"/>
						<line number="107" hits="1"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="125" hits="0"/>
						<line number="127" hits="1"/>
						<line number="138" hits="0"/>
						<line number="140" hits="0"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
						<line number="147" hits="0"/>
						<line number="149" hits="0"/>
					</lines>
				</class>
				<class name="fault_policy.py" filename="_internal/runtime/fault_policy.py" complexity="0" line-rate="0.44" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="33" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="64" hits="1"/>
						<line number="83" hits="0"/>
						<line number="85" hits="0"/>
						<line number="87" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="97" hits="0"/>
						<line number="101" hits="0"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
					</lines>
				</class>
				<class name="lifecycle.py" filename="_internal/runtime/lifecycle.py" complexity="0" line-rate="0.2581" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="36" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="70" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="76" hits="0"/>
						<line number="78" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="89" hits="0"/>
						<line number="91" hits="0"/>
						<line number="93" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="98" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="108" hits="1"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="124" hits="0"/>
						<line number="126" hits="0"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="133" hits="0"/>
						<line number="135" hits="0"/>
						<line number="136" hits="0"/>
						<line number="138" hits="0"/>
						<line number="140" hits="0"/>
						<line number="141" hits="0"/>
						<line number="145" hits="0"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
						<line number="161" hits="1"/>
						<line number="168" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="_internal.validation" line-rate="0.3514" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="_internal/validation/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="4" hits="1"/>
						<line number="6" hits="1"/>
					</lines>
				</class>
				<class name="dependency_validation.py" filename="_internal/validation/dependency_validation.py" complexity="0" line-rate="0.5352" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
						<line number="44" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="59" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="85" hits="1"/>
						<line number="106" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="0"/>
						<line number="111" hits="0"/>
						<line number="116" hits="1"/>
						<line number="117" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="126" hits="1"/>
						<line number="130" hits="1"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="0"/>
						<line number="153" hits="0"/>
						<line number="158" hits="0"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="166" hits="0"/>
						<line number="167" hits="0"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0"/>
						<line number="181" hits="1"/>
						<line number="203" hits="1"/>
						<line number="204" hits="1"/>
						<line number="205" hits="0"/>
						<line number="206" hits="0"/>
						<line number="211" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="214" hits="0"/>
						<line number="217" hits="1"/>
						<line number="220" hits="1"/>
						<line number="226" hits="1"/>
						<line number="228" hits="0"/>
						<line number="231" hits="1"/>
						<line number="232" hits="0"/>
						<line number="233" hits="0"/>
						<line number="234" hits="0"/>
						<line number="240" hits="1"/>
						<line number="241" hits="1"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="246" hits="0"/>
						<line number="247" hits="0"/>
						<line number="248" hits="0"/>
					</lines>
				</class>
				<class name="manifest_loader.py" filename="_internal/validation/manifest_loader.py" complexity="0" line-rate="0.1852" branch-rate="0">
					<methods/>
					<lines>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="29" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="63" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="69" hits="0"/>
						<line number="72" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="89" hits="0"/>
						<line number="90" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="95" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="102" hits="0"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="109" hits="0"/>
						<line number="110" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="117" hits="0"/>
						<line number="119" hits="0"/>
						<line number="121" hits="0"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0"/>
						<line number="127" hits="1"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
						<line number="145" hits="0"/>
						<line number="146" hits="0"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="151" hits="0"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="155" hits="0"/>
						<line number="157" hits="0"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="163" hits="0"/>
						<line number="165" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="175" hits="0"/>
						<line number="177" hits="0"/>
						<line number="179" hits="1"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="198" hits="0"/>
						<line number="199" hits="0"/>
						<line number="204" hits="1"/>
						<line number="225" hits="0"/>
						<line number="226" hits="0"/>
					</lines>
				</class>
				<class name="name_validation.py" filename="_internal/validation/name_validation.py" complexity="0" line-rate="0.18" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="17" hits="1"/>
						<line number="27" hits="0"/>
						<line number="28" hits="0"/>
						<line number="30" hits="0"/>
						<line number="31" hits="0"/>
						<line number="36" hits="0"/>
						<line number="37" hits="0"/>
						<line number="40" hits="1"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="63" hits="1"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="87" hits="1"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="107" hits="1"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="130" hits="1"/>
						<line number="140" hits="0"/>
						<line number="143" hits="1"/>
						<line number="157" hits="0"/>
						<line number="158" hits="0"/>
						<line number="161" hits="0"/>
						<line number="164" hits="0"/>
						<line number="165" hits="0"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="172" hits="0"/>
						<line number="173" hits="0"/>
						<line number="175" hits="0"/>
					</lines>
				</class>
				<class name="plugin_validation.py" filename="_internal/validation/plugin_validation.py" complexity="0" line-rate="0.4583" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="57" hits="1"/>
						<line number="66" hits="1"/>
						<line number="76" hits="1"/>
						<line number="86" hits="1"/>
						<line number="96" hits="1"/>
						<line number="107" hits="1"/>
						<line number="121" hits="0"/>
						<line number="124" hits="0"/>
						<line number="125" hits="0"/>
						<line number="128" hits="0"/>
						<line number="129" hits="0"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="133" hits="0"/>
						<line number="136" hits="0"/>
						<line number="139" hits="0"/>
						<line number="140" hits="0"/>
						<line number="145" hits="0"/>
						<line number="148" hits="0"/>
						<line number="151" hits="1"/>
						<line number="153" hits="0"/>
						<line number="154" hits="0"/>
						<line number="159" hits="0"/>
						<line number="160" hits="0"/>
						<line number="166" hits="1"/>
						<line number="168" hits="0"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="189" hits="1"/>
						<line number="191" hits="0"/>
						<line number="192" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="200" hits="0"/>
						<line number="205" hits="0"/>
						<line number="206" hits="0"/>
						<line number="207" hits="0"/>
						<line number="208" hits="0"/>
						<line number="216" hits="1"/>
						<line number="229" hits="0"/>
						<line number="232" hits="1"/>
						<line number="245" hits="0"/>
						<line number="248" hits="1"/>
						<line number="261" hits="0"/>
						<line number="264" hits="1"/>
						<line number="276" hits="0"/>
						<line number="279" hits="1"/>
						<line number="292" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="api" line-rate="0.4945" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="api/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
					</lines>
				</class>
				<class name="app.py" filename="api/app.py" complexity="0" line-rate="0.3697" branch-rate="0">
					<methods/>
					<lines>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="0"/>
						<line number="25" hits="0"/>
						<line number="26" hits="0"/>
						<line number="29" hits="1"/>
						<line number="47" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="83" hits="1"/>
						<line number="85" hits="1"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="101" hits="0"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="106" hits="0"/>
						<line number="108" hits="0"/>
						<line number="111" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="115" hits="0"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="119" hits="0"/>
						<line number="130" hits="0"/>
						<line number="132" hits="1"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="145" hits="1"/>
						<line number="156" hits="0"/>
						<line number="157" hits="0"/>
						<line number="158" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="166" hits="0"/>
						<line number="167" hits="0"/>
						<line number="169" hits="0"/>
						<line number="178" hits="0"/>
						<line number="180" hits="0"/>
						<line number="191" hits="0"/>
						<line number="192" hits="0"/>
						<line number="194" hits="1"/>
						<line number="204" hits="0"/>
						<line number="205" hits="0"/>
						<line number="207" hits="0"/>
						<line number="208" hits="0"/>
						<line number="211" hits="0"/>
						<line number="214" hits="1"/>
						<line number="229" hits="0"/>
						<line number="230" hits="0"/>
						<line number="231" hits="0"/>
						<line number="233" hits="1"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="245" hits="0"/>
						<line number="247" hits="1"/>
						<line number="254" hits="1"/>
						<line number="255" hits="0"/>
						<line number="256" hits="1"/>
						<line number="259" hits="1"/>
						<line number="270" hits="0"/>
						<line number="271" hits="0"/>
						<line number="272" hits="0"/>
						<line number="274" hits="1"/>
						<line number="281" hits="0"/>
						<line number="282" hits="0"/>
						<line number="283" hits="0"/>
						<line number="286" hits="1"/>
						<line number="303" hits="0"/>
						<line number="304" hits="0"/>
						<line number="306" hits="0"/>
						<line number="307" hits="0"/>
						<line number="309" hits="0"/>
						<line number="320" hits="1"/>
						<line number="333" hits="0"/>
						<line number="334" hits="0"/>
						<line number="335" hits="0"/>
						<line number="337" hits="1"/>
						<line number="348" hits="0"/>
						<line number="349" hits="0"/>
						<line number="350" hits="0"/>
						<line number="352" hits="1"/>
						<line number="362" hits="0"/>
						<line number="363" hits="0"/>
						<line number="364" hits="0"/>
						<line number="367" hits="1"/>
						<line number="368" hits="1"/>
						<line number="370" hits="1"/>
						<line number="372" hits="1"/>
						<line number="373" hits="1"/>
						<line number="375" hits="0"/>
						<line number="377" hits="1"/>
						<line number="378" hits="1"/>
						<line number="380" hits="0"/>
						<line number="382" hits="1"/>
						<line number="418" hits="0"/>
						<line number="421" hits="0"/>
						<line number="422" hits="0"/>
						<line number="427" hits="0"/>
						<line number="428" hits="0"/>
						<line number="430" hits="0"/>
						<line number="432" hits="0"/>
					</lines>
				</class>
				<class name="app_plugin.py" filename="api/app_plugin.py" complexity="0" line-rate="0.2128" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="0"/>
						<line number="29" hits="1"/>
						<line number="50" hits="1"/>
						<line number="59" hits="0"/>
						<line number="62" hits="0"/>
						<line number="65" hits="0"/>
						<line number="68" hits="0"/>
						<line number="71" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="81" hits="0"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="129" hits="1"/>
						<line number="158" hits="1"/>
						<line number="169" hits="0"/>
						<line number="171" hits="0"/>
						<line number="173" hits="0"/>
						<line number="174" hits="0"/>
						<line number="175" hits="0"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0"/>
						<line number="181" hits="0"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="191" hits="0"/>
						<line number="195" hits="1"/>
						<line number="201" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="205" hits="0"/>
						<line number="206" hits="0"/>
						<line number="212" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0"/>
						<line number="220" hits="1"/>
						<line number="244" hits="0"/>
						<line number="245" hits="0"/>
						<line number="248" hits="0"/>
						<line number="251" hits="0"/>
						<line number="257" hits="0"/>
						<line number="259" hits="0"/>
						<line number="265" hits="1"/>
						<line number="289" hits="0"/>
						<line number="290" hits="0"/>
						<line number="291" hits="0"/>
						<line number="292" hits="0"/>
						<line number="295" hits="0"/>
						<line number="301" hits="0"/>
						<line number="303" hits="0"/>
						<line number="311" hits="1"/>
						<line number="328" hits="0"/>
						<line number="330" hits="0"/>
						<line number="331" hits="0"/>
						<line number="332" hits="0"/>
						<line number="335" hits="0"/>
						<line number="336" hits="0"/>
						<line number="337" hits="0"/>
						<line number="341" hits="0"/>
						<line number="342" hits="0"/>
						<line number="343" hits="0"/>
						<line number="344" hits="0"/>
						<line number="345" hits="0"/>
						<line number="348" hits="0"/>
						<line number="351" hits="0"/>
						<line number="354" hits="0"/>
						<line number="358" hits="0"/>
						<line number="364" hits="0"/>
						<line number="365" hits="0"/>
						<line number="371" hits="1"/>
						<line number="388" hits="0"/>
						<line number="390" hits="0"/>
						<line number="391" hits="0"/>
						<line number="394" hits="0"/>
						<line number="395" hits="0"/>
						<line number="396" hits="0"/>
						<line number="399" hits="0"/>
						<line number="400" hits="0"/>
						<line number="402" hits="0"/>
						<line number="407" hits="0"/>
						<line number="408" hits="0"/>
					</lines>
				</class>
				<class name="background.py" filename="api/background.py" complexity="0" line-rate="0.4286" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="33" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="0"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="47" hits="0"/>
						<line number="50" hits="1"/>
						<line number="89" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="100" hits="0"/>
						<line number="102" hits="0"/>
						<line number="106" hits="0"/>
						<line number="110" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="124" hits="0"/>
						<line number="127" hits="0"/>
						<line number="129" hits="0"/>
						<line number="131" hits="0"/>
						<line number="134" hits="1"/>
						<line number="136" hits="0"/>
						<line number="139" hits="1"/>
						<line number="141" hits="0"/>
						<line number="144" hits="1"/>
						<line number="146" hits="0"/>
					</lines>
				</class>
				<class name="builder.py" filename="api/builder.py" complexity="0" line-rate="0.5992" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="50" hits="1"/>
						<line number="51" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="75" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="96" hits="1"/>
						<line number="97" hits="1"/>
						<line number="99" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="1"/>
						<line number="104" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="0"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="132" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="0"/>
						<line number="138" hits="1"/>
						<line number="142" hits="1"/>
						<line number="145" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="153" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="165" hits="1"/>
						<line number="166" hits="1"/>
						<line number="167" hits="1"/>
						<line number="168" hits="1"/>
						<line number="171" hits="1"/>
						<line number="172" hits="1"/>
						<line number="173" hits="1"/>
						<line number="176" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="183" hits="1"/>
						<line number="194" hits="1"/>
						<line number="197" hits="1"/>
						<line number="201" hits="0"/>
						<line number="203" hits="1"/>
						<line number="224" hits="1"/>
						<line number="225" hits="0"/>
						<line number="229" hits="1"/>
						<line number="236" hits="1"/>
						<line number="238" hits="1"/>
						<line number="248" hits="1"/>
						<line number="249" hits="0"/>
						<line number="256" hits="0"/>
						<line number="266" hits="1"/>
						<line number="267" hits="0"/>
						<line number="274" hits="0"/>
						<line number="287" hits="1"/>
						<line number="290" hits="1"/>
						<line number="292" hits="0"/>
						<line number="308" hits="1"/>
						<line number="309" hits="1"/>
						<line number="310" hits="0"/>
						<line number="316" hits="1"/>
						<line number="319" hits="1"/>
						<line number="320" hits="1"/>
						<line number="326" hits="1"/>
						<line number="329" hits="1"/>
						<line number="330" hits="0"/>
						<line number="331" hits="0"/>
						<line number="336" hits="0"/>
						<line number="337" hits="0"/>
						<line number="339" hits="0"/>
						<line number="340" hits="0"/>
						<line number="342" hits="0"/>
						<line number="354" hits="1"/>
						<line number="355" hits="1"/>
						<line number="358" hits="1"/>
						<line number="372" hits="1"/>
						<line number="373" hits="0"/>
						<line number="375" hits="1"/>
						<line number="377" hits="1"/>
						<line number="380" hits="1"/>
						<line number="381" hits="1"/>
						<line number="392" hits="1"/>
						<line number="394" hits="1"/>
						<line number="404" hits="0"/>
						<line number="406" hits="0"/>
						<line number="408" hits="0"/>
						<line number="421" hits="0"/>
						<line number="433" hits="1"/>
						<line number="435" hits="1"/>
						<line number="457" hits="0"/>
						<line number="458" hits="0"/>
						<line number="464" hits="0"/>
						<line number="465" hits="0"/>
						<line number="470" hits="0"/>
						<line number="471" hits="0"/>
						<line number="473" hits="1"/>
						<line number="487" hits="0"/>
						<line number="488" hits="0"/>
						<line number="489" hits="0"/>
						<line number="490" hits="0"/>
						<line number="492" hits="1"/>
						<line number="499" hits="0"/>
						<line number="500" hits="0"/>
						<line number="501" hits="0"/>
						<line number="502" hits="0"/>
						<line number="504" hits="1"/>
						<line number="543" hits="0"/>
						<line number="544" hits="0"/>
						<line number="546" hits="0"/>
						<line number="549" hits="0"/>
						<line number="557" hits="0"/>
						<line number="558" hits="0"/>
						<line number="563" hits="0"/>
						<line number="565" hits="0"/>
						<line number="566" hits="0"/>
						<line number="567" hits="0"/>
						<line number="568" hits="0"/>
						<line number="570" hits="0"/>
						<line number="572" hits="1"/>
						<line number="604" hits="0"/>
						<line number="606" hits="0"/>
						<line number="612" hits="0"/>
						<line number="616" hits="0"/>
						<line number="618" hits="1"/>
						<line number="650" hits="1"/>
						<line number="651" hits="0"/>
						<line number="653" hits="1"/>
						<line number="655" hits="1"/>
						<line number="706" hits="0"/>
						<line number="709" hits="0"/>
						<line number="716" hits="0"/>
						<line number="718" hits="0"/>
						<line number="719" hits="0"/>
						<line number="721" hits="1"/>
						<line number="741" hits="0"/>
						<line number="743" hits="1"/>
						<line number="781" hits="0"/>
						<line number="784" hits="0"/>
						<line number="787" hits="0"/>
						<line number="788" hits="0"/>
						<line number="791" hits="0"/>
						<line number="796" hits="0"/>
						<line number="797" hits="0"/>
						<line number="799" hits="1"/>
						<line number="806" hits="0"/>
						<line number="808" hits="0"/>
						<line number="810" hits="0"/>
						<line number="811" hits="0"/>
						<line number="812" hits="0"/>
						<line number="813" hits="0"/>
						<line number="815" hits="0"/>
						<line number="816" hits="0"/>
						<line number="818" hits="0"/>
						<line number="820" hits="1"/>
						<line number="832" hits="1"/>
						<line number="834" hits="1"/>
						<line number="845" hits="1"/>
						<line number="848" hits="1"/>
						<line number="850" hits="1"/>
						<line number="857" hits="1"/>
						<line number="858" hits="0"/>
						<line number="861" hits="1"/>
						<line number="862" hits="1"/>
						<line number="863" hits="0"/>
						<line number="869" hits="1"/>
						<line number="871" hits="1"/>
						<line number="877" hits="1"/>
						<line number="878" hits="1"/>
						<line number="885" hits="1"/>
						<line number="886" hits="1"/>
						<line number="887" hits="1"/>
						<line number="888" hits="0"/>
						<line number="889" hits="1"/>
						<line number="891" hits="1"/>
						<line number="892" hits="0"/>
						<line number="893" hits="0"/>
						<line number="894" hits="0"/>
						<line number="896" hits="1"/>
						<line number="906" hits="1"/>
						<line number="908" hits="1"/>
						<line number="917" hits="1"/>
						<line number="918" hits="1"/>
						<line number="922" hits="1"/>
						<line number="923" hits="1"/>
						<line number="934" hits="1"/>
						<line number="935" hits="1"/>
						<line number="938" hits="1"/>
						<line number="939" hits="1"/>
						<line number="945" hits="1"/>
						<line number="948" hits="1"/>
						<line number="949" hits="1"/>
						<line number="950" hits="0"/>
						<line number="951" hits="0"/>
						<line number="953" hits="1"/>
						<line number="964" hits="1"/>
						<line number="966" hits="1"/>
						<line number="968" hits="0"/>
						<line number="970" hits="0"/>
						<line number="971" hits="0"/>
						<line number="984" hits="0"/>
						<line number="985" hits="0"/>
						<line number="1000" hits="1"/>
						<line number="1036" hits="0"/>
						<line number="1039" hits="0"/>
						<line number="1040" hits="0"/>
						<line number="1045" hits="0"/>
						<line number="1046" hits="0"/>
						<line number="1048" hits="0"/>
						<line number="1050" hits="0"/>
					</lines>
				</class>
				<class name="depends.py" filename="api/depends.py" complexity="0" line-rate="0.8125" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="0"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="41" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="0"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="73" hits="0"/>
					</lines>
				</class>
				<class name="events.py" filename="api/events.py" complexity="0" line-rate="0.4516" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="65" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="0"/>
						<line number="73" hits="1"/>
						<line number="74" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="0"/>
						<line number="83" hits="1"/>
						<line number="86" hits="0"/>
						<line number="91" hits="1"/>
						<line number="92" hits="0"/>
						<line number="97" hits="1"/>
						<line number="100" hits="1"/>
						<line number="101" hits="1"/>
						<line number="102" hits="0"/>
						<line number="108" hits="1"/>
						<line number="114" hits="0"/>
						<line number="119" hits="1"/>
						<line number="138" hits="1"/>
						<line number="140" hits="1"/>
						<line number="142" hits="1"/>
						<line number="145" hits="1"/>
						<line number="158" hits="1"/>
						<line number="159" hits="1"/>
						<line number="160" hits="1"/>
						<line number="161" hits="1"/>
						<line number="162" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="0"/>
						<line number="165" hits="1"/>
						<line number="166" hits="0"/>
						<line number="171" hits="1"/>
						<line number="184" hits="0"/>
						<line number="185" hits="0"/>
						<line number="190" hits="0"/>
						<line number="193" hits="1"/>
						<line number="203" hits="0"/>
						<line number="206" hits="1"/>
						<line number="216" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="0"/>
						<line number="223" hits="0"/>
						<line number="224" hits="0"/>
						<line number="227" hits="0"/>
						<line number="230" hits="0"/>
						<line number="232" hits="0"/>
						<line number="233" hits="0"/>
						<line number="235" hits="0"/>
						<line number="238" hits="1"/>
						<line number="250" hits="0"/>
						<line number="253" hits="0"/>
						<line number="255" hits="0"/>
						<line number="257" hits="0"/>
						<line number="258" hits="0"/>
						<line number="261" hits="0"/>
						<line number="262" hits="0"/>
						<line number="265" hits="0"/>
						<line number="267" hits="0"/>
						<line number="270" hits="1"/>
						<line number="283" hits="0"/>
						<line number="284" hits="0"/>
						<line number="285" hits="0"/>
						<line number="287" hits="0"/>
						<line number="290" hits="1"/>
						<line number="292" hits="0"/>
						<line number="293" hits="0"/>
						<line number="296" hits="1"/>
						<line number="298" hits="0"/>
						<line number="299" hits="0"/>
						<line number="303" hits="0"/>
						<line number="304" hits="0"/>
						<line number="309" hits="0"/>
						<line number="310" hits="0"/>
						<line number="311" hits="0"/>
						<line number="317" hits="1"/>
						<line number="321" hits="0"/>
						<line number="322" hits="0"/>
						<line number="323" hits="0"/>
						<line number="326" hits="0"/>
						<line number="327" hits="0"/>
						<line number="332" hits="1"/>
						<line number="335" hits="0"/>
						<line number="341" hits="0"/>
					</lines>
				</class>
				<class name="plugin.py" filename="api/plugin.py" complexity="0" line-rate="0.7358" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="0"/>
						<line number="28" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="65" hits="1"/>
						<line number="68" hits="1"/>
						<line number="70" hits="1"/>
						<line number="83" hits="1"/>
						<line number="86" hits="1"/>
						<line number="87" hits="0"/>
						<line number="123" hits="1"/>
						<line number="161" hits="1"/>
						<line number="163" hits="1"/>
						<line number="164" hits="0"/>
						<line number="169" hits="1"/>
						<line number="170" hits="0"/>
						<line number="175" hits="1"/>
						<line number="176" hits="0"/>
						<line number="178" hits="1"/>
						<line number="179" hits="0"/>
						<line number="181" hits="1"/>
						<line number="182" hits="0"/>
						<line number="187" hits="1"/>
						<line number="188" hits="0"/>
						<line number="193" hits="1"/>
						<line number="197" hits="0"/>
						<line number="202" hits="1"/>
						<line number="203" hits="1"/>
						<line number="204" hits="0"/>
						<line number="209" hits="1"/>
						<line number="210" hits="1"/>
						<line number="211" hits="1"/>
						<line number="214" hits="1"/>
						<line number="216" hits="1"/>
						<line number="218" hits="1"/>
						<line number="221" hits="1"/>
						<line number="234" hits="0"/>
						<line number="235" hits="0"/>
						<line number="239" hits="0"/>
						<line number="248" hits="1"/>
						<line number="258" hits="0"/>
					</lines>
				</class>
				<class name="service.py" filename="api/service.py" complexity="0" line-rate="0.5" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="23" hits="1"/>
						<line number="26" hits="1"/>
						<line number="57" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="0"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="0"/>
						<line number="75" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="0"/>
						<line number="84" hits="1"/>
						<line number="87" hits="0"/>
						<line number="92" hits="1"/>
						<line number="110" hits="1"/>
						<line number="112" hits="1"/>
						<line number="114" hits="1"/>
						<line number="117" hits="1"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="135" hits="0"/>
						<line number="138" hits="1"/>
						<line number="148" hits="0"/>
						<line number="151" hits="1"/>
						<line number="161" hits="0"/>
						<line number="164" hits="0"/>
						<line number="166" hits="0"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="172" hits="0"/>
						<line number="175" hits="0"/>
						<line number="177" hits="0"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0"/>
						<line number="180" hits="0"/>
						<line number="182" hits="0"/>
						<line number="185" hits="1"/>
						<line number="198" hits="0"/>
						<line number="199" hits="0"/>
						<line number="200" hits="0"/>
						<line number="202" hits="0"/>
						<line number="205" hits="1"/>
						<line number="215" hits="0"/>
						<line number="216" hits="0"/>
						<line number="218" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="config" line-rate="0.5266" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="config/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="19" hits="1"/>
						<line number="24" hits="1"/>
					</lines>
				</class>
				<class name="models.py" filename="config/models.py" complexity="0" line-rate="0.7792" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="32" hits="1"/>
						<line number="37" hits="1"/>
						<line number="43" hits="1"/>
						<line number="51" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="62" hits="0"/>
						<line number="66" hits="0"/>
						<line number="68" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="0"/>
						<line number="81" hits="1"/>
						<line number="83" hits="1"/>
						<line number="93" hits="0"/>
						<line number="95" hits="1"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="108" hits="0"/>
						<line number="110" hits="1"/>
						<line number="120" hits="0"/>
						<line number="122" hits="1"/>
						<line number="132" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="137" hits="1"/>
						<line number="139" hits="0"/>
						<line number="141" hits="1"/>
						<line number="143" hits="0"/>
						<line number="145" hits="1"/>
						<line number="147" hits="0"/>
						<line number="149" hits="1"/>
						<line number="155" hits="1"/>
						<line number="163" hits="1"/>
						<line number="169" hits="1"/>
						<line number="174" hits="1"/>
						<line number="179" hits="1"/>
						<line number="185" hits="1"/>
						<line number="190" hits="1"/>
						<line number="195" hits="1"/>
						<line number="201" hits="1"/>
						<line number="209" hits="1"/>
						<line number="212" hits="1"/>
						<line number="213" hits="1"/>
						<line number="214" hits="1"/>
						<line number="215" hits="1"/>
						<line number="220" hits="1"/>
						<line number="226" hits="1"/>
						<line number="229" hits="1"/>
						<line number="230" hits="1"/>
						<line number="231" hits="1"/>
						<line number="235" hits="1"/>
						<line number="240" hits="1"/>
						<line number="246" hits="1"/>
						<line number="249" hits="1"/>
						<line number="253" hits="1"/>
						<line number="257" hits="1"/>
						<line number="261" hits="1"/>
						<line number="266" hits="1"/>
						<line number="272" hits="1"/>
						<line number="280" hits="1"/>
						<line number="285" hits="1"/>
						<line number="289" hits="1"/>
						<line number="294" hits="1"/>
						<line number="299" hits="1"/>
					</lines>
				</class>
				<class name="typed_config.py" filename="config/typed_config.py" complexity="0" line-rate="0.3333" branch-rate="0">
					<methods/>
					<lines>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="32" hits="1"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="58" hits="1"/>
						<line number="60" hits="0"/>
						<line number="61" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="66" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="69" hits="0"/>
						<line number="70" hits="0"/>
						<line number="72" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="79" hits="1"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="110" hits="0"/>
						<line number="112" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="120" hits="1"/>
						<line number="130" hits="0"/>
						<line number="131" hits="0"/>
						<line number="136" hits="0"/>
						<line number="137" hits="0"/>
						<line number="142" hits="0"/>
						<line number="144" hits="1"/>
						<line number="151" hits="0"/>
						<line number="153" hits="1"/>
						<line number="164" hits="0"/>
						<line number="166" hits="1"/>
						<line number="168" hits="0"/>
						<line number="170" hits="1"/>
						<line number="172" hits="0"/>
						<line number="174" hits="1"/>
						<line number="176" hits="0"/>
						<line number="178" hits="1"/>
						<line number="180" hits="0"/>
						<line number="182" hits="1"/>
						<line number="184" hits="0"/>
						<line number="186" hits="1"/>
						<line number="187" hits="1"/>
						<line number="189" hits="0"/>
						<line number="191" hits="1"/>
						<line number="192" hits="1"/>
						<line number="194" hits="0"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="199" hits="0"/>
						<line number="208" hits="1"/>
						<line number="216" hits="1"/>
						<line number="223" hits="1"/>
						<line number="224" hits="1"/>
						<line number="226" hits="1"/>
						<line number="246" hits="0"/>
						<line number="248" hits="0"/>
						<line number="255" hits="0"/>
						<line number="256" hits="0"/>
						<line number="258" hits="1"/>
						<line number="268" hits="0"/>
						<line number="270" hits="1"/>
						<line number="288" hits="0"/>
						<line number="290" hits="0"/>
						<line number="291" hits="0"/>
						<line number="293" hits="0"/>
						<line number="294" hits="0"/>
						<line number="295" hits="0"/>
						<line number="296" hits="0"/>
						<line number="301" hits="0"/>
						<line number="303" hits="0"/>
						<line number="304" hits="0"/>
						<line number="306" hits="0"/>
						<line number="307" hits="0"/>
						<line number="311" hits="0"/>
						<line number="312" hits="0"/>
						<line number="314" hits="0"/>
						<line number="315" hits="0"/>
						<line number="317" hits="1"/>
						<line number="319" hits="0"/>
						<line number="320" hits="0"/>
						<line number="322" hits="1"/>
						<line number="324" hits="0"/>
						<line number="326" hits="1"/>
						<line number="328" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="core" line-rate="0.455" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="core/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="18" hits="1"/>
					</lines>
				</class>
				<class name="config.py" filename="core/config.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="16" hits="1"/>
						<line number="22" hits="1"/>
						<line number="23" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="40" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="51" hits="1"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="71" hits="1"/>
					</lines>
				</class>
				<class name="constants.py" filename="core/constants.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="27" hits="1"/>
						<line number="36" hits="1"/>
						<line number="43" hits="1"/>
						<line number="50" hits="1"/>
						<line number="56" hits="1"/>
						<line number="60" hits="1"/>
						<line number="78" hits="1"/>
						<line number="81" hits="1"/>
						<line number="84" hits="1"/>
						<line number="87" hits="1"/>
						<line number="90" hits="1"/>
						<line number="93" hits="1"/>
						<line number="96" hits="1"/>
					</lines>
				</class>
				<class name="deprecation.py" filename="core/deprecation.py" complexity="0" line-rate="0.2059" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="51" hits="0"/>
						<line number="53" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="61" hits="0"/>
						<line number="63" hits="0"/>
						<line number="64" hits="0"/>
						<line number="65" hits="0"/>
						<line number="70" hits="0"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="78" hits="0"/>
						<line number="80" hits="0"/>
						<line number="83" hits="1"/>
						<line number="118" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="123" hits="0"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="128" hits="0"/>
						<line number="129" hits="0"/>
						<line number="131" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="136" hits="0"/>
						<line number="137" hits="0"/>
						<line number="143" hits="0"/>
						<line number="145" hits="0"/>
						<line number="147" hits="0"/>
						<line number="150" hits="1"/>
						<line number="166" hits="0"/>
						<line number="169" hits="1"/>
						<line number="188" hits="0"/>
						<line number="189" hits="0"/>
						<line number="191" hits="0"/>
						<line number="198" hits="1"/>
						<line number="214" hits="1"/>
						<line number="216" hits="0"/>
						<line number="219" hits="0"/>
						<line number="220" hits="0"/>
						<line number="222" hits="0"/>
						<line number="223" hits="0"/>
						<line number="224" hits="0"/>
						<line number="225" hits="0"/>
						<line number="226" hits="0"/>
						<line number="228" hits="0"/>
						<line number="229" hits="0"/>
						<line number="230" hits="0"/>
						<line number="231" hits="0"/>
						<line number="232" hits="0"/>
						<line number="234" hits="0"/>
						<line number="240" hits="0"/>
						<line number="243" hits="0"/>
						<line number="246" hits="1"/>
						<line number="262" hits="0"/>
						<line number="271" hits="1"/>
					</lines>
				</class>
				<class name="docstring_convention.py" filename="core/docstring_convention.py" complexity="0" line-rate="0.2111" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="44" hits="1"/>
						<line number="46" hits="1"/>
						<line number="48" hits="0"/>
						<line number="49" hits="0"/>
						<line number="54" hits="1"/>
						<line number="55" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="65" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="68" hits="1"/>
						<line number="69" hits="1"/>
						<line number="70" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="87" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="91" hits="1"/>
						<line number="97" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="105" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="118" hits="0"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="123" hits="0"/>
						<line number="125" hits="1"/>
						<line number="127" hits="0"/>
						<line number="130" hits="1"/>
						<line number="134" hits="1"/>
						<line number="148" hits="1"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="164" hits="0"/>
						<line number="167" hits="0"/>
						<line number="168" hits="0"/>
						<line number="170" hits="0"/>
						<line number="171" hits="0"/>
						<line number="174" hits="0"/>
						<line number="177" hits="0"/>
						<line number="193" hits="1"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="198" hits="0"/>
						<line number="199" hits="0"/>
						<line number="200" hits="0"/>
						<line number="201" hits="0"/>
						<line number="204" hits="0"/>
						<line number="207" hits="0"/>
						<line number="208" hits="0"/>
						<line number="210" hits="0"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0"/>
						<line number="217" hits="0"/>
						<line number="218" hits="0"/>
						<line number="219" hits="0"/>
						<line number="221" hits="0"/>
						<line number="223" hits="1"/>
						<line number="225" hits="0"/>
						<line number="228" hits="0"/>
						<line number="229" hits="0"/>
						<line number="230" hits="0"/>
						<line number="231" hits="0"/>
						<line number="234" hits="0"/>
						<line number="237" hits="0"/>
						<line number="239" hits="0"/>
						<line number="240" hits="0"/>
						<line number="242" hits="0"/>
						<line number="245" hits="0"/>
						<line number="246" hits="0"/>
						<line number="247" hits="0"/>
						<line number="249" hits="0"/>
						<line number="251" hits="1"/>
						<line number="253" hits="0"/>
						<line number="254" hits="0"/>
						<line number="255" hits="0"/>
						<line number="258" hits="0"/>
						<line number="259" hits="0"/>
						<line number="260" hits="0"/>
						<line number="261" hits="0"/>
						<line number="262" hits="0"/>
						<line number="263" hits="0"/>
						<line number="264" hits="0"/>
						<line number="265" hits="0"/>
						<line number="268" hits="0"/>
						<line number="269" hits="0"/>
						<line number="271" hits="0"/>
						<line number="273" hits="0"/>
						<line number="274" hits="0"/>
						<line number="276" hits="1"/>
						<line number="278" hits="0"/>
						<line number="279" hits="0"/>
						<line number="280" hits="0"/>
						<line number="283" hits="0"/>
						<line number="284" hits="0"/>
						<line number="285" hits="0"/>
						<line number="286" hits="0"/>
						<line number="287" hits="0"/>
						<line number="289" hits="0"/>
						<line number="291" hits="1"/>
						<line number="293" hits="0"/>
						<line number="294" hits="0"/>
						<line number="295" hits="0"/>
						<line number="298" hits="0"/>
						<line number="299" hits="0"/>
						<line number="300" hits="0"/>
						<line number="301" hits="0"/>
						<line number="302" hits="0"/>
						<line number="304" hits="0"/>
						<line number="306" hits="1"/>
						<line number="308" hits="0"/>
						<line number="309" hits="0"/>
						<line number="312" hits="0"/>
						<line number="313" hits="0"/>
						<line number="315" hits="0"/>
						<line number="316" hits="0"/>
						<line number="317" hits="0"/>
						<line number="318" hits="0"/>
						<line number="319" hits="0"/>
						<line number="320" hits="0"/>
						<line number="321" hits="0"/>
						<line number="323" hits="0"/>
						<line number="324" hits="0"/>
						<line number="326" hits="0"/>
						<line number="328" hits="1"/>
						<line number="330" hits="0"/>
						<line number="331" hits="0"/>
						<line number="334" hits="0"/>
						<line number="335" hits="0"/>
						<line number="337" hits="0"/>
						<line number="338" hits="0"/>
						<line number="339" hits="0"/>
						<line number="341" hits="0"/>
						<line number="342" hits="0"/>
						<line number="343" hits="0"/>
						<line number="344" hits="0"/>
						<line number="346" hits="0"/>
						<line number="347" hits="0"/>
						<line number="348" hits="0"/>
						<line number="351" hits="0"/>
						<line number="352" hits="0"/>
						<line number="354" hits="0"/>
						<line number="357" hits="0"/>
						<line number="358" hits="0"/>
						<line number="360" hits="0"/>
						<line number="362" hits="1"/>
						<line number="364" hits="0"/>
						<line number="365" hits="0"/>
						<line number="367" hits="0"/>
						<line number="368" hits="0"/>
						<line number="369" hits="0"/>
						<line number="370" hits="0"/>
						<line number="371" hits="0"/>
						<line number="372" hits="0"/>
						<line number="373" hits="0"/>
						<line number="375" hits="0"/>
						<line number="377" hits="1"/>
						<line number="379" hits="0"/>
						<line number="380" hits="0"/>
						<line number="381" hits="0"/>
						<line number="384" hits="0"/>
						<line number="385" hits="0"/>
						<line number="386" hits="0"/>
						<line number="387" hits="0"/>
						<line number="388" hits="0"/>
						<line number="389" hits="0"/>
						<line number="392" hits="0"/>
						<line number="393" hits="0"/>
						<line number="394" hits="0"/>
						<line number="396" hits="0"/>
						<line number="397" hits="0"/>
						<line number="398" hits="0"/>
						<line number="399" hits="0"/>
						<line number="401" hits="0"/>
						<line number="403" hits="0"/>
						<line number="405" hits="1"/>
						<line number="407" hits="0"/>
						<line number="408" hits="0"/>
						<line number="410" hits="0"/>
						<line number="411" hits="0"/>
						<line number="412" hits="0"/>
						<line number="413" hits="0"/>
						<line number="414" hits="0"/>
						<line number="415" hits="0"/>
						<line number="416" hits="0"/>
						<line number="418" hits="0"/>
						<line number="420" hits="1"/>
						<line number="422" hits="0"/>
						<line number="423" hits="0"/>
						<line number="425" hits="0"/>
						<line number="426" hits="0"/>
						<line number="427" hits="0"/>
						<line number="428" hits="0"/>
						<line number="429" hits="0"/>
						<line number="430" hits="0"/>
						<line number="431" hits="0"/>
						<line number="433" hits="0"/>
						<line number="436" hits="1"/>
						<line number="439" hits="1"/>
						<line number="459" hits="0"/>
						<line number="462" hits="0"/>
						<line number="463" hits="0"/>
						<line number="466" hits="0"/>
						<line number="467" hits="0"/>
						<line number="470" hits="0"/>
						<line number="471" hits="0"/>
						<line number="472" hits="0"/>
						<line number="473" hits="0"/>
						<line number="474" hits="0"/>
						<line number="477" hits="0"/>
						<line number="478" hits="0"/>
						<line number="479" hits="0"/>
						<line number="480" hits="0"/>
						<line number="483" hits="0"/>
						<line number="484" hits="0"/>
						<line number="485" hits="0"/>
						<line number="486" hits="0"/>
						<line number="487" hits="0"/>
						<line number="490" hits="0"/>
						<line number="491" hits="0"/>
						<line number="492" hits="0"/>
						<line number="493" hits="0"/>
						<line number="494" hits="0"/>
						<line number="497" hits="0"/>
						<line number="498" hits="0"/>
						<line number="499" hits="0"/>
						<line number="500" hits="0"/>
						<line number="501" hits="0"/>
						<line number="502" hits="0"/>
						<line number="503" hits="0"/>
						<line number="504" hits="0"/>
						<line number="506" hits="0"/>
						<line number="508" hits="0"/>
						<line number="512" hits="1"/>
					</lines>
				</class>
				<class name="error_handler.py" filename="core/error_handler.py" complexity="0" line-rate="0.2273" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="33" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="44" hits="1"/>
						<line number="65" hits="0"/>
						<line number="67" hits="0"/>
						<line number="68" hits="0"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="75" hits="0"/>
						<line number="89" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="96" hits="0"/>
						<line number="99" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="103" hits="0"/>
						<line number="104" hits="0"/>
						<line number="106" hits="0"/>
						<line number="118" hits="1"/>
						<line number="136" hits="0"/>
						<line number="138" hits="0"/>
						<line number="155" hits="1"/>
						<line number="172" hits="0"/>
						<line number="175" hits="0"/>
						<line number="176" hits="0"/>
						<line number="179" hits="0"/>
						<line number="180" hits="0"/>
						<line number="183" hits="0"/>
						<line number="186" hits="0"/>
						<line number="189" hits="0"/>
						<line number="191" hits="1"/>
						<line number="206" hits="0"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="211" hits="0"/>
						<line number="212" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="217" hits="0"/>
						<line number="219" hits="1"/>
						<line number="226" hits="0"/>
						<line number="227" hits="0"/>
						<line number="230" hits="0"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="245" hits="0"/>
						<line number="246" hits="0"/>
						<line number="247" hits="0"/>
						<line number="248" hits="0"/>
						<line number="250" hits="0"/>
						<line number="252" hits="1"/>
						<line number="259" hits="0"/>
						<line number="267" hits="1"/>
						<line number="274" hits="0"/>
						<line number="276" hits="0"/>
						<line number="277" hits="0"/>
						<line number="279" hits="0"/>
						<line number="280" hits="0"/>
						<line number="282" hits="0"/>
						<line number="283" hits="0"/>
						<line number="285" hits="0"/>
						<line number="286" hits="0"/>
						<line number="288" hits="0"/>
						<line number="289" hits="0"/>
						<line number="291" hits="0"/>
						<line number="293" hits="1"/>
						<line number="300" hits="0"/>
						<line number="302" hits="0"/>
						<line number="303" hits="0"/>
						<line number="304" hits="0"/>
						<line number="307" hits="0"/>
						<line number="309" hits="0"/>
						<line number="314" hits="1"/>
						<line number="316" hits="0"/>
						<line number="317" hits="0"/>
					</lines>
				</class>
				<class name="exceptions.py" filename="core/exceptions.py" complexity="0" line-rate="0.4766" branch-rate="0">
					<methods/>
					<lines>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="22" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="39" hits="1"/>
						<line number="42" hits="1"/>
						<line number="51" hits="1"/>
						<line number="73" hits="0"/>
						<line number="74" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="77" hits="0"/>
						<line number="78" hits="0"/>
						<line number="79" hits="0"/>
						<line number="81" hits="1"/>
						<line number="88" hits="0"/>
						<line number="101" hits="1"/>
						<line number="116" hits="1"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="139" hits="0"/>
						<line number="140" hits="0"/>
						<line number="141" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="146" hits="0"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="149" hits="0"/>
						<line number="151" hits="0"/>
						<line number="152" hits="0"/>
						<line number="155" hits="1"/>
						<line number="167" hits="1"/>
						<line number="186" hits="0"/>
						<line number="187" hits="0"/>
						<line number="188" hits="0"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="198" hits="0"/>
						<line number="202" hits="1"/>
						<line number="208" hits="1"/>
						<line number="210" hits="0"/>
						<line number="211" hits="0"/>
						<line number="214" hits="1"/>
						<line number="221" hits="1"/>
						<line number="240" hits="0"/>
						<line number="241" hits="0"/>
						<line number="242" hits="0"/>
						<line number="243" hits="0"/>
						<line number="244" hits="0"/>
						<line number="247" hits="0"/>
						<line number="248" hits="0"/>
						<line number="249" hits="0"/>
						<line number="250" hits="0"/>
						<line number="252" hits="0"/>
						<line number="255" hits="1"/>
						<line number="263" hits="1"/>
						<line number="271" hits="1"/>
						<line number="284" hits="1"/>
						<line number="300" hits="0"/>
						<line number="301" hits="0"/>
						<line number="302" hits="0"/>
						<line number="305" hits="1"/>
						<line number="314" hits="1"/>
						<line number="327" hits="1"/>
						<line number="345" hits="0"/>
						<line number="346" hits="0"/>
						<line number="347" hits="0"/>
						<line number="348" hits="0"/>
						<line number="352" hits="1"/>
						<line number="361" hits="1"/>
						<line number="370" hits="1"/>
						<line number="378" hits="1"/>
						<line number="387" hits="1"/>
						<line number="398" hits="1"/>
						<line number="402" hits="1"/>
						<line number="410" hits="1"/>
						<line number="418" hits="1"/>
						<line number="435" hits="1"/>
						<line number="453" hits="0"/>
						<line number="454" hits="0"/>
						<line number="455" hits="0"/>
						<line number="456" hits="0"/>
						<line number="460" hits="1"/>
						<line number="472" hits="1"/>
						<line number="483" hits="0"/>
						<line number="484" hits="0"/>
						<line number="487" hits="1"/>
						<line number="497" hits="1"/>
						<line number="510" hits="1"/>
						<line number="518" hits="1"/>
						<line number="528" hits="1"/>
						<line number="537" hits="1"/>
						<line number="547" hits="1"/>
						<line number="558" hits="1"/>
					</lines>
				</class>
				<class name="stability.py" filename="core/stability.py" complexity="0" line-rate="0.4203" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="19" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="52" hits="1"/>
						<line number="78" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="84" hits="0"/>
						<line number="85" hits="0"/>
						<line number="86" hits="0"/>
						<line number="87" hits="0"/>
						<line number="93" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="99" hits="0"/>
						<line number="101" hits="0"/>
						<line number="103" hits="0"/>
						<line number="106" hits="1"/>
						<line number="126" hits="0"/>
						<line number="129" hits="1"/>
						<line number="149" hits="0"/>
						<line number="152" hits="1"/>
						<line number="172" hits="0"/>
						<line number="175" hits="1"/>
						<line number="192" hits="0"/>
						<line number="195" hits="1"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="217" hits="0"/>
						<line number="223" hits="1"/>
						<line number="239" hits="0"/>
						<line number="242" hits="1"/>
						<line number="258" hits="0"/>
						<line number="261" hits="1"/>
						<line number="277" hits="0"/>
						<line number="280" hits="1"/>
						<line number="295" hits="1"/>
						<line number="297" hits="0"/>
						<line number="300" hits="0"/>
						<line number="302" hits="0"/>
						<line number="304" hits="0"/>
						<line number="306" hits="0"/>
						<line number="307" hits="0"/>
						<line number="308" hits="0"/>
						<line number="314" hits="0"/>
						<line number="317" hits="0"/>
						<line number="320" hits="1"/>
						<line number="338" hits="0"/>
						<line number="340" hits="0"/>
						<line number="342" hits="0"/>
						<line number="345" hits="0"/>
						<line number="352" hits="0"/>
						<line number="353" hits="0"/>
						<line number="355" hits="0"/>
						<line number="359" hits="1"/>
					</lines>
				</class>
				<class name="types.py" filename="core/types.py" complexity="0" line-rate="0.987" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="32" hits="1"/>
						<line number="35" hits="1"/>
						<line number="38" hits="1"/>
						<line number="41" hits="1"/>
						<line number="46" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="81" hits="1"/>
						<line number="84" hits="1"/>
						<line number="87" hits="1"/>
						<line number="92" hits="1"/>
						<line number="95" hits="1"/>
						<line number="98" hits="1"/>
						<line number="103" hits="1"/>
						<line number="106" hits="1"/>
						<line number="109" hits="1"/>
						<line number="112" hits="1"/>
						<line number="120" hits="1"/>
						<line number="121" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="125" hits="1"/>
						<line number="126" hits="1"/>
						<line number="127" hits="1"/>
						<line number="128" hits="1"/>
						<line number="130" hits="1"/>
						<line number="131" hits="1"/>
						<line number="133" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="138" hits="1"/>
						<line number="139" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="143" hits="1"/>
						<line number="144" hits="1"/>
						<line number="147" hits="1"/>
						<line number="148" hits="1"/>
						<line number="149" hits="1"/>
						<line number="150" hits="1"/>
						<line number="151" hits="1"/>
						<line number="156" hits="1"/>
						<line number="159" hits="1"/>
						<line number="162" hits="1"/>
						<line number="167" hits="1"/>
						<line number="170" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1"/>
						<line number="182" hits="1"/>
						<line number="186" hits="1"/>
						<line number="190" hits="1"/>
						<line number="194" hits="1"/>
						<line number="198" hits="1"/>
						<line number="203" hits="1"/>
						<line number="206" hits="1"/>
						<line number="209" hits="1"/>
						<line number="212" hits="1"/>
						<line number="218" hits="1"/>
						<line number="221" hits="1"/>
						<line number="224" hits="1"/>
						<line number="227" hits="1"/>
						<line number="231" hits="1"/>
						<line number="232" hits="1"/>
						<line number="234" hits="1"/>
						<line number="235" hits="1"/>
						<line number="236" hits="1"/>
						<line number="237" hits="1"/>
						<line number="239" hits="1"/>
						<line number="240" hits="1"/>
						<line number="242" hits="1"/>
						<line number="243" hits="1"/>
						<line number="244" hits="1"/>
						<line number="246" hits="1"/>
						<line number="247" hits="1"/>
						<line number="249" hits="1"/>
						<line number="250" hits="1"/>
						<line number="251" hits="1"/>
						<line number="252" hits="1"/>
						<line number="253" hits="1"/>
						<line number="255" hits="1"/>
						<line number="256" hits="1"/>
						<line number="258" hits="1"/>
						<line number="259" hits="1"/>
						<line number="260" hits="1"/>
						<line number="261" hits="1"/>
						<line number="262" hits="1"/>
						<line number="264" hits="1"/>
						<line number="265" hits="1"/>
						<line number="267" hits="1"/>
						<line number="268" hits="1"/>
						<line number="269" hits="1"/>
						<line number="270" hits="1"/>
						<line number="271" hits="1"/>
						<line number="272" hits="1"/>
						<line number="273" hits="1"/>
						<line number="275" hits="1"/>
						<line number="276" hits="1"/>
						<line number="278" hits="1"/>
						<line number="279" hits="1"/>
						<line number="280" hits="1"/>
						<line number="281" hits="1"/>
						<line number="283" hits="1"/>
						<line number="284" hits="1"/>
						<line number="286" hits="1"/>
						<line number="287" hits="1"/>
						<line number="288" hits="1"/>
						<line number="289" hits="1"/>
						<line number="291" hits="1"/>
						<line number="292" hits="1"/>
						<line number="294" hits="1"/>
						<line number="295" hits="1"/>
						<line number="296" hits="1"/>
						<line number="297" hits="1"/>
						<line number="298" hits="1"/>
						<line number="304" hits="1"/>
						<line number="305" hits="1"/>
						<line number="306" hits="1"/>
						<line number="307" hits="1"/>
						<line number="311" hits="1"/>
						<line number="312" hits="1"/>
						<line number="314" hits="1"/>
						<line number="315" hits="1"/>
						<line number="316" hits="1"/>
						<line number="317" hits="1"/>
						<line number="318" hits="1"/>
						<line number="319" hits="1"/>
						<line number="320" hits="1"/>
						<line number="321" hits="1"/>
						<line number="322" hits="1"/>
						<line number="323" hits="1"/>
						<line number="325" hits="1"/>
						<line number="326" hits="1"/>
						<line number="328" hits="1"/>
						<line number="329" hits="1"/>
						<line number="330" hits="1"/>
						<line number="331" hits="1"/>
						<line number="332" hits="1"/>
						<line number="334" hits="1"/>
						<line number="335" hits="1"/>
						<line number="337" hits="1"/>
						<line number="338" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="discovery" line-rate="0.3924" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="discovery/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1"/>
					</lines>
				</class>
				<class name="discovery.py" filename="discovery/discovery.py" complexity="0" line-rate="0.1792" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="20" hits="1"/>
						<line number="21" hits="0"/>
						<line number="24" hits="1"/>
						<line number="33" hits="1"/>
						<line number="40" hits="1"/>
						<line number="41" hits="1"/>
						<line number="43" hits="1"/>
						<line number="60" hits="0"/>
						<line number="62" hits="0"/>
						<line number="63" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="80" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="86" hits="0"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="91" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="99" hits="0"/>
						<line number="100" hits="0"/>
						<line number="101" hits="0"/>
						<line number="102" hits="0"/>
						<line number="104" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="115" hits="1"/>
						<line number="133" hits="0"/>
						<line number="136" hits="0"/>
						<line number="139" hits="0"/>
						<line number="142" hits="0"/>
						<line number="143" hits="0"/>
						<line number="144" hits="0"/>
						<line number="145" hits="0"/>
						<line number="146" hits="0"/>
						<line number="147" hits="0"/>
						<line number="149" hits="0"/>
						<line number="150" hits="0"/>
						<line number="156" hits="0"/>
						<line number="161" hits="0"/>
						<line number="162" hits="0"/>
						<line number="164" hits="0"/>
						<line number="173" hits="1"/>
						<line number="192" hits="0"/>
						<line number="195" hits="0"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="199" hits="0"/>
						<line number="202" hits="0"/>
						<line number="204" hits="0"/>
						<line number="205" hits="0"/>
						<line number="206" hits="0"/>
						<line number="208" hits="0"/>
						<line number="209" hits="0"/>
						<line number="210" hits="0"/>
						<line number="213" hits="0"/>
						<line number="214" hits="0"/>
						<line number="215" hits="0"/>
						<line number="216" hits="0"/>
						<line number="221" hits="0"/>
						<line number="223" hits="0"/>
						<line number="225" hits="0"/>
						<line number="226" hits="0"/>
						<line number="227" hits="0"/>
						<line number="228" hits="0"/>
						<line number="230" hits="0"/>
						<line number="231" hits="0"/>
						<line number="236" hits="0"/>
						<line number="239" hits="0"/>
						<line number="240" hits="0"/>
						<line number="241" hits="0"/>
						<line number="246" hits="0"/>
						<line number="248" hits="1"/>
						<line number="263" hits="0"/>
						<line number="264" hits="0"/>
						<line number="265" hits="0"/>
						<line number="267" hits="0"/>
						<line number="271" hits="0"/>
						<line number="272" hits="0"/>
						<line number="273" hits="0"/>
						<line number="274" hits="0"/>
						<line number="275" hits="0"/>
						<line number="276" hits="0"/>
						<line number="277" hits="0"/>
						<line number="278" hits="0"/>
						<line number="279" hits="0"/>
						<line number="281" hits="1"/>
						<line number="303" hits="0"/>
						<line number="305" hits="0"/>
						<line number="306" hits="0"/>
						<line number="308" hits="0"/>
					</lines>
				</class>
				<class name="models.py" filename="discovery/models.py" complexity="0" line-rate="0.8163" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="39" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="45" hits="1"/>
						<line number="47" hits="0"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="71" hits="1"/>
						<line number="72" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="83" hits="1"/>
						<line number="84" hits="1"/>
						<line number="86" hits="1"/>
						<line number="88" hits="0"/>
						<line number="89" hits="0"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="98" hits="1"/>
						<line number="99" hits="1"/>
						<line number="101" hits="0"/>
						<line number="103" hits="1"/>
						<line number="104" hits="1"/>
						<line number="106" hits="0"/>
						<line number="108" hits="1"/>
						<line number="110" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="experimental" line-rate="0.06962" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="experimental/__init__.py" complexity="0" line-rate="0.6471" branch-rate="0">
					<methods/>
					<lines>
						<line number="23" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="29" hits="1"/>
						<line number="38" hits="1"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="86" hits="0"/>
						<line number="88" hits="1"/>
						<line number="91" hits="1"/>
						<line number="98" hits="0"/>
						<line number="101" hits="1"/>
						<line number="111" hits="0"/>
					</lines>
				</class>
				<class name="events.py" filename="experimental/events.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="15" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="21" hits="0"/>
						<line number="24" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="47" hits="0"/>
						<line number="57" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="62" hits="0"/>
						<line number="79" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="94" hits="0"/>
						<line number="96" hits="0"/>
						<line number="111" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="119" hits="0"/>
						<line number="121" hits="0"/>
						<line number="133" hits="0"/>
						<line number="134" hits="0"/>
						<line number="135" hits="0"/>
						<line number="137" hits="0"/>
						<line number="144" hits="0"/>
						<line number="145" hits="0"/>
						<line number="146" hits="0"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="151" hits="0"/>
						<line number="154" hits="0"/>
						<line number="161" hits="0"/>
						<line number="163" hits="0"/>
						<line number="165" hits="0"/>
						<line number="172" hits="0"/>
						<line number="174" hits="0"/>
						<line number="184" hits="0"/>
						<line number="186" hits="0"/>
						<line number="188" hits="0"/>
						<line number="227" hits="0"/>
					</lines>
				</class>
				<class name="fractal.py" filename="experimental/fractal.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="14" hits="0"/>
						<line number="16" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="28" hits="0"/>
						<line number="39" hits="0"/>
						<line number="41" hits="0"/>
						<line number="42" hits="0"/>
						<line number="44" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="55" hits="0"/>
						<line number="62" hits="0"/>
						<line number="64" hits="0"/>
						<line number="74" hits="0"/>
						<line number="76" hits="0"/>
						<line number="83" hits="0"/>
						<line number="86" hits="0"/>
						<line number="93" hits="0"/>
						<line number="95" hits="0"/>
						<line number="97" hits="0"/>
						<line number="105" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="109" hits="0"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="119" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="162" hits="0"/>
					</lines>
				</class>
				<class name="registry.py" filename="experimental/registry.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="15" hits="0"/>
						<line number="17" hits="0"/>
						<line number="18" hits="0"/>
						<line number="19" hits="0"/>
						<line number="20" hits="0"/>
						<line number="23" hits="0"/>
						<line number="31" hits="0"/>
						<line number="37" hits="0"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="43" hits="0"/>
						<line number="44" hits="0"/>
						<line number="50" hits="0"/>
						<line number="51" hits="0"/>
						<line number="52" hits="0"/>
						<line number="53" hits="0"/>
						<line number="54" hits="0"/>
						<line number="55" hits="0"/>
						<line number="56" hits="0"/>
						<line number="57" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="63" hits="0"/>
						<line number="73" hits="0"/>
						<line number="80" hits="0"/>
						<line number="81" hits="0"/>
						<line number="82" hits="0"/>
						<line number="84" hits="0"/>
						<line number="97" hits="0"/>
						<line number="99" hits="0"/>
						<line number="109" hits="0"/>
						<line number="111" hits="0"/>
						<line number="125" hits="0"/>
						<line number="127" hits="0"/>
						<line number="140" hits="0"/>
						<line number="142" hits="0"/>
						<line number="149" hits="0"/>
						<line number="151" hits="0"/>
						<line number="163" hits="0"/>
						<line number="165" hits="0"/>
						<line number="172" hits="0"/>
						<line number="175" hits="0"/>
						<line number="182" hits="0"/>
						<line number="184" hits="0"/>
						<line number="186" hits="0"/>
						<line number="193" hits="0"/>
						<line number="194" hits="0"/>
						<line number="196" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="206" hits="0"/>
						<line number="216" hits="0"/>
						<line number="251" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="logging" line-rate="1" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="logging/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="21" hits="1"/>
					</lines>
				</class>
				<class name="analyzer.py" filename="logging/analyzer.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="30" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="50" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="55" hits="1"/>
						<line number="56" hits="1"/>
						<line number="66" hits="1"/>
						<line number="67" hits="1"/>
						<line number="69" hits="1"/>
						<line number="88" hits="1"/>
						<line number="89" hits="1"/>
						<line number="99" hits="1"/>
						<line number="102" hits="1"/>
						<line number="103" hits="1"/>
						<line number="105" hits="1"/>
						<line number="113" hits="1"/>
						<line number="114" hits="1"/>
						<line number="115" hits="1"/>
						<line number="121" hits="1"/>
						<line number="123" hits="1"/>
						<line number="124" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="138" hits="1"/>
						<line number="140" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="144" hits="1"/>
						<line number="156" hits="1"/>
						<line number="157" hits="1"/>
						<line number="171" hits="1"/>
						<line number="174" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="179" hits="1"/>
						<line number="180" hits="1"/>
						<line number="181" hits="1"/>
						<line number="184" hits="1"/>
						<line number="185" hits="1"/>
						<line number="186" hits="1"/>
						<line number="189" hits="1"/>
						<line number="190" hits="1"/>
						<line number="192" hits="1"/>
					</lines>
				</class>
				<class name="structured_logger.py" filename="logging/structured_logger.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="18" hits="1"/>
						<line number="19" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="30" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="41" hits="1"/>
						<line number="42" hits="1"/>
						<line number="43" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="48" hits="1"/>
						<line number="49" hits="1"/>
						<line number="52" hits="1"/>
						<line number="53" hits="1"/>
						<line number="54" hits="1"/>
						<line number="57" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="66" hits="1"/>
						<line number="74" hits="1"/>
						<line number="90" hits="1"/>
						<line number="91" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="96" hits="1"/>
						<line number="98" hits="1"/>
						<line number="100" hits="1"/>
						<line number="120" hits="1"/>
						<line number="122" hits="1"/>
						<line number="126" hits="1"/>
						<line number="129" hits="1"/>
						<line number="141" hits="1"/>
						<line number="142" hits="1"/>
						<line number="145" hits="1"/>
						<line number="150" hits="1"/>
						<line number="166" hits="1"/>
						<line number="172" hits="1"/>
						<line number="173" hits="1"/>
						<line number="175" hits="1"/>
						<line number="187" hits="1"/>
						<line number="201" hits="1"/>
						<line number="215" hits="1"/>
						<line number="225" hits="1"/>
						<line number="228" hits="1"/>
						<line number="231" hits="1"/>
						<line number="239" hits="1"/>
						<line number="240" hits="1"/>
						<line number="241" hits="1"/>
						<line number="243" hits="1"/>
						<line number="245" hits="1"/>
						<line number="246" hits="1"/>
						<line number="248" hits="1"/>
						<line number="250" hits="1"/>
						<line number="251" hits="1"/>
						<line number="252" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="manifest" line-rate="0.1846" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="manifest/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
					</lines>
				</class>
				<class name="converter.py" filename="manifest/converter.py" complexity="0" line-rate="0.1719" branch-rate="0">
					<methods/>
					<lines>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="26" hits="1"/>
						<line number="28" hits="1"/>
						<line number="29" hits="1"/>
						<line number="31" hits="1"/>
						<line number="32" hits="1"/>
						<line number="44" hits="1"/>
						<line number="53" hits="1"/>
						<line number="60" hits="1"/>
						<line number="62" hits="1"/>
						<line number="75" hits="0"/>
						<line number="77" hits="0"/>
						<line number="79" hits="0"/>
						<line number="80" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="84" hits="0"/>
						<line number="86" hits="0"/>
						<line number="89" hits="0"/>
						<line number="91" hits="0"/>
						<line number="92" hits="0"/>
						<line number="93" hits="0"/>
						<line number="94" hits="0"/>
						<line number="95" hits="0"/>
						<line number="96" hits="0"/>
						<line number="98" hits="1"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="117" hits="0"/>
						<line number="119" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="124" hits="0"/>
						<line number="126" hits="1"/>
						<line number="140" hits="0"/>
						<line number="142" hits="0"/>
						<line number="144" hits="0"/>
						<line number="147" hits="0"/>
						<line number="157" hits="0"/>
						<line number="160" hits="0"/>
						<line number="161" hits="0"/>
						<line number="164" hits="0"/>
						<line number="169" hits="0"/>
						<line number="172" hits="0"/>
						<line number="175" hits="0"/>
						<line number="178" hits="0"/>
						<line number="181" hits="0"/>
						<line number="191" hits="0"/>
						<line number="192" hits="0"/>
						<line number="194" hits="0"/>
						<line number="196" hits="0"/>
						<line number="197" hits="0"/>
						<line number="198" hits="0"/>
						<line number="199" hits="0"/>
						<line number="201" hits="0"/>
						<line number="202" hits="0"/>
						<line number="203" hits="0"/>
						<line number="204" hits="0"/>
						<line number="206" hits="1"/>
						<line number="209" hits="0"/>
						<line number="211" hits="0"/>
						<line number="222" hits="1"/>
						<line number="224" hits="0"/>
						<line number="230" hits="1"/>
						<line number="232" hits="0"/>
						<line number="234" hits="0"/>
						<line number="236" hits="0"/>
						<line number="237" hits="0"/>
						<line number="238" hits="0"/>
						<line number="241" hits="0"/>
						<line number="244" hits="0"/>
						<line number="247" hits="0"/>
						<line number="249" hits="0"/>
						<line number="258" hits="0"/>
						<line number="260" hits="1"/>
						<line number="262" hits="0"/>
						<line number="264" hits="0"/>
						<line number="266" hits="0"/>
						<line number="267" hits="0"/>
						<line number="269" hits="0"/>
						<line number="270" hits="0"/>
						<line number="272" hits="0"/>
						<line number="275" hits="0"/>
						<line number="277" hits="0"/>
						<line number="284" hits="0"/>
						<line number="286" hits="1"/>
						<line number="294" hits="0"/>
						<line number="295" hits="0"/>
						<line number="296" hits="0"/>
						<line number="297" hits="0"/>
						<line number="298" hits="0"/>
						<line number="299" hits="0"/>
						<line number="300" hits="0"/>
						<line number="301" hits="0"/>
						<line number="303" hits="0"/>
						<line number="304" hits="0"/>
						<line number="307" hits="0"/>
						<line number="308" hits="0"/>
						<line number="311" hits="0"/>
						<line number="312" hits="0"/>
						<line number="314" hits="0"/>
						<line number="316" hits="0"/>
						<line number="318" hits="1"/>
						<line number="320" hits="0"/>
						<line number="321" hits="0"/>
						<line number="322" hits="0"/>
						<line number="323" hits="0"/>
						<line number="324" hits="0"/>
						<line number="326" hits="1"/>
						<line number="328" hits="0"/>
						<line number="330" hits="0"/>
						<line number="332" hits="0"/>
						<line number="333" hits="0"/>
						<line number="336" hits="0"/>
						<line number="339" hits="0"/>
						<line number="342" hits="0"/>
						<line number="350" hits="0"/>
						<line number="359" hits="0"/>
						<line number="361" hits="1"/>
						<line number="363" hits="0"/>
						<line number="365" hits="0"/>
						<line number="366" hits="0"/>
						<line number="373" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="plugins" line-rate="0.1462" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="plugins/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="23" hits="1"/>
					</lines>
				</class>
				<class name="core_loader.py" filename="plugins/core_loader.py" complexity="0" line-rate="0.2951" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="20" hits="1"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="26" hits="1"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="38" hits="0"/>
						<line number="40" hits="1"/>
						<line number="46" hits="0"/>
						<line number="48" hits="1"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="65" hits="0"/>
						<line number="67" hits="0"/>
						<line number="69" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="80" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="87" hits="1"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="100" hits="1"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="111" hits="0"/>
						<line number="115" hits="1"/>
						<line number="118" hits="1"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="130" hits="1"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="141" hits="1"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="152" hits="1"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="172" hits="1"/>
						<line number="187" hits="0"/>
						<line number="188" hits="0"/>
						<line number="189" hits="0"/>
					</lines>
				</class>
				<class name="internal_loader.py" filename="plugins/internal_loader.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="0"/>
						<line number="9" hits="0"/>
						<line number="11" hits="0"/>
						<line number="12" hits="0"/>
						<line number="14" hits="0"/>
						<line number="17" hits="0"/>
						<line number="20" hits="0"/>
						<line number="22" hits="0"/>
						<line number="23" hits="0"/>
						<line number="24" hits="0"/>
						<line number="26" hits="0"/>
						<line number="28" hits="0"/>
						<line number="29" hits="0"/>
						<line number="30" hits="0"/>
						<line number="32" hits="0"/>
						<line number="33" hits="0"/>
						<line number="34" hits="0"/>
						<line number="35" hits="0"/>
						<line number="36" hits="0"/>
						<line number="38" hits="0"/>
						<line number="40" hits="0"/>
						<line number="46" hits="0"/>
						<line number="48" hits="0"/>
						<line number="58" hits="0"/>
						<line number="59" hits="0"/>
						<line number="60" hits="0"/>
						<line number="65" hits="0"/>
						<line number="67" hits="0"/>
						<line number="69" hits="0"/>
						<line number="75" hits="0"/>
						<line number="76" hits="0"/>
						<line number="80" hits="0"/>
						<line number="82" hits="0"/>
						<line number="83" hits="0"/>
						<line number="87" hits="0"/>
						<line number="97" hits="0"/>
						<line number="98" hits="0"/>
						<line number="100" hits="0"/>
						<line number="106" hits="0"/>
						<line number="107" hits="0"/>
						<line number="108" hits="0"/>
						<line number="109" hits="0"/>
						<line number="111" hits="0"/>
						<line number="115" hits="0"/>
						<line number="118" hits="0"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="127" hits="0"/>
						<line number="130" hits="0"/>
						<line number="137" hits="0"/>
						<line number="138" hits="0"/>
						<line number="141" hits="0"/>
						<line number="147" hits="0"/>
						<line number="148" hits="0"/>
						<line number="151" hits="0"/>
						<line number="160" hits="0"/>
						<line number="164" hits="0"/>
						<line number="166" hits="0"/>
						<line number="167" hits="0"/>
						<line number="168" hits="0"/>
						<line number="169" hits="0"/>
						<line number="171" hits="0"/>
						<line number="175" hits="0"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="193" hits="0"/>
						<line number="206" hits="0"/>
						<line number="207" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="plugins.core" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="plugins/core/__init__.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="32" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="plugins.core.json_validator" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="plugins/core/json_validator/__init__.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="0"/>
						<line number="10" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="plugins.core.llm_provider" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="plugins/core/llm_provider/__init__.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="0"/>
						<line number="10" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="plugins.core.wiring_analyzer" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="plugins/core/wiring_analyzer/__init__.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="0"/>
						<line number="10" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="plugins.internal" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="plugins/internal/__init__.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="29" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="plugins.internal.ai_orchestrator" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="plugins/internal/ai_orchestrator/__init__.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="0"/>
						<line number="10" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="plugins.internal.plugin_generator" line-rate="0" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="plugins/internal/plugin_generator/__init__.py" complexity="0" line-rate="0" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="0"/>
						<line number="10" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="schemas" line-rate="0.5538" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="schemas/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="32" hits="1"/>
						<line number="38" hits="1"/>
						<line number="51" hits="1"/>
					</lines>
				</class>
				<class name="generator.py" filename="schemas/generator.py" complexity="0" line-rate="0.1633" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="10" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="14" hits="1"/>
						<line number="17" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="0"/>
						<line number="41" hits="1"/>
						<line number="43" hits="0"/>
						<line number="45" hits="0"/>
						<line number="46" hits="0"/>
						<line number="47" hits="0"/>
						<line number="50" hits="0"/>
						<line number="55" hits="0"/>
						<line number="66" hits="1"/>
						<line number="68" hits="0"/>
						<line number="70" hits="0"/>
						<line number="71" hits="0"/>
						<line number="72" hits="0"/>
						<line number="75" hits="0"/>
						<line number="80" hits="0"/>
						<line number="91" hits="1"/>
						<line number="93" hits="0"/>
						<line number="96" hits="0"/>
						<line number="98" hits="0"/>
						<line number="100" hits="0"/>
						<line number="102" hits="0"/>
						<line number="108" hits="0"/>
						<line number="110" hits="0"/>
						<line number="112" hits="0"/>
						<line number="113" hits="0"/>
						<line number="114" hits="0"/>
						<line number="115" hits="0"/>
						<line number="116" hits="0"/>
						<line number="117" hits="0"/>
						<line number="118" hits="0"/>
						<line number="124" hits="0"/>
						<line number="129" hits="0"/>
						<line number="132" hits="1"/>
						<line number="134" hits="0"/>
						<line number="136" hits="0"/>
						<line number="138" hits="0"/>
						<line number="139" hits="0"/>
						<line number="141" hits="0"/>
						<line number="144" hits="1"/>
						<line number="178" hits="0"/>
						<line number="179" hits="0"/>
						<line number="180" hits="0"/>
						<line number="182" hits="0"/>
						<line number="183" hits="0"/>
						<line number="186" hits="0"/>
						<line number="189" hits="0"/>
						<line number="190" hits="0"/>
						<line number="193" hits="0"/>
						<line number="205" hits="0"/>
						<line number="212" hits="0"/>
						<line number="215" hits="0"/>
						<line number="216" hits="0"/>
						<line number="217" hits="0"/>
						<line number="218" hits="0"/>
						<line number="219" hits="0"/>
						<line number="220" hits="0"/>
						<line number="223" hits="0"/>
						<line number="224" hits="0"/>
						<line number="225" hits="0"/>
						<line number="226" hits="0"/>
						<line number="227" hits="0"/>
						<line number="228" hits="0"/>
						<line number="231" hits="0"/>
						<line number="233" hits="0"/>
						<line number="245" hits="1"/>
						<line number="271" hits="0"/>
						<line number="274" hits="0"/>
						<line number="275" hits="0"/>
						<line number="276" hits="0"/>
						<line number="277" hits="0"/>
						<line number="279" hits="0"/>
						<line number="280" hits="0"/>
						<line number="282" hits="0"/>
						<line number="294" hits="1"/>
						<line number="304" hits="0"/>
						<line number="307" hits="0"/>
						<line number="310" hits="0"/>
						<line number="311" hits="0"/>
						<line number="313" hits="0"/>
						<line number="315" hits="0"/>
						<line number="322" hits="0"/>
						<line number="325" hits="1"/>
						<line number="339" hits="0"/>
						<line number="341" hits="0"/>
						<line number="342" hits="0"/>
						<line number="343" hits="0"/>
						<line number="344" hits="0"/>
						<line number="345" hits="0"/>
						<line number="346" hits="0"/>
						<line number="347" hits="0"/>
						<line number="351" hits="1"/>
					</lines>
				</class>
				<class name="manifest.py" filename="schemas/manifest.py" complexity="0" line-rate="0.9468" branch-rate="0">
					<methods/>
					<lines>
						<line number="9" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="18" hits="1"/>
						<line number="21" hits="1"/>
						<line number="22" hits="1"/>
						<line number="24" hits="1"/>
						<line number="25" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="31" hits="1"/>
						<line number="34" hits="1"/>
						<line number="35" hits="1"/>
						<line number="36" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="1"/>
						<line number="41" hits="1"/>
						<line number="44" hits="1"/>
						<line number="45" hits="1"/>
						<line number="46" hits="1"/>
						<line number="47" hits="1"/>
						<line number="49" hits="1"/>
						<line number="55" hits="1"/>
						<line number="58" hits="1"/>
						<line number="59" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="62" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="66" hits="1"/>
						<line number="72" hits="1"/>
						<line number="75" hits="1"/>
						<line number="76" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="79" hits="1"/>
						<line number="80" hits="1"/>
						<line number="81" hits="1"/>
						<line number="83" hits="1"/>
						<line number="89" hits="1"/>
						<line number="92" hits="1"/>
						<line number="93" hits="1"/>
						<line number="94" hits="1"/>
						<line number="95" hits="1"/>
						<line number="97" hits="1"/>
						<line number="103" hits="1"/>
						<line number="106" hits="1"/>
						<line number="107" hits="1"/>
						<line number="108" hits="1"/>
						<line number="109" hits="1"/>
						<line number="110" hits="1"/>
						<line number="111" hits="1"/>
						<line number="112" hits="1"/>
						<line number="113" hits="1"/>
						<line number="115" hits="1"/>
						<line number="116" hits="1"/>
						<line number="117" hits="1"/>
						<line number="119" hits="0"/>
						<line number="120" hits="0"/>
						<line number="121" hits="0"/>
						<line number="122" hits="0"/>
						<line number="123" hits="0"/>
						<line number="125" hits="1"/>
						<line number="131" hits="1"/>
						<line number="134" hits="1"/>
						<line number="135" hits="1"/>
						<line number="136" hits="1"/>
						<line number="138" hits="1"/>
						<line number="144" hits="1"/>
						<line number="154" hits="1"/>
						<line number="157" hits="1"/>
						<line number="160" hits="1"/>
						<line number="163" hits="1"/>
						<line number="166" hits="1"/>
						<line number="169" hits="1"/>
						<line number="172" hits="1"/>
						<line number="175" hits="1"/>
						<line number="176" hits="1"/>
						<line number="178" hits="1"/>
						<line number="184" hits="1"/>
						<line number="193" hits="1"/>
						<line number="196" hits="1"/>
						<line number="197" hits="1"/>
						<line number="198" hits="1"/>
						<line number="201" hits="1"/>
						<line number="204" hits="1"/>
						<line number="210" hits="1"/>
						<line number="216" hits="1"/>
						<line number="217" hits="1"/>
						<line number="219" hits="1"/>
						<line number="226" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="schemas.json" line-rate="0.4375" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="schemas/json/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="9" hits="1"/>
						<line number="15" hits="1"/>
					</lines>
				</class>
				<class name="app_graph.py" filename="schemas/json/app_graph.py" complexity="0" line-rate="0.3571" branch-rate="0">
					<methods/>
					<lines>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="12" hits="1"/>
						<line number="430" hits="1"/>
						<line number="440" hits="0"/>
						<line number="441" hits="0"/>
						<line number="443" hits="0"/>
						<line number="444" hits="0"/>
						<line number="446" hits="0"/>
						<line number="448" hits="0"/>
						<line number="449" hits="0"/>
						<line number="451" hits="0"/>
						<line number="454" hits="1"/>
						<line number="461" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
		<package name="services" line-rate="0.5581" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="services/__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="11" hits="1"/>
						<line number="13" hits="1"/>
						<line number="14" hits="1"/>
						<line number="15" hits="1"/>
						<line number="17" hits="1"/>
					</lines>
				</class>
				<class name="error_service.py" filename="services/error_service.py" complexity="0" line-rate="0.5" branch-rate="0">
					<methods/>
					<lines>
						<line number="10" hits="1"/>
						<line number="12" hits="1"/>
						<line number="13" hits="1"/>
						<line number="15" hits="1"/>
						<line number="16" hits="1"/>
						<line number="17" hits="1"/>
						<line number="19" hits="1"/>
						<line number="20" hits="0"/>
						<line number="23" hits="1"/>
						<line number="31" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="36" hits="1"/>
						<line number="55" hits="0"/>
						<line number="61" hits="0"/>
						<line number="67" hits="1"/>
						<line number="86" hits="0"/>
						<line number="93" hits="0"/>
						<line number="99" hits="1"/>
						<line number="118" hits="0"/>
						<line number="125" hits="0"/>
						<line number="126" hits="0"/>
						<line number="128" hits="0"/>
						<line number="134" hits="1"/>
						<line number="153" hits="0"/>
						<line number="160" hits="0"/>
						<line number="166" hits="1"/>
						<line number="183" hits="0"/>
						<line number="188" hits="0"/>
						<line number="194" hits="1"/>
						<line number="213" hits="0"/>
						<line number="220" hits="0"/>
						<line number="226" hits="1"/>
						<line number="233" hits="0"/>
						<line number="235" hits="1"/>
						<line number="245" hits="0"/>
						<line number="250" hits="0"/>
						<line number="251" hits="0"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
