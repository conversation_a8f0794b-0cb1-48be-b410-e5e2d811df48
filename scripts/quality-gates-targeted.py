#!/usr/bin/env python3
"""
Plugginger Quality Gates - Targeted File Analysis
Third line of defence after ruff and mypy for specific files.
Standalone version with no external dependencies.
"""

import argparse
import ast
import re
import sys
from collections import defaultdict
from pathlib import Path
from typing import Any

# Quality gate thresholds
MAX_METHOD_LINES = 50
MAX_CODE_DUPLICATION_SIMILARITY = 0.8  # 80% similarity threshold
MIN_LINE_COUNT_FOR_DUPLICATION = 10
MAGIC_NUMBER_EXCEPTIONS = {0, 1, 2, -1, 100, 1000}  # Common acceptable numbers


class Violation:
    """Represents a code quality violation."""
    
    def __init__(self, violation_type: str, file_path: str, line_number: int, 
                 message: str, severity: str = "ERROR") -> None:
        self.type = violation_type
        self.file = file_path
        self.line = line_number
        self.message = message
        self.severity = severity
    
    def __str__(self) -> str:
        return f"{self.severity}: {self.file}:{self.line} - {self.message}"


class TargetedQualityAnalyzer:
    """Quality analyzer for specific files only."""
    
    def __init__(self, max_method_lines: int = 50) -> None:
        self.violations: list[Violation] = []
        self.max_method_lines = max_method_lines
        
    def analyze_files(self, file_paths: list[Path]) -> list[Violation]:
        """Analyze only the specified files."""
        
        # Filter to existing Python files
        py_files = [f for f in file_paths if f.exists() and f.suffix == '.py']
        
        if not py_files:
            print("⚠️  No valid Python files provided")
            return []
        
        print(f"🔍 Running Targeted Quality Gates on {len(py_files)} files...")
        
        for py_file in py_files:
            print(f"  → Analyzing {py_file}")
            self._analyze_single_file(py_file)
        
        return self.violations
    
    def _analyze_single_file(self, py_file: Path) -> None:
        """Analyze a single Python file."""
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            tree = ast.parse(content)
            
            # Run all checks on this file
            self._check_function_level_imports(py_file, tree)
            self._check_method_length(py_file, tree, lines)
            self._check_magic_numbers(py_file, tree)
            self._check_naming_consistency(py_file, content)
            
        except Exception as e:
            self.violations.append(Violation(
                "PARSE_ERROR", str(py_file), 0,
                f"Failed to parse file: {e}", "WARNING"
            ))
    
    def _check_function_level_imports(self, py_file: Path, tree: ast.AST) -> None:
        """Check for function-level imports in this file."""
        visitor = FunctionImportVisitor(str(py_file))
        visitor.visit(tree)
        self.violations.extend(visitor.violations)
    
    def _check_method_length(self, py_file: Path, tree: ast.AST, lines: list[str]) -> None:
        """Check method length in this file."""
        visitor = MethodLengthVisitor(str(py_file), lines, self.max_method_lines)
        visitor.visit(tree)
        self.violations.extend(visitor.violations)
    
    def _check_magic_numbers(self, py_file: Path, tree: ast.AST) -> None:
        """Check for magic numbers in this file."""
        visitor = MagicNumberVisitor(str(py_file))
        visitor.visit(tree)
        self.violations.extend(visitor.violations)
    
    def _check_naming_consistency(self, py_file: Path, content: str) -> None:
        """Check for naming consistency issues in this file."""
        # Look for common inconsistent patterns
        inconsistencies = [
            (r'\bplugin_class\b', r'\bpluginclass\b', "plugin_class vs pluginclass"),
            (r'\bitem_class\b', r'\bplugin_class\b', "item_class vs plugin_class"),
            (r'\bapp_config\b', r'\bconfig\b', "app_config vs config"),
        ]
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            for pattern1, pattern2, description in inconsistencies:
                if re.search(pattern1, line) and re.search(pattern2, line):
                    self.violations.append(Violation(
                        "NAMING_INCONSISTENCY", str(py_file), line_num,
                        f"Inconsistent naming: {description} in same line"
                    ))


class FunctionImportVisitor(ast.NodeVisitor):
    """AST visitor to detect function-level imports."""
    
    def __init__(self, file_path: str) -> None:
        self.file_path = file_path
        self.violations: list[Violation] = []
        self.in_function = False
        self.in_type_checking = False
        self.function_stack: list[str] = []
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        self.in_function = True
        self.function_stack.append(node.name)
        self.generic_visit(node)
        self.function_stack.pop()
        self.in_function = len(self.function_stack) > 0
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        self.in_function = True
        self.function_stack.append(node.name)
        self.generic_visit(node)
        self.function_stack.pop()
        self.in_function = len(self.function_stack) > 0
    
    def visit_If(self, node: ast.If) -> None:
        # Check for TYPE_CHECKING blocks
        if (isinstance(node.test, ast.Name) and 
            node.test.id == "TYPE_CHECKING"):
            self.in_type_checking = True
            self.generic_visit(node)
            self.in_type_checking = False
        else:
            self.generic_visit(node)
    
    def visit_Import(self, node: ast.Import) -> None:
        if self.in_function and not self.in_type_checking:
            function_name = self.function_stack[-1] if self.function_stack else "unknown"
            imports = ", ".join(alias.name for alias in node.names)
            self.violations.append(Violation(
                "FUNCTION_LEVEL_IMPORT", self.file_path, node.lineno,
                f"Function-level import in '{function_name}()': import {imports}"
            ))
    
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        if self.in_function and not self.in_type_checking:
            function_name = self.function_stack[-1] if self.function_stack else "unknown"
            module = node.module or "?"
            imports = ", ".join(alias.name for alias in node.names)
            self.violations.append(Violation(
                "FUNCTION_LEVEL_IMPORT", self.file_path, node.lineno,
                f"Function-level import in '{function_name}()': from {module} import {imports}"
            ))


class MethodLengthVisitor(ast.NodeVisitor):
    """AST visitor to check method length."""
    
    def __init__(self, file_path: str, lines: list[str], max_lines: int = MAX_METHOD_LINES) -> None:
        self.file_path = file_path
        self.lines = lines
        self.violations: list[Violation] = []
        self.max_lines = max_lines
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        self._check_method_length(node)
        self.generic_visit(node)
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        self._check_method_length(node)
        self.generic_visit(node)
    
    def _check_method_length(self, node: ast.FunctionDef | ast.AsyncFunctionDef) -> None:
        start_line = node.lineno
        end_line = node.end_lineno or start_line
        
        # Count actual code lines (excluding empty lines and comments)
        code_lines = 0
        for line_num in range(start_line - 1, end_line):
            if line_num < len(self.lines):
                line = self.lines[line_num].strip()
                if line and not line.startswith('#'):
                    code_lines += 1
        
        if code_lines > self.max_lines:
            self.violations.append(Violation(
                "LONG_METHOD", self.file_path, start_line,
                f"Method '{node.name}' is {code_lines} lines long (max: {self.max_lines})"
            ))


class MagicNumberVisitor(ast.NodeVisitor):
    """AST visitor to detect magic numbers."""
    
    def __init__(self, file_path: str) -> None:
        self.file_path = file_path
        self.violations: list[Violation] = []
    
    def visit_Constant(self, node: ast.Constant) -> None:
        if isinstance(node.value, (int, float)) and node.value not in MAGIC_NUMBER_EXCEPTIONS:
            # Skip if it's in a known constant context
            parent = getattr(node, 'parent', None)
            if not self._is_constant_context(parent):
                self.violations.append(Violation(
                    "MAGIC_NUMBER", self.file_path, node.lineno,
                    f"Magic number detected: {node.value}"
                ))
    
    def _is_constant_context(self, parent: Any) -> bool:
        """Check if the number is in a context where it's acceptable."""
        # This is a simplified check - in practice, you'd want more sophisticated detection
        return False


def main() -> int:
    """Main entry point with file-based arguments."""
    parser = argparse.ArgumentParser(
        description="Targeted Quality Gates - Third line of defence after ruff and mypy",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Check single file
  python scripts/quality-gates-targeted.py src/plugginger/api/app.py
  
  # Check multiple files  
  python scripts/quality-gates-targeted.py src/plugginger/api/app.py src/plugginger/api/builder.py
  
  # Check with custom method length limit
  python scripts/quality-gates-targeted.py --max-method-lines 70 src/plugginger/api/builder.py
  
  # Integration with git (changed files)
  git diff --name-only HEAD~1 --diff-filter=AM -- "*.py" | xargs python scripts/quality-gates-targeted.py
        """
    )
    
    parser.add_argument(
        'files', 
        nargs='+', 
        type=Path,
        help='Python files to analyze'
    )
    
    parser.add_argument(
        '--max-method-lines', 
        type=int, 
        default=50,
        help='Maximum lines per method (default: 50)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output with detailed analysis'
    )
    
    parser.add_argument(
        '--json',
        action='store_true', 
        help='Output results in JSON format'
    )
    
    args = parser.parse_args()
    
    # Run targeted analysis
    analyzer = TargetedQualityAnalyzer(max_method_lines=args.max_method_lines)
    violations = analyzer.analyze_files(args.files)
    
    if not violations:
        print("✅ All targeted quality gates passed!")
        return 0
    
    # Output results
    if args.json:
        import json
        violation_data = [
            {
                "type": v.type,
                "file": v.file, 
                "line": v.line,
                "message": v.message,
                "severity": v.severity
            }
            for v in violations
        ]
        print(json.dumps(violation_data, indent=2))
    else:
        print(f"\n❌ TARGETED QUALITY GATES FAILED: {len(violations)} violations found")
        print("=" * 80)
        
        # Group by file for better readability
        by_file: dict[str, list[Violation]] = {}
        for violation in violations:
            if violation.file not in by_file:
                by_file[violation.file] = []
            by_file[violation.file].append(violation)
        
        for file_path, file_violations in by_file.items():
            print(f"\n📄 {file_path} ({len(file_violations)} issues):")
            for violation in file_violations:
                print(f"  {violation}")
        
        print("\n" + "=" * 80)
        print("💡 Fix these issues before committing")
    
    return 1


if __name__ == "__main__":
    sys.exit(main())
