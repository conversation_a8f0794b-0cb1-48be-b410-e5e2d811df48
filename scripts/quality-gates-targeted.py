#!/usr/bin/env python3
"""
Plugginger Quality Gates - Targeted File Analysis
Third line of defence after ruff and mypy for specific files.
Extended with external tool integration for comprehensive analysis.
"""

import argparse
import ast
import json
import re
import subprocess
import sys
from collections import defaultdict
from pathlib import Path
from typing import Any, Optional

# Quality gate thresholds
MAX_METHOD_LINES = 50
MAX_CODE_DUPLICATION_SIMILARITY = 0.8  # 80% similarity threshold
MIN_LINE_COUNT_FOR_DUPLICATION = 10
MAGIC_NUMBER_EXCEPTIONS = {0, 1, 2, -1, 10, 100, 1000, 3600, 86400}  # Common acceptable numbers

# External tools configuration
EXTERNAL_TOOLS = {
    'radon': {
        'cmd': ['radon', 'cc', '--min', 'C'],  # Cyclomatic complexity
        'description': 'Cyclomatic complexity analysis',
        'severity': 'WARNING'
    },
    'vulture': {
        'cmd': ['vulture', '--min-confidence', '80'],  # Dead code detection
        'description': 'Dead code detection',
        'severity': 'WARNING'
    },
    'pydeps': {
        'cmd': ['pydeps', '--show-deps'],  # Dependency analysis
        'description': 'Dependency analysis',
        'severity': 'INFO'
    },
    'pylint': {
        'cmd': ['pylint', '--disable=all', '--enable=import-error,cyclic-import'],  # Import issues only
        'description': 'Import and circular dependency detection',
        'severity': 'ERROR'
    }
}


class Violation:
    """Represents a code quality violation."""
    
    def __init__(self, violation_type: str, file_path: str, line_number: int, 
                 message: str, severity: str = "ERROR") -> None:
        self.type = violation_type
        self.file = file_path
        self.line = line_number
        self.message = message
        self.severity = severity
    
    def __str__(self) -> str:
        return f"{self.severity}: {self.file}:{self.line} - {self.message}"


class TargetedQualityAnalyzer:
    """Quality analyzer for specific files only."""

    def __init__(self, max_method_lines: int = 50, enable_external_tools: bool = False,
                 check_generics: bool = True) -> None:
        self.violations: list[Violation] = []
        self.max_method_lines = max_method_lines
        self.enable_external_tools = enable_external_tools
        self.check_generics = check_generics

    def analyze_files(self, file_paths: list[Path]) -> list[Violation]:
        """Analyze only the specified files."""

        # Filter to existing Python files
        py_files = [f for f in file_paths if f.exists() and f.suffix == '.py']

        if not py_files:
            print("⚠️  No valid Python files provided")
            return []

        print(f"🔍 Running Targeted Quality Gates on {len(py_files)} files...")
        if self.enable_external_tools:
            print("🛠️  External tools enabled: radon, vulture, pydeps, pylint")

        for py_file in py_files:
            print(f"  → Analyzing {py_file}")
            self._analyze_single_file(py_file)

        # Run external tools if enabled
        if self.enable_external_tools:
            self._run_external_tools(py_files)

        return self.violations
    
    def _analyze_single_file(self, py_file: Path) -> None:
        """Analyze a single Python file."""
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            tree = ast.parse(content)
            
            # Run all checks on this file
            self._check_function_level_imports(py_file, tree)
            self._check_method_length(py_file, tree, lines)
            self._check_magic_numbers(py_file, tree)
            self._check_naming_consistency(py_file, content)
            if self.check_generics:
                self._check_generic_usage(py_file, content)
            
        except Exception as e:
            self.violations.append(Violation(
                "PARSE_ERROR", str(py_file), 0,
                f"Failed to parse file: {e}", "WARNING"
            ))
    
    def _check_function_level_imports(self, py_file: Path, tree: ast.AST) -> None:
        """Check for function-level imports in this file."""
        visitor = FunctionImportVisitor(str(py_file))
        visitor.visit(tree)
        self.violations.extend(visitor.violations)
    
    def _check_method_length(self, py_file: Path, tree: ast.AST, lines: list[str]) -> None:
        """Check method length in this file."""
        visitor = MethodLengthVisitor(str(py_file), lines, self.max_method_lines)
        visitor.visit(tree)
        self.violations.extend(visitor.violations)
    
    def _check_magic_numbers(self, py_file: Path, tree: ast.AST) -> None:
        """Check for magic numbers in this file."""
        visitor = MagicNumberVisitor(str(py_file))
        visitor.visit(tree)
        self.violations.extend(visitor.violations)
    
    def _check_naming_consistency(self, py_file: Path, content: str) -> None:
        """Check for naming consistency issues in this file."""
        # Look for common inconsistent patterns
        inconsistencies = [
            (r'\bplugin_class\b', r'\bpluginclass\b', "plugin_class vs pluginclass"),
            (r'\bitem_class\b', r'\bplugin_class\b', "item_class vs plugin_class (consider standardizing)"),
            (r'\bapp_config\b', r'\bconfig\b(?!_)', "app_config vs config (be specific)"),
        ]

        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            # Skip comments and docstrings
            stripped_line = line.strip()
            if stripped_line.startswith('#') or stripped_line.startswith('"""') or stripped_line.startswith("'''"):
                continue

            for pattern1, pattern2, description in inconsistencies:
                if re.search(pattern1, line) and re.search(pattern2, line):
                    self.violations.append(Violation(
                        "NAMING_INCONSISTENCY", str(py_file), line_num,
                        f"Inconsistent naming: {description} in same line", "WARNING"
                    ))

    def _check_generic_usage(self, py_file: Path, content: str) -> None:
        """Check for generic type usage that should be avoided."""
        # Import the generic checker from our mypy plugin
        try:
            from scripts.mypy_no_generics import check_generic_usage_in_file
            violations = check_generic_usage_in_file(str(py_file))

            for violation in violations:
                self.violations.append(Violation(
                    str(violation['type']), str(violation['file']), int(violation['line']),
                    str(violation['message']), str(violation['severity'])
                ))
        except ImportError:
            # Fallback to simple regex-based detection
            self._check_generic_usage_simple(py_file, content)

    def _check_generic_usage_simple(self, py_file: Path, content: str) -> None:
        """Simple regex-based generic type detection."""
        import re

        generic_patterns = [
            (r'\bList\[', 'typing.List usage detected - prefer list[SpecificType]'),
            (r'\bDict\[', 'typing.Dict usage detected - prefer dict[str, SpecificType]'),
            (r'\bSet\[', 'typing.Set usage detected - prefer set[SpecificType]'),
            (r'\bAny\b', 'typing.Any usage detected - prefer specific concrete type'),
            (r'\bUnion\[', 'typing.Union usage detected - prefer Type1 | Type2 syntax'),
            (r'\bOptional\[', 'typing.Optional usage detected - prefer SpecificType | None'),
            (r':\s*list\s*=', 'Untyped list detected - add type annotation'),
            (r':\s*dict\s*=', 'Untyped dict detected - add type annotation'),
            (r':\s*set\s*=', 'Untyped set detected - add type annotation'),
        ]

        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            # Skip comments and docstrings
            stripped = line.strip()
            if stripped.startswith('#') or stripped.startswith('"""') or stripped.startswith("'''"):
                continue

            for pattern, message in generic_patterns:
                if re.search(pattern, line):
                    self.violations.append(Violation(
                        "GENERIC_TYPE_USAGE", str(py_file), line_num,
                        message, "WARNING"
                    ))

    def _run_external_tools(self, py_files: list[Path]) -> None:
        """Run external quality tools on the specified files."""
        print("🛠️  Running external quality tools...")

        for tool_name, tool_config in EXTERNAL_TOOLS.items():
            if self._is_tool_available(tool_name):
                print(f"  → Running {tool_name}: {tool_config['description']}")
                self._run_single_tool(tool_name, tool_config, py_files)
            else:
                print(f"  ⚠️  {tool_name} not available (install with: pip install {tool_name})")

    def _is_tool_available(self, tool_name: str) -> bool:
        """Check if an external tool is available."""
        try:
            subprocess.run([tool_name, '--version'],
                         capture_output=True, check=False, timeout=5)
            return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def _run_single_tool(self, tool_name: str, tool_config: dict[str, Any], py_files: list[Path]) -> None:
        """Run a single external tool and parse its output."""
        try:
            # Build command with file paths
            cmd = tool_config['cmd'] + [str(f) for f in py_files]

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.stdout:
                self._parse_tool_output(tool_name, tool_config, result.stdout)

        except subprocess.TimeoutExpired:
            self.violations.append(Violation(
                "TOOL_TIMEOUT", "external", 0,
                f"{tool_name} timed out after 30 seconds", "WARNING"
            ))
        except Exception as e:
            self.violations.append(Violation(
                "TOOL_ERROR", "external", 0,
                f"{tool_name} failed: {e}", "WARNING"
            ))

    def _parse_tool_output(self, tool_name: str, tool_config: dict[str, Any], output: str) -> None:
        """Parse output from external tools and create violations."""
        severity = tool_config['severity']

        if tool_name == 'radon':
            self._parse_radon_output(output, severity)
        elif tool_name == 'vulture':
            self._parse_vulture_output(output, severity)
        elif tool_name == 'pylint':
            self._parse_pylint_output(output, severity)
        elif tool_name == 'pydeps':
            self._parse_pydeps_output(output, severity)

    def _parse_radon_output(self, output: str, severity: str) -> None:
        """Parse radon cyclomatic complexity output."""
        for line in output.strip().split('\n'):
            if line and not line.startswith('**'):
                # Example: src/plugginger/api/app.py:42:4 PluggingerApp.build - C (10)
                if ':' in line and ' - ' in line:
                    parts = line.split(':')
                    if len(parts) >= 3:
                        file_path = parts[0]
                        line_num = int(parts[1]) if parts[1].isdigit() else 0
                        rest = ':'.join(parts[2:])
                        if ' - C ' in rest or ' - D ' in rest or ' - E ' in rest or ' - F ' in rest:
                            self.violations.append(Violation(
                                "HIGH_COMPLEXITY", file_path, line_num,
                                f"High cyclomatic complexity: {rest.strip()}", severity
                            ))

    def _parse_vulture_output(self, output: str, severity: str) -> None:
        """Parse vulture dead code output."""
        for line in output.strip().split('\n'):
            if line and ':' in line:
                # Example: src/plugginger/api/app.py:42: unused variable 'unused_var' (confidence: 90%)
                parts = line.split(':', 2)
                if len(parts) >= 3:
                    file_path = parts[0]
                    line_num = int(parts[1]) if parts[1].isdigit() else 0
                    message = parts[2].strip()
                    self.violations.append(Violation(
                        "DEAD_CODE", file_path, line_num,
                        f"Dead code detected: {message}", severity
                    ))

    def _parse_pylint_output(self, output: str, severity: str) -> None:
        """Parse pylint output for import errors and circular imports."""
        for line in output.strip().split('\n'):
            if 'import-error' in line or 'cyclic-import' in line:
                # Example: src/plugginger/api/app.py:1:0: E0401: Unable to import 'missing_module' (import-error)
                parts = line.split(':', 3)
                if len(parts) >= 4:
                    file_path = parts[0]
                    line_num = int(parts[1]) if parts[1].isdigit() else 0
                    message = parts[3].strip()
                    violation_type = "CIRCULAR_IMPORT" if 'cyclic-import' in line else "IMPORT_ERROR"
                    self.violations.append(Violation(
                        violation_type, file_path, line_num,
                        f"Import issue: {message}", severity
                    ))

    def _parse_pydeps_output(self, output: str, severity: str) -> None:
        """Parse pydeps dependency output."""
        # pydeps output is usually for information, not violations
        # We'll just log it as info for now
        if output.strip():
            self.violations.append(Violation(
                "DEPENDENCY_INFO", "project", 0,
                f"Dependency analysis available (run pydeps manually for details)", "INFO"
            ))


class FunctionImportVisitor(ast.NodeVisitor):
    """AST visitor to detect function-level imports."""
    
    def __init__(self, file_path: str) -> None:
        self.file_path = file_path
        self.violations: list[Violation] = []
        self.in_function = False
        self.in_type_checking = False
        self.function_stack: list[str] = []
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        self.in_function = True
        self.function_stack.append(node.name)
        self.generic_visit(node)
        self.function_stack.pop()
        self.in_function = len(self.function_stack) > 0
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        self.in_function = True
        self.function_stack.append(node.name)
        self.generic_visit(node)
        self.function_stack.pop()
        self.in_function = len(self.function_stack) > 0
    
    def visit_If(self, node: ast.If) -> None:
        # Check for TYPE_CHECKING blocks
        if (isinstance(node.test, ast.Name) and 
            node.test.id == "TYPE_CHECKING"):
            self.in_type_checking = True
            self.generic_visit(node)
            self.in_type_checking = False
        else:
            self.generic_visit(node)
    
    def visit_Import(self, node: ast.Import) -> None:
        if self.in_function and not self.in_type_checking:
            # Check if this is a legitimate circular import avoidance
            if self._is_circular_import_avoidance(node.lineno):
                return

            function_name = self.function_stack[-1] if self.function_stack else "unknown"
            imports = ", ".join(alias.name for alias in node.names)
            self.violations.append(Violation(
                "FUNCTION_LEVEL_IMPORT", self.file_path, node.lineno,
                f"Function-level import in '{function_name}()': import {imports}"
            ))
    
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        if self.in_function and not self.in_type_checking:
            # Check if this is a legitimate circular import avoidance
            if self._is_circular_import_avoidance(node.lineno):
                return

            function_name = self.function_stack[-1] if self.function_stack else "unknown"
            module = node.module or "?"
            imports = ", ".join(alias.name for alias in node.names)
            self.violations.append(Violation(
                "FUNCTION_LEVEL_IMPORT", self.file_path, node.lineno,
                f"Function-level import in '{function_name}()': from {module} import {imports}"
            ))

    def _is_circular_import_avoidance(self, line_number: int) -> bool:
        """Check if a function-level import is for legitimate circular import avoidance."""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Check the line before and after for circular import comments
            for i in range(max(0, line_number - 3), min(len(lines), line_number + 2)):
                line = lines[i].lower()
                if any(phrase in line for phrase in [
                    'avoid circular import', 'circular import', 'avoid circular',
                    'prevent circular', 'circular dependency', 'avoid cycle'
                ]):
                    return True
            return False
        except:
            return False


class MethodLengthVisitor(ast.NodeVisitor):
    """AST visitor to check method length."""
    
    def __init__(self, file_path: str, lines: list[str], max_lines: int = MAX_METHOD_LINES) -> None:
        self.file_path = file_path
        self.lines = lines
        self.violations: list[Violation] = []
        self.max_lines = max_lines
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        self._check_method_length(node)
        self.generic_visit(node)
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        self._check_method_length(node)
        self.generic_visit(node)
    
    def _check_method_length(self, node: ast.FunctionDef | ast.AsyncFunctionDef) -> None:
        start_line = node.lineno
        end_line = node.end_lineno or start_line
        
        # Count actual code lines (excluding empty lines and comments)
        code_lines = 0
        for line_num in range(start_line - 1, end_line):
            if line_num < len(self.lines):
                line = self.lines[line_num].strip()
                if line and not line.startswith('#'):
                    code_lines += 1
        
        if code_lines > self.max_lines:
            # Changed to WARNING instead of ERROR - 50 lines can be too strict
            severity = "WARNING" if code_lines <= self.max_lines * 1.5 else "ERROR"
            self.violations.append(Violation(
                "LONG_METHOD", self.file_path, start_line,
                f"Method '{node.name}' is {code_lines} lines long (max: {self.max_lines})", severity
            ))


class MagicNumberVisitor(ast.NodeVisitor):
    """AST visitor to detect magic numbers."""
    
    def __init__(self, file_path: str) -> None:
        self.file_path = file_path
        self.violations: list[Violation] = []
    
    def visit_Constant(self, node: ast.Constant) -> None:
        if isinstance(node.value, (int, float)) and node.value not in MAGIC_NUMBER_EXCEPTIONS:
            # Skip if it's in a known constant context
            if not self._is_constant_context(node):
                self.violations.append(Violation(
                    "MAGIC_NUMBER", self.file_path, node.lineno,
                    f"Magic number detected: {node.value} (consider using a named constant)"
                ))

    def _is_constant_context(self, node: ast.Constant) -> bool:
        """Check if the number is in a context where it's acceptable."""
        # Get the source line to check for constant definitions and default parameters
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if node.lineno <= len(lines):
                    line = lines[node.lineno - 1].strip()
                    # Check if it's a constant definition (UPPERCASE_NAME = value)
                    if ': Final[' in line or re.match(r'^[A-Z_][A-Z0-9_]*\s*[:=]', line):
                        return True
                    # Check if it's a default parameter (def func(param: type = value))
                    if re.search(r'def\s+\w+\s*\([^)]*=\s*' + str(node.value), line):
                        return True
                    # Check if it's in a function signature with default value
                    if '=' in line and ('def ' in line or 'async def ' in line):
                        return True
                    # Check if it's a Pydantic Field default value (single line)
                    if 'Field(' in line and 'default=' in line:
                        return True
                    # Check if it's a Pydantic Field default value (multi-line)
                    if 'default=' in line:
                        # Look backwards for Field( declaration
                        for i in range(max(0, node.lineno - 5), node.lineno):
                            if i < len(lines) and 'Field(' in lines[i]:
                                return True
        except:
            pass

        # Check if it's a default argument value
        parent = getattr(node, 'parent', None)
        if hasattr(parent, 'defaults') or hasattr(parent, 'kw_defaults'):
            return True

        # Check if it's in a comparison (like version checks)
        if isinstance(parent, ast.Compare):
            return True

        # Check if it's in a slice operation
        if isinstance(parent, ast.Slice):
            return True

        # Check if it's in a range() call
        if (isinstance(parent, ast.Call) and
            isinstance(parent.func, ast.Name) and
            parent.func.id == 'range'):
            return True

        return False


def main() -> int:
    """Main entry point with file-based arguments."""
    parser = argparse.ArgumentParser(
        description="Targeted Quality Gates - Third line of defence after ruff and mypy",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Check single file
  python scripts/quality-gates-targeted.py src/plugginger/api/app.py

  # Check multiple files
  python scripts/quality-gates-targeted.py src/plugginger/api/app.py src/plugginger/api/builder.py

  # Check with custom method length limit (WARNING at 50, ERROR at 75)
  python scripts/quality-gates-targeted.py --max-method-lines 70 src/plugginger/api/builder.py

  # Enable external tools (radon, vulture, pydeps, pylint)
  python scripts/quality-gates-targeted.py --external-tools src/plugginger/api/app.py

  # Disable generic type checking
  python scripts/quality-gates-targeted.py --no-generics-check src/plugginger/api/app.py

  # Integration with git (changed files)
  git diff --name-only HEAD~1 --diff-filter=AM -- "*.py" | xargs python scripts/quality-gates-targeted.py

  # Full analysis with all tools
  python scripts/quality-gates-targeted.py --external-tools --verbose src/plugginger/api/app.py
        """
    )
    
    parser.add_argument(
        'files', 
        nargs='+', 
        type=Path,
        help='Python files to analyze'
    )
    
    parser.add_argument(
        '--max-method-lines', 
        type=int, 
        default=50,
        help='Maximum lines per method (default: 50)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output with detailed analysis'
    )
    
    parser.add_argument(
        '--json',
        action='store_true',
        help='Output results in JSON format'
    )

    parser.add_argument(
        '--external-tools',
        action='store_true',
        help='Enable external tools (radon, vulture, pydeps, pylint)'
    )

    parser.add_argument(
        '--no-generics-check',
        action='store_true',
        help='Disable generic type usage checking'
    )
    
    args = parser.parse_args()
    
    # Run targeted analysis
    analyzer = TargetedQualityAnalyzer(
        max_method_lines=args.max_method_lines,
        enable_external_tools=args.external_tools,
        check_generics=not args.no_generics_check
    )

    violations = analyzer.analyze_files(args.files)
    
    if not violations:
        print("✅ All targeted quality gates passed!")
        return 0
    
    # Output results
    if args.json:
        import json
        violation_data = [
            {
                "type": v.type,
                "file": v.file, 
                "line": v.line,
                "message": v.message,
                "severity": v.severity
            }
            for v in violations
        ]
        print(json.dumps(violation_data, indent=2))
    else:
        print(f"\n❌ TARGETED QUALITY GATES FAILED: {len(violations)} violations found")
        print("=" * 80)
        
        # Group by file for better readability
        by_file: dict[str, list[Violation]] = {}
        for violation in violations:
            if violation.file not in by_file:
                by_file[violation.file] = []
            by_file[violation.file].append(violation)
        
        for file_path, file_violations in by_file.items():
            print(f"\n📄 {file_path} ({len(file_violations)} issues):")
            for violation in file_violations:
                print(f"  {violation}")
        
        print("\n" + "=" * 80)
        print("💡 Fix these issues before committing")
    
    return 1


if __name__ == "__main__":
    sys.exit(main())
