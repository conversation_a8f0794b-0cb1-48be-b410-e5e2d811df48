#!/usr/bin/env python3
"""
Plugginger Code Quality Gates
Comprehensive code quality enforcement for AI-generated code.
"""

import ast
import hashlib
import re
import sys
from collections import defaultdict
from pathlib import Path
from typing import Any, Dict, List, Set, Tuple, Optional, Union

# Quality gate thresholds
MAX_METHOD_LINES = 50
MAX_CODE_DUPLICATION_SIMILARITY = 0.8  # 80% similarity threshold
MIN_LINE_COUNT_FOR_DUPLICATION = 10
MAGIC_NUMBER_EXCEPTIONS = {0, 1, 2, -1, 100, 1000}  # Common acceptable numbers


class Violation:
    """Represents a code quality violation."""
    
    def __init__(self, violation_type: str, file_path: str, line_number: int, 
                 message: str, severity: str = "ERROR") -> None:
        self.type = violation_type
        self.file = file_path
        self.line = line_number
        self.message = message
        self.severity = severity
    
    def __str__(self) -> str:
        return f"{self.severity}: {self.file}:{self.line} - {self.message}"


class QualityGateAnalyzer:
    """Main analyzer for code quality gates."""
    
    def __init__(self, src_dir: str = "src") -> None:
        self.src_dir = Path(src_dir)
        self.violations: List[Violation] = []
        
    def run_all_checks(self) -> List[Violation]:
        """Run all quality gate checks."""
        print("🔍 Running Plugginger Quality Gates...")
        
        # Get all Python files
        py_files = list(self.src_dir.rglob("*.py"))
        print(f"Analyzing {len(py_files)} Python files...")
        
        # Run all checks
        self.check_function_level_imports(py_files)
        self.check_method_length(py_files)
        self.check_code_duplication(py_files)
        self.check_circular_imports(py_files)
        self.check_magic_numbers(py_files)
        self.check_naming_consistency(py_files)
        
        return self.violations
    
    def check_function_level_imports(self, py_files: List[Path]) -> None:
        """Detect function-level imports (except TYPE_CHECKING)."""
        print("  → Checking function-level imports...")
        
        for py_file in py_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                visitor = FunctionImportVisitor(str(py_file))
                visitor.visit(tree)
                self.violations.extend(visitor.violations)
                
            except Exception as e:
                self.violations.append(Violation(
                    "PARSE_ERROR", str(py_file), 0,
                    f"Failed to parse file: {e}", "WARNING"
                ))
    
    def check_method_length(self, py_files: List[Path]) -> None:
        """Check for methods longer than MAX_METHOD_LINES."""
        print("  → Checking method length...")
        
        for py_file in py_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                tree = ast.parse(''.join(lines))
                visitor = MethodLengthVisitor(str(py_file), lines)
                visitor.visit(tree)
                self.violations.extend(visitor.violations)
                
            except Exception as e:
                self.violations.append(Violation(
                    "PARSE_ERROR", str(py_file), 0,
                    f"Failed to parse file: {e}", "WARNING"
                ))
    
    def check_code_duplication(self, py_files: List[Path]) -> None:
        """Check for code duplication."""
        print("  → Checking code duplication...")
        
        # Extract all methods and their content
        method_contents: Dict[str, Tuple[Path, int, List[str]]] = {}
        
        for py_file in py_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                tree = ast.parse(''.join(lines))
                visitor = MethodExtractorVisitor(py_file, lines)
                visitor.visit(tree)
                method_contents.update(visitor.methods)
                
            except Exception:
                continue
        
        # Compare all methods for similarity
        method_list = list(method_contents.items())
        for i, (method1_name, (file1, line1, content1)) in enumerate(method_list):
            for method2_name, (file2, line2, content2) in method_list[i+1:]:
                if file1 != file2:  # Only check across different files
                    similarity = self._calculate_similarity(content1, content2)
                    if similarity > MAX_CODE_DUPLICATION_SIMILARITY and len(content1) >= MIN_LINE_COUNT_FOR_DUPLICATION:
                        self.violations.append(Violation(
                            "CODE_DUPLICATION", str(file1), line1,
                            f"Method '{method1_name}' has {similarity:.1%} similarity with "
                            f"'{method2_name}' in {file2}:{line2}"
                        ))
    
    def check_circular_imports(self, py_files: List[Path]) -> None:
        """Check for circular imports."""
        print("  → Checking circular imports...")
        
        # Build import graph
        import_graph: Dict[str, Set[str]] = defaultdict(set)
        
        for py_file in py_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                visitor = ImportVisitor()
                visitor.visit(tree)
                
                # Convert file path to module name
                module_name = self._path_to_module(py_file)
                import_graph[module_name].update(visitor.imports)
                
            except Exception:
                continue
        
        # Detect cycles using DFS
        cycles = self._find_cycles_in_graph(import_graph)
        
        for cycle in cycles:
            if len(cycle) >= 2:  # Only report actual cycles
                cycle_str = " → ".join(cycle) + f" → {cycle[0]}"
                self.violations.append(Violation(
                    "CIRCULAR_IMPORT", "multiple", 0,
                    f"Circular import detected: {cycle_str}"
                ))
    
    def check_magic_numbers(self, py_files: List[Path]) -> None:
        """Check for magic numbers."""
        print("  → Checking magic numbers...")
        
        for py_file in py_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                visitor = MagicNumberVisitor(str(py_file))
                visitor.visit(tree)
                self.violations.extend(visitor.violations)
                
            except Exception:
                continue
    
    def check_naming_consistency(self, py_files: List[Path]) -> None:
        """Check for naming consistency issues."""
        print("  → Checking naming consistency...")
        
        for py_file in py_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Look for common inconsistent patterns
                inconsistencies = [
                    (r'\bplugin_class\b', r'\bpluginclass\b', "plugin_class vs pluginclass"),
                    (r'\bitem_class\b', r'\bplugin_class\b', "item_class vs plugin_class"),
                    (r'\bapp_config\b', r'\bconfig\b', "app_config vs config"),
                ]
                
                lines = content.split('\n')
                for line_num, line in enumerate(lines, 1):
                    for pattern1, pattern2, description in inconsistencies:
                        if re.search(pattern1, line) and re.search(pattern2, line):
                            self.violations.append(Violation(
                                "NAMING_INCONSISTENCY", str(py_file), line_num,
                                f"Inconsistent naming: {description} in same line"
                            ))
                
            except Exception:
                continue
    
    def _calculate_similarity(self, content1: List[str], content2: List[str]) -> float:
        """Calculate similarity between two code blocks."""
        # Normalize content (remove whitespace, comments)
        norm1 = self._normalize_code(content1)
        norm2 = self._normalize_code(content2)
        
        if not norm1 or not norm2:
            return 0.0
        
        # Simple similarity based on common lines
        set1 = set(norm1)
        set2 = set(norm2)
        
        if not set1 or not set2:
            return 0.0
        
        intersection = len(set1 & set2)
        union = len(set1 | set2)
        
        return intersection / union if union > 0 else 0.0
    
    def _normalize_code(self, lines: List[str]) -> List[str]:
        """Normalize code for comparison."""
        normalized = []
        for line in lines:
            # Remove comments and whitespace
            line = re.sub(r'#.*', '', line)  # Remove comments
            line = line.strip()
            if line and not line.startswith('#'):
                normalized.append(line)
        return normalized
    
    def _path_to_module(self, py_file: Path) -> str:
        """Convert file path to module name."""
        try:
            relative = py_file.relative_to(self.src_dir)
            parts = list(relative.parts[:-1]) + [relative.stem]
            return '.'.join(parts)
        except ValueError:
            return str(py_file)
    
    def _find_cycles_in_graph(self, graph: Dict[str, Set[str]]) -> List[List[str]]:
        """Find cycles in import graph using DFS."""
        def dfs(node: str, path: List[str], visited: Set[str]) -> List[List[str]]:
            if node in path:
                cycle_start = path.index(node)
                return [path[cycle_start:]]
            
            if node in visited:
                return []
            
            visited.add(node)
            path.append(node)
            
            cycles = []
            for neighbor in graph.get(node, set()):
                cycles.extend(dfs(neighbor, path.copy(), visited))
            
            return cycles
        
        all_cycles = []
        visited: Set[str] = set()  # ← FIXED: Added type annotation
        
        for node in graph:
            if node not in visited:
                cycles = dfs(node, [], visited.copy())
                all_cycles.extend(cycles)
        
        return all_cycles


class FunctionImportVisitor(ast.NodeVisitor):
    """AST visitor to detect function-level imports."""
    
    def __init__(self, file_path: str) -> None:
        self.file_path = file_path
        self.violations: List[Violation] = []
        self.in_function = False
        self.in_type_checking = False
        self.function_stack: List[str] = []
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        self.in_function = True
        self.function_stack.append(node.name)
        self.generic_visit(node)
        self.function_stack.pop()
        self.in_function = len(self.function_stack) > 0
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        self.in_function = True
        self.function_stack.append(node.name)
        self.generic_visit(node)
        self.function_stack.pop()
        self.in_function = len(self.function_stack) > 0
    
    def visit_If(self, node: ast.If) -> None:
        # Check for TYPE_CHECKING blocks
        if (isinstance(node.test, ast.Name) and 
            node.test.id == "TYPE_CHECKING"):
            self.in_type_checking = True
            self.generic_visit(node)
            self.in_type_checking = False
        else:
            self.generic_visit(node)
    
    def visit_Import(self, node: ast.Import) -> None:
        if self.in_function and not self.in_type_checking:
            function_name = self.function_stack[-1] if self.function_stack else "unknown"
            imports = ", ".join(alias.name for alias in node.names)
            self.violations.append(Violation(
                "FUNCTION_LEVEL_IMPORT", self.file_path, node.lineno,
                f"Function-level import in '{function_name}()': import {imports}"
            ))
    
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        if self.in_function and not self.in_type_checking:
            function_name = self.function_stack[-1] if self.function_stack else "unknown"
            module = node.module or "?"
            imports = ", ".join(alias.name for alias in node.names)
            self.violations.append(Violation(
                "FUNCTION_LEVEL_IMPORT", self.file_path, node.lineno,
                f"Function-level import in '{function_name}()': from {module} import {imports}"
            ))


class MethodLengthVisitor(ast.NodeVisitor):
    """AST visitor to check method length."""
    
    def __init__(self, file_path: str, lines: List[str]) -> None:
        self.file_path = file_path
        self.lines = lines
        self.violations: List[Violation] = []
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        self._check_method_length(node)
        self.generic_visit(node)
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        self._check_method_length(node)
        self.generic_visit(node)
    
    def _check_method_length(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef]) -> None:
        start_line = node.lineno
        end_line = node.end_lineno or start_line
        
        # Count actual code lines (excluding empty lines and comments)
        code_lines = 0
        for line_num in range(start_line - 1, end_line):
            if line_num < len(self.lines):
                line = self.lines[line_num].strip()
                if line and not line.startswith('#'):
                    code_lines += 1
        
        if code_lines > MAX_METHOD_LINES:
            self.violations.append(Violation(
                "LONG_METHOD", self.file_path, start_line,
                f"Method '{node.name}' is {code_lines} lines long (max: {MAX_METHOD_LINES})"
            ))


class MethodExtractorVisitor(ast.NodeVisitor):
    """AST visitor to extract method contents for duplication checking."""
    
    def __init__(self, file_path: Path, lines: List[str]) -> None:
        self.file_path = file_path
        self.lines = lines
        self.methods: Dict[str, Tuple[Path, int, List[str]]] = {}
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        self._extract_method(node)
        self.generic_visit(node)
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        self._extract_method(node)
        self.generic_visit(node)
    
    def _extract_method(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef]) -> None:
        start_line = node.lineno - 1
        end_line = (node.end_lineno or start_line + 1) - 1
        
        if start_line < len(self.lines) and end_line < len(self.lines):
            method_lines = self.lines[start_line:end_line + 1]
            # Only track methods with substantial content
            if len(method_lines) >= MIN_LINE_COUNT_FOR_DUPLICATION:
                method_name = f"{self.file_path.name}:{node.name}"
                self.methods[method_name] = (self.file_path, node.lineno, method_lines)


class ImportVisitor(ast.NodeVisitor):  # ← FIXED: Added proper type annotations
    """AST visitor to collect imports."""
    
    def __init__(self) -> None:
        self.imports: Set[str] = set()
    
    def visit_Import(self, node: ast.Import) -> None:
        for alias in node.names:
            if alias.name.startswith('plugginger.'):
                # Convert to module format
                module_parts = alias.name.split('.')
                if len(module_parts) >= 2:
                    self.imports.add('.'.join(module_parts[1:]))  # Remove 'plugginger' prefix
    
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        if node.module and node.module.startswith('plugginger.'):
            # Convert to module format
            module_parts = node.module.split('.')
            if len(module_parts) >= 2:
                self.imports.add('.'.join(module_parts[1:]))  # Remove 'plugginger' prefix


class MagicNumberVisitor(ast.NodeVisitor):
    """AST visitor to detect magic numbers."""
    
    def __init__(self, file_path: str) -> None:
        self.file_path = file_path
        self.violations: List[Violation] = []
    
    def visit_Constant(self, node: ast.Constant) -> None:
        if isinstance(node.value, (int, float)) and node.value not in MAGIC_NUMBER_EXCEPTIONS:
            # Skip if it's in a known constant context
            parent = getattr(node, 'parent', None)
            if not self._is_constant_context(parent):
                self.violations.append(Violation(
                    "MAGIC_NUMBER", self.file_path, node.lineno,
                    f"Magic number detected: {node.value}"
                ))
    
    def _is_constant_context(self, parent: Any) -> bool:  # ← FIXED: Added return type annotation
        """Check if the number is in a context where it's acceptable."""
        # This is a simplified check - in practice, you'd want more sophisticated detection
        return False


def main() -> int:
    """Main entry point."""
    analyzer = QualityGateAnalyzer()
    violations = analyzer.run_all_checks()
    
    if not violations:
        print("✅ All quality gates passed!")
        return 0
    
    # Group violations by type
    by_type: Dict[str, List[Violation]] = defaultdict(list)
    for violation in violations:
        by_type[violation.type].append(violation)
    
    print(f"\n❌ QUALITY GATE FAILED: {len(violations)} violations found")
    print("=" * 80)
    
    for violation_type, type_violations in by_type.items():
        print(f"\n{violation_type} ({len(type_violations)} violations):")
        for violation in type_violations[:10]:  # Limit output
            print(f"  {violation}")
        if len(type_violations) > 10:
            print(f"  ... and {len(type_violations) - 10} more")
    
    print("\n" + "=" * 80)
    print("❌ BUILD BLOCKED: Fix all violations before merging")
    
    return 1


if __name__ == "__main__":
    sys.exit(main())
