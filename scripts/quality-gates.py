#!/usr/bin/env python3
"""
Plugginger Quality Gates - Targeted File Analysis
Third line of defence after ruff and mypy for specific files.
"""

import argparse
import ast
import sys
from pathlib import Path

# Import all the existing classes from the previous script
from quality_gates import (
    Violation, 
    FunctionImportVisitor,
    MethodLengthVisitor, 
    MethodExtractorVisitor,
    ImportVisitor,
    MagicNumberVisitor
)


class TargetedQualityAnalyzer:
    """Quality analyzer for specific files only."""
    
    def __init__(self, max_method_lines: int = 50):
        self.violations: list[Violation] = []
        self.max_method_lines = max_method_lines
        
    def analyze_files(self, file_paths: List[Path]) -> List[Violation]:
        """Analyze only the specified files."""
        
        # Filter to existing Python files
        py_files = [f for f in file_paths if f.exists() and f.suffix == '.py']
        
        if not py_files:
            print("⚠️  No valid Python files provided")
            return []
        
        print(f"🔍 Running Targeted Quality Gates on {len(py_files)} files...")
        
        for py_file in py_files:
            print(f"  → Analyzing {py_file}")
            self._analyze_single_file(py_file)
        
        return self.violations
    
    def _analyze_single_file(self, py_file: Path) -> None:
        """Analyze a single Python file."""
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            tree = ast.parse(content)
            
            # Run all checks on this file
            self._check_function_level_imports(py_file, tree)
            self._check_method_length(py_file, tree, lines)
            self._check_magic_numbers(py_file, tree)
            
        except Exception as e:
            self.violations.append(Violation(
                "PARSE_ERROR", str(py_file), 0,
                f"Failed to parse file: {e}", "WARNING"
            ))
    
    def _check_function_level_imports(self, py_file: Path, tree: ast.AST) -> None:
        """Check for function-level imports in this file."""
        visitor = FunctionImportVisitor(str(py_file))
        visitor.visit(tree)
        self.violations.extend(visitor.violations)
    
    def _check_method_length(self, py_file: Path, tree: ast.AST, lines: list[str]) -> None:
        """Check method length in this file."""
        visitor = MethodLengthVisitor(str(py_file), lines)
        visitor.max_lines = self.max_method_lines  # Use configurable limit
        visitor.visit(tree)
        self.violations.extend(visitor.violations)
    
    def _check_magic_numbers(self, py_file: Path, tree: ast.AST) -> None:
        """Check for magic numbers in this file."""
        visitor = MagicNumberVisitor(str(py_file))
        visitor.visit(tree)
        self.violations.extend(visitor.violations)


def main() -> int:
    """Main entry point with file-based arguments."""
    parser = argparse.ArgumentParser(
        description="Targeted Quality Gates - Third line of defence after ruff and mypy",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Check single file
  python scripts/quality-gates-targeted.py src/plugginger/api/app.py
  
  # Check multiple files  
  python scripts/quality-gates-targeted.py src/plugginger/api/app.py src/plugginger/api/builder.py
  
  # Check with custom method length limit
  python scripts/quality-gates-targeted.py --max-method-lines 70 src/plugginger/api/builder.py
  
  # Integration with git (changed files)
  git diff --name-only HEAD~1 --diff-filter=AM -- "*.py" | xargs python scripts/quality-gates-targeted.py
        """
    )
    
    parser.add_argument(
        'files', 
        nargs='+', 
        type=Path,
        help='Python files to analyze'
    )
    
    parser.add_argument(
        '--max-method-lines', 
        type=int, 
        default=50,
        help='Maximum lines per method (default: 50)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Verbose output with detailed analysis'
    )
    
    parser.add_argument(
        '--json',
        action='store_true', 
        help='Output results in JSON format'
    )
    
    args = parser.parse_args()
    
    # Run targeted analysis
    analyzer = TargetedQualityAnalyzer(max_method_lines=args.max_method_lines)
    violations = analyzer.analyze_files(args.files)
    
    if not violations:
        print("✅ All targeted quality gates passed!")
        return 0
    
    # Output results
    if args.json:
        import json
        violation_data = [
            {
                "type": v.type,
                "file": v.file, 
                "line": v.line,
                "message": v.message,
                "severity": v.severity
            }
            for v in violations
        ]
        print(json.dumps(violation_data, indent=2))
    else:
        print(f"\n❌ TARGETED QUALITY GATES FAILED: {len(violations)} violations found")
        print("=" * 80)
        
        # Group by file for better readability
        by_file: dict[str, list[Violation]] = {}
        for violation in violations:
            if violation.file not in by_file:
                by_file[violation.file] = []
            by_file[violation.file].append(violation)
        
        for file_path, file_violations in by_file.items():
            print(f"\n📄 {file_path} ({len(file_violations)} issues):")
            for violation in file_violations:
                print(f"  {violation}")
        
        print("\n" + "=" * 80)
        print("💡 Fix these issues before committing")
    
    return 1


if __name__ == "__main__":
    sys.exit(main())
