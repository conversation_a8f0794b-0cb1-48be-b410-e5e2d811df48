"""
MyPy plugin to warn about generic type usage.

This plugin helps enforce the Plugginger coding standard of avoiding
generic types in favor of specific, concrete types.

Usage:
1. Add this file to your project
2. Configure mypy.ini:
   [mypy]
   plugins = scripts.mypy_no_generics

3. Run mypy as usual - it will now warn about generic usage
"""

from typing import Callable, Optional, Type as TypingType, Union
from mypy.plugin import Plugin, FunctionContext, MethodContext
from mypy.nodes import (
    ARG_POS, Argument, Decorator, FuncDef, OverloadedFuncDef, 
    TypeInfo, MypyFile, ImportFrom, Import
)
from mypy.types import Type, UnboundType, Instance


class NoGenericsPlugin(Plugin):
    """MyPy plugin that warns about generic type usage."""
    
    # Generic types to warn about
    GENERIC_TYPES = {
        'typing.List', 'typing.Dict', 'typing.Set', 'typing.Tuple',
        'typing.Optional', 'typing.Union', 'typing.Any',
        'list', 'dict', 'set', 'tuple',
        'builtins.list', 'builtins.dict', 'builtins.set', 'builtins.tuple'
    }
    
    # Acceptable specific alternatives
    SPECIFIC_ALTERNATIVES = {
        'typing.List': 'list[SpecificType]',
        'typing.Dict': 'dict[str, SpecificType]',
        'typing.Set': 'set[SpecificType]',
        'typing.Any': 'specific concrete type',
        'typing.Union': 'specific union like str | int',
        'typing.Optional': 'SpecificType | None'
    }

    def get_function_hook(self, fullname: str) -> Optional[Callable[[FunctionContext], Type]]:
        """Hook for function calls."""
        if any(generic in fullname for generic in self.GENERIC_TYPES):
            return self._warn_generic_usage
        return None

    def get_method_signature_hook(self, fullname: str) -> Optional[Callable[[MethodContext], Type]]:
        """Hook for method signatures."""
        return None

    def get_type_analyze_hook(self, fullname: str) -> Optional[Callable[[Type], Type]]:
        """Hook for type analysis."""
        if fullname in self.GENERIC_TYPES:
            return self._analyze_generic_type
        return None

    def _warn_generic_usage(self, context: FunctionContext) -> Type:
        """Warn about generic type usage in function calls."""
        type_name = str(context.default_return_type)
        
        # Check if it's a problematic generic
        for generic_type in self.GENERIC_TYPES:
            if generic_type in type_name:
                alternative = self.SPECIFIC_ALTERNATIVES.get(generic_type, "a specific concrete type")
                self.api.fail(
                    f"Generic type usage detected: {type_name}. "
                    f"Consider using {alternative} instead for better type safety.",
                    context.context
                )
                break
        
        return context.default_return_type

    def _analyze_generic_type(self, typ: Type) -> Type:
        """Analyze and warn about generic types."""
        if isinstance(typ, Instance):
            type_name = typ.type.fullname
            if type_name in self.GENERIC_TYPES:
                # Only warn if it's truly generic (no type parameters)
                if not typ.args or any(str(arg) == 'Any' for arg in typ.args):
                    alternative = self.SPECIFIC_ALTERNATIVES.get(type_name, "a specific concrete type")
                    # Note: We can't easily get context here, so we'll use a generic message
                    # This would need more sophisticated implementation for line numbers
        
        return typ

    def get_additional_deps(self, file: MypyFile) -> list[tuple[int, str, int]]:
        """Get additional dependencies for analysis."""
        deps = []
        
        # Check imports for generic types
        for node in file.defs:
            if isinstance(node, (Import, ImportFrom)):
                for name in getattr(node, 'names', []):
                    imported_name = name.name
                    if any(generic in imported_name for generic in ['List', 'Dict', 'Set', 'Any', 'Union']):
                        # We could track these imports for more specific warnings
                        pass
        
        return deps


def plugin(version: str) -> TypingType[Plugin]:
    """Plugin entry point."""
    return NoGenericsPlugin


# Additional utility functions for integration with quality gates

def check_generic_usage_in_file(file_path: str) -> list[dict[str, Union[str, int]]]:
    """
    Check a Python file for generic type usage.
    
    This function can be called from the quality gates script
    to check for generic usage without running full mypy.
    """
    import ast
    import re
    
    violations = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simple regex-based detection for common patterns
        generic_patterns = [
            (r'\bList\[', 'typing.List usage detected'),
            (r'\bDict\[', 'typing.Dict usage detected'),
            (r'\bSet\[', 'typing.Set usage detected'),
            (r'\bAny\b', 'typing.Any usage detected'),
            (r'\bUnion\[', 'typing.Union usage detected'),
            (r'\bOptional\[', 'typing.Optional usage detected'),
            (r':\s*list\s*=', 'Untyped list detected'),
            (r':\s*dict\s*=', 'Untyped dict detected'),
            (r':\s*set\s*=', 'Untyped set detected'),
        ]
        
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            # Skip comments and docstrings
            stripped = line.strip()
            if stripped.startswith('#') or stripped.startswith('"""') or stripped.startswith("'''"):
                continue
            
            for pattern, message in generic_patterns:
                if re.search(pattern, line):
                    violations.append({
                        'file': file_path,
                        'line': line_num,
                        'message': message,
                        'type': 'GENERIC_TYPE_USAGE',
                        'severity': 'WARNING'
                    })
    
    except Exception as e:
        violations.append({
            'file': file_path,
            'line': 0,
            'message': f'Error checking generic usage: {e}',
            'type': 'ANALYSIS_ERROR',
            'severity': 'WARNING'
        })
    
    return violations


def suggest_specific_alternatives(generic_type: str) -> str:
    """Suggest specific alternatives for generic types."""
    alternatives = {
        'List': 'list[str] or list[int] or list[YourSpecificType]',
        'Dict': 'dict[str, str] or dict[str, YourValueType]',
        'Set': 'set[str] or set[int] or set[YourSpecificType]',
        'Any': 'str, int, YourSpecificType, or proper Union',
        'Union': 'str | int or YourType1 | YourType2',
        'Optional': 'YourSpecificType | None'
    }
    
    return alternatives.get(generic_type, 'a more specific concrete type')


# Configuration for mypy.ini
MYPY_CONFIG_TEMPLATE = """
# Add this to your mypy.ini file:

[mypy]
plugins = scripts.mypy_no_generics
strict = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true

# This plugin will warn about:
# - typing.List, typing.Dict, typing.Set usage
# - typing.Any usage (prefer specific types)
# - typing.Union usage (prefer | syntax with specific types)
# - Untyped collections (list, dict, set without type parameters)
"""

if __name__ == "__main__":
    # Test the generic detection on a file
    import sys
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        violations = check_generic_usage_in_file(file_path)
        if violations:
            print(f"Generic type violations found in {file_path}:")
            for violation in violations:
                print(f"  Line {violation['line']}: {violation['message']}")
        else:
            print(f"No generic type violations found in {file_path}")
    else:
        print("Usage: python mypy_no_generics.py <file_path>")
        print("\nOr add as mypy plugin in mypy.ini:")
        print(MYPY_CONFIG_TEMPLATE)
