# Plugginger Framework Testing Guide

**Version**: 6.0  
**Last Updated**: December 5, 2025  
**Status**: Production Ready  

## 🎯 Testing Philosophy

Plugginger follows a **comprehensive testing strategy** with multiple layers of validation to ensure framework reliability, type safety, and performance. All tests must pass before any code changes are merged.

## 🏗️ Test Architecture

### Test Categories

#### 1. **Unit Tests** (`tests/unit/`)
**Purpose**: Test individual components in isolation

**Coverage Areas**:
- Core type system validation
- Error handling service functionality
- Configuration management
- Plugin lifecycle components
- Service registration and discovery

**Example Structure**:
```python
# tests/unit/test_core_error_handling.py
def test_error_data_typed_creation():
    """Test ErrorDataTyped dataclass creation and validation."""
    error_data = ErrorDataTyped(
        operation="test_operation",
        error_type="ValidationError",
        message="Test error message",
        context={"key": "value"},
        timestamp="2025-12-05T15:30:00Z"
    )
    assert error_data.operation == "test_operation"
    assert error_data.error_type == "ValidationError"
```

#### 2. **Integration Tests** (`tests/integration/`)
**Purpose**: Test component interactions and data flow

**Coverage Areas**:
- Builder phase integration
- Event system end-to-end flow
- Service call chains
- Configuration resolution
- Error handling integration

**Example Structure**:
```python
# tests/integration/test_centralized_error_handling_integration.py
async def test_error_service_integration():
    """Test ErrorService integration with framework components."""
    error_service = ErrorService(logger=mock_logger)
    
    # Test plugin error handling
    error_data = error_service.handle_plugin_error(
        context={"plugin_id": "test_plugin", "operation": "initialization"}
    )
    
    assert isinstance(error_data, ErrorDataTyped)
    assert error_data.operation == "plugin_error_handling"
```

#### 3. **End-to-End Tests** (`tests/e2e/`)
**Purpose**: Test complete application workflows

**Coverage Areas**:
- Full application building and execution
- Plugin composition and interaction
- Event system with real plugins
- Service calls across plugin boundaries
- Fractal composition scenarios

**Example Structure**:
```python
# tests/e2e/test_fractal_composition_integration.py
async def test_complete_fractal_app_workflow():
    """Test complete fractal application composition."""
    # Build outer app with inner app plugin
    outer_app = (
        PluggingerAppBuilder("outer_app")
        .add_plugin("inner_app", InnerAppPlugin())
        .build()
    )
    
    # Test service calls across app boundaries
    result = await outer_app.call_service("inner_app.process_data", "test")
    assert result == "processed: test"
```

## 📊 Quality Gates

### Mandatory Test Requirements

#### 1. **Test Coverage** (>75% Required)
```bash
# Run coverage analysis
pytest --cov=src/plugginger --cov-report=html --cov-report=term

# Current status: >75% coverage achieved
Coverage: 81.03% (requirement: >75%)
```

#### 2. **Type Safety** (mypy --strict)
```bash
# All source files must pass strict type checking
mypy . --strict

# Current status: 0 errors in refactored modules
Success: no issues found in 139 source files
```

#### 3. **Code Quality** (ruff check)
```bash
# All files must pass style and import checks
ruff check .

# Current status: All checks passed
All checks passed!
```

### Test Execution Pipeline

#### 1. **Local Development**
```bash
# Run all tests with coverage
pytest tests/ --cov=src/plugginger -v

# Run specific test categories
pytest tests/unit/ -v          # Unit tests only
pytest tests/integration/ -v   # Integration tests only
pytest tests/e2e/ -v          # End-to-end tests only
```

#### 2. **Pre-commit Validation**
```bash
# Mandatory checks before any commit
pytest tests/ --cov=src/plugginger --cov-fail-under=75
mypy . --strict
ruff check .
```

#### 3. **CI/CD Pipeline**
- All tests must pass in CI environment
- Coverage reports generated and tracked
- Quality gates enforced automatically
- No merge allowed without green tests

## 🧪 Test Implementation Patterns

### 1. **Dataclass Testing Pattern**
```python
# Testing typed dataclasses (post-R4 refactoring)
def test_typed_error_data():
    """Test typed error data creation and access."""
    error_data = ErrorDataTyped(
        operation="test_op",
        error_type="TestError",
        message="Test message",
        context={"key": "value"},
        timestamp="2025-12-05T15:30:00Z"
    )
    
    # Use dataclass attribute access (not dict access)
    assert error_data.operation == "test_op"
    assert error_data.context["key"] == "value"
```

### 2. **Async Testing Pattern**
```python
# Testing asynchronous components
@pytest.mark.asyncio
async def test_async_service_call():
    """Test asynchronous service execution."""
    app = build_test_app()
    
    result = await app.call_service("test_service", "input_data")
    assert result == "expected_output"
```

### 3. **Mock and Fixture Pattern**
```python
# Using fixtures for test setup
@pytest.fixture
def mock_logger():
    """Provide mock logger for testing."""
    return Mock(spec=logging.Logger)

@pytest.fixture
def error_service(mock_logger):
    """Provide configured ErrorService for testing."""
    return ErrorService(logger=mock_logger)

def test_with_fixtures(error_service):
    """Test using provided fixtures."""
    result = error_service.handle_plugin_error()
    assert isinstance(result, ErrorDataTyped)
```

## 🔧 Test Configuration

### pytest Configuration (`pytest.ini`)
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --strict-markers
    --strict-config
    --cov=src/plugginger
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=75
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
```

### Coverage Configuration
```toml
# pyproject.toml [tool.coverage.run]
source = ["src/plugginger"]
omit = [
    "*/tests/*",
    "*/examples/*",
    "*/scripts/*"
]

# [tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError"
]
```

## 🚀 Test Development Guidelines

### 1. **Test Naming Convention**
```python
# Pattern: test_<component>_<scenario>_<expected_outcome>
def test_error_service_plugin_error_returns_typed_data():
    """Test that ErrorService.handle_plugin_error returns ErrorDataTyped."""
    pass

def test_event_dispatcher_pattern_matching_finds_listeners():
    """Test that EventDispatcher finds listeners for event patterns."""
    pass
```

### 2. **Test Documentation**
```python
def test_complex_scenario():
    """
    Test complex scenario with multiple components.
    
    This test verifies:
    1. Component A initializes correctly
    2. Component B receives data from A
    3. Error handling works when B fails
    4. System recovers gracefully
    """
    pass
```

### 3. **Test Data Management**
```python
# Use fixtures for reusable test data
@pytest.fixture
def sample_plugin_config():
    """Provide sample plugin configuration for testing."""
    return PluginConfigData({
        "name": "test_plugin",
        "version": "1.0.0",
        "settings": {"debug": True}
    })
```

## 📈 Test Metrics and Monitoring

### Current Test Status (December 2025)
- **Total Tests**: 150+ tests across all categories
- **Test Coverage**: 81.03% (exceeds 75% requirement)
- **Unit Tests**: 26/26 passing (error handling focus)
- **Integration Tests**: All passing
- **End-to-End Tests**: All passing
- **Quality Gates**: All green (mypy, ruff, pytest)

### Test Performance Metrics
- **Average Test Runtime**: <30 seconds for full suite
- **Unit Test Speed**: <5 seconds
- **Integration Test Speed**: <15 seconds
- **E2E Test Speed**: <10 seconds

### Continuous Improvement
- **Test Coverage Trending**: Monitored and improved continuously
- **Test Performance**: Optimized for fast feedback
- **Test Reliability**: Flaky tests identified and fixed
- **Test Maintenance**: Regular review and updates

## 🔄 Test Maintenance Strategy

### 1. **Regular Test Review**
- Monthly review of test coverage
- Quarterly review of test performance
- Annual review of test architecture

### 2. **Test Refactoring**
- Update tests when APIs change
- Maintain test clarity and readability
- Remove obsolete tests
- Add tests for new features

### 3. **Test Documentation**
- Keep test documentation current
- Document complex test scenarios
- Maintain test guidelines
- Share testing best practices

---

**This testing documentation ensures comprehensive validation of the Plugginger framework across all layers and components, maintaining high quality and reliability standards.**
