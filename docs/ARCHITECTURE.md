# Plugginger Framework Architecture

**Version**: 6.0  
**Last Updated**: December 5, 2025  
**Status**: Production Ready  

## 🏗️ Overview

Plugginger is a universal Python plugin framework built on **Pluggy** and **Blinker**, providing comprehensive plugin management, event system, hook system, and error handling. The framework follows a **layered architecture** with strict separation of concerns and comprehensive type safety.

## 🎯 Core Design Principles

### 1. **Separation of Concerns**
- **API Layer**: Public interfaces and builders
- **Core Layer**: Framework fundamentals and types
- **Services Layer**: Business logic and utilities
- **Internal Layer**: Implementation details and runtime
- **Plugin Layer**: Extensibility and composition

### 2. **Type Safety First**
- **mypy --strict** compliance across all modules
- **Comprehensive type definitions** replacing `dict[str, Any]` patterns
- **Protocol-based interfaces** for duck typing
- **Typed data classes** for structured data

### 3. **Error Handling Excellence**
- **Centralized ErrorService** with structured context
- **98%+ consistent error patterns** framework-wide
- **Automatic sensitive data filtering**
- **Comprehensive error context** for debugging

### 4. **Dependency Injection**
- **Constructor-based DI** with automatic resolution
- **Protocol-based dependencies** for flexibility
- **Singleton caching** for performance
- **Comprehensive logging** and error handling

## 🏛️ Architecture Layers

### 📚 API Layer (`src/plugginger/api/`)

**Purpose**: Public interfaces and application builders

**Key Components**:
- `builder.py` - Main application builder with 7 specialized phases
- `plugin.py` - Base plugin interface and decorators
- `app_plugin.py` - Fractal composition support
- `service.py` - Service registration decorators
- `events.py` - Event system decorators

**Design Patterns**:
- **Builder Pattern** - Fluent API for application construction
- **Decorator Pattern** - Service and event registration
- **Strategy Pattern** - Configurable build phases

### 🔧 Core Layer (`src/plugginger/core/`)

**Purpose**: Framework fundamentals and shared types

**Key Components**:
- `types.py` - Comprehensive type definitions and protocols
- `error_handler.py` - Centralized error handling with typed interfaces
- `exceptions.py` - Framework-specific exception hierarchy
- `config.py` - Configuration models and validation
- `constants.py` - Framework constants and metadata keys

**Type System**:
```python
# Configuration Types
PluginConfigData = dict[str, Any]
ServiceConfigData = dict[str, Any] 
AppConfigData = dict[str, Any]

# Error Handling Types
@dataclass
class ErrorDataTyped:
    operation: str
    error_type: str
    message: str
    context: dict[str, Any]
    timestamp: str

# Event System Types
EventData = dict[str, Any]
EventPayload = dict[str, Any]
```

### 🛠️ Services Layer (`src/plugginger/services/`)

**Purpose**: Business logic and framework utilities

**Key Components**:
- `error_service.py` - Centralized error handling service (100% typed)
- `manifest_service.py` - Plugin manifest management
- `type_safety_service.py` - Type safety analysis and validation (100% typed)

**Service Architecture**:
- **Single Responsibility** - Each service has one clear purpose
- **Dependency Injection** - Services injected via constructor
- **Type Safety** - All services use typed interfaces
- **Error Handling** - Integrated with ErrorService

### 🔒 Internal Layer (`src/plugginger/_internal/`)

**Purpose**: Implementation details and runtime management

**Key Components**:

#### Runtime (`_internal/runtime/`)
- `dispatcher.py` - Event and service dispatching (90% typed)
- `fault_policy.py` - Error isolation and recovery
- `lifecycle.py` - Plugin lifecycle management

#### Builder Phases (`_internal/builder_phases/`)
- `app_config_resolver.py` - Configuration resolution
- `dependency_orchestrator.py` - Dependency graph construction
- `plugin_instantiator.py` - Plugin instance creation
- `interface_registrar.py` - Service and event registration (90% typed)

**Design Patterns**:
- **Strategy Pattern** - Event pattern matching
- **Command Pattern** - Build phase execution
- **Observer Pattern** - Event dispatching
- **Factory Pattern** - Plugin instantiation

### 🔌 Plugin Layer (`plugins/`)

**Purpose**: Framework extensibility and composition

**Structure**:
```
plugins/
├── core/           # User-reusable framework-shipped plugins
├── internal/       # Framework-internal-only plugins  
└── [user]/         # User-defined plugins
```

**Plugin Types**:
- **Core Plugins** - Reusable framework components
- **Internal Plugins** - Framework implementation details
- **User Plugins** - Application-specific functionality
- **App Plugins** - Fractal composition support

## 🔄 Data Flow Architecture

### 1. **Application Building Flow**

```mermaid
graph TD
    A[PluggingerAppBuilder] --> B[Global Config Resolution]
    B --> C[Runtime Initialization]
    C --> D[Dependency Graph Building]
    D --> E[App Instance Creation]
    E --> F[Plugin Instantiation]
    F --> G[Interface Registration]
    G --> H[Build Finalization]
```

### 2. **Event System Flow**

```mermaid
graph TD
    A[Event Emission] --> B[Pattern Matching]
    B --> C[Listener Discovery]
    C --> D[Task Creation]
    D --> E[Concurrent Execution]
    E --> F[Error Handling]
    F --> G[Task Cleanup]
```

### 3. **Service Call Flow**

```mermaid
graph TD
    A[Service Call] --> B[Service Resolution]
    B --> C[Dependency Injection]
    C --> D[Method Execution]
    D --> E[Result Return]
    E --> F[Error Handling]
```

## 🎨 Design Patterns Used

### 1. **Builder Pattern** (API Layer)
- **PluggingerAppBuilder** - Fluent API for application construction
- **7 Specialized Phases** - Modular build process
- **Method Chaining** - Intuitive configuration

### 2. **Strategy Pattern** (Multiple Layers)
- **EventPatternMatcher** - Configurable pattern matching
- **FaultPolicyHandler** - Pluggable error handling strategies
- **TypeSafetyAnalyzer** - Configurable type analysis

### 3. **Service Locator → Dependency Injection Migration**
- **Constructor Injection** - Dependencies provided at creation
- **Protocol-based Interfaces** - Flexible dependency contracts
- **Automatic Resolution** - Framework handles dependency graph

### 4. **Observer Pattern** (Event System)
- **Event Dispatching** - Decoupled event handling
- **Pattern-based Subscription** - Flexible event routing
- **Asynchronous Processing** - Non-blocking event handling

## 🔐 Error Handling Architecture

### Centralized ErrorService

```python
class ErrorService:
    def handle_plugin_error(self, context: dict[str, Any] | None = None) -> ErrorDataTyped
    def handle_service_error(self, context: dict[str, Any] | None = None) -> ErrorDataTyped
    def handle_event_error(self, context: dict[str, Any] | None = None) -> ErrorDataTyped
    def handle_configuration_error(self, context: dict[str, Any] | None = None) -> ErrorDataTyped
    def handle_dependency_error(self, context: dict[str, Any] | None = None) -> ErrorDataTyped
    def handle_validation_error(self, context: dict[str, Any] | None = None) -> ErrorDataTyped
```

### Error Consistency Metrics
- **98%+ Framework-wide Consistency** - Standardized error patterns
- **Structured Context** - Rich debugging information
- **Security** - Automatic sensitive data filtering
- **Type Safety** - Fully typed error interfaces

## 🧪 Testing Architecture

### Test Categories
- **Unit Tests** (`tests/unit/`) - Individual component testing
- **Integration Tests** (`tests/integration/`) - Component interaction testing
- **End-to-End Tests** (`tests/e2e/`) - Full application flow testing

### Quality Gates
- **pytest** - All tests must pass (>75% coverage required)
- **mypy --strict** - Zero type errors across all files
- **ruff check** - Zero style/import errors

## 📊 Quality Metrics

### Current Status (December 2025)
- **Test Coverage**: >75% (requirement met)
- **Type Safety**: 35% of framework fully typed
- **Error Consistency**: 98%+ framework-wide
- **Code Complexity**: A-level average (excellent)
- **dict[str, Any] Usage**: 385 patterns (20 eliminated in R4)

### Quality Achievements
- **Zero D/C level complexity** - All complex code refactored
- **Production-ready foundation** - Solid architectural base
- **Comprehensive type system** - 35% framework coverage
- **Centralized error handling** - 98%+ consistency

## 🚀 Performance Characteristics

### Event System
- **Pattern Caching** - Improved performance for frequent events
- **Concurrent Processing** - Non-blocking event handling
- **Backpressure Management** - Graceful handling of high load
- **Task Cleanup** - Memory leak prevention

### Dependency Injection
- **Singleton Caching** - Efficient instance reuse
- **Lazy Loading** - On-demand dependency resolution
- **Circular Detection** - Prevents infinite loops
- **Type Validation** - Runtime type checking

## 🔮 Future Architecture Evolution

### Planned Improvements
1. **Complete Type Safety** - 100% framework coverage
2. **Enhanced Plugin Interfaces** - Protocol-based plugin contracts
3. **Advanced Error Recovery** - Automatic error correction
4. **Performance Optimization** - Further caching and optimization

### Architectural Principles to Maintain
- **Backward Compatibility** - Smooth migration paths
- **Type Safety First** - Comprehensive type coverage
- **Error Handling Excellence** - Consistent error patterns
- **Separation of Concerns** - Clear layer boundaries

## 📋 Configuration Architecture

### Configuration Hierarchy
1. **Global App Config** - Application-wide settings
2. **Plugin Configs** - Plugin-specific configuration
3. **Service Configs** - Service-specific settings
4. **Runtime Configs** - Dynamic runtime configuration

### Type-Safe Configuration
```python
# Typed Configuration Models
class GlobalAppConfig(BaseModel):
    app_name: str
    log_level: LogLevel
    event_listener_fault_policy: EventListenerFaultPolicy
    default_event_listener_timeout_seconds: float
    executors: list[ExecutorConfig]
    plugin_configs: TypedPluginConfigs
    max_fractal_depth: int

# Plugin Configuration
class TypedPluginConfigs(BaseModel):
    configs: dict[str, PluginConfigData]

    def get_config(self, plugin_id: str) -> PluginConfigData
    def set_config(self, plugin_id: str, config: PluginConfigData) -> None
```

## 🔄 Lifecycle Management

### Plugin Lifecycle Phases
1. **Discovery** - Plugin manifest loading and validation
2. **Dependency Resolution** - Dependency graph construction
3. **Instantiation** - Plugin instance creation with DI
4. **Registration** - Service and event listener registration
5. **Initialization** - Plugin setup and configuration
6. **Runtime** - Active plugin execution
7. **Shutdown** - Graceful plugin cleanup

### Event Lifecycle
1. **Emission** - Event triggered by plugin or framework
2. **Pattern Matching** - Find matching event listeners
3. **Task Creation** - Create async tasks for listeners
4. **Execution** - Concurrent listener execution
5. **Error Handling** - Handle listener failures
6. **Cleanup** - Task cleanup and memory management

## 🔌 Plugin Development Guide

### Plugin Structure
```python
from plugginger.api import PluginBase, service, on_event

class MyPlugin(PluginBase):
    def __init__(self, logger: logging.Logger, **injected_dependencies):
        super().__init__()
        self._logger = logger

    @service("my_service")
    async def my_service_method(self, data: str) -> str:
        return f"Processed: {data}"

    @on_event("user.*")
    async def handle_user_events(self, event_data: dict[str, Any]) -> None:
        self._logger.info(f"Handling user event: {event_data}")
```

### Service Registration
- **Automatic Discovery** - Services discovered via decorators
- **Fully Qualified Names** - Namespace-based service naming
- **Type Safety** - Service signatures validated
- **Dependency Injection** - Automatic dependency resolution

### Event System Usage
- **Pattern-based Subscription** - Wildcard event patterns
- **Asynchronous Processing** - Non-blocking event handling
- **Error Isolation** - Failed listeners don't affect others
- **Timeout Management** - Configurable listener timeouts

## 🛡️ Security Architecture

### Plugin Isolation
- **Namespace Separation** - Plugins operate in separate namespaces
- **Resource Limits** - Memory and CPU quotas per plugin
- **Permission Model** - Restricted system access
- **Input Validation** - All plugin inputs validated

### Error Security
- **Sensitive Data Filtering** - Automatic removal of secrets
- **Structured Logging** - Consistent log format
- **Error Context** - Rich debugging without exposure
- **Audit Trail** - Comprehensive error tracking

## 📈 Monitoring and Observability

### Structured Logging
```python
# Framework provides structured logging
{
    "timestamp": "2025-12-05T15:30:00Z",
    "level": "INFO",
    "component": "EventDispatcher",
    "event_type": "user.login",
    "plugin_id": "auth_plugin",
    "context": {
        "user_id": "user123",
        "session_id": "sess456"
    }
}
```

### Metrics Collection
- **Event Processing Metrics** - Event throughput and latency
- **Service Call Metrics** - Service performance tracking
- **Error Rate Monitoring** - Error frequency and patterns
- **Resource Usage** - Memory and CPU utilization

### Health Checks
- **Plugin Health** - Individual plugin status
- **Service Availability** - Service endpoint health
- **Event System Health** - Event processing status
- **Dependency Health** - Dependency resolution status

---

**This architecture documentation reflects the current state after successful completion of R1-R4 refactoring phases, establishing a solid foundation for future development.**
