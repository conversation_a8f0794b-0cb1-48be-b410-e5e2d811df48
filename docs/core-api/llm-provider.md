# LLM Provider API Reference

The LLM Provider system enables AI-powered plugin generation with support for 100+ language model providers through LiteLLM.

## Overview

Plugginger supports 100+ LLM providers through LiteLLM unified interface:
- **OpenAI**: GPT-4, GPT-4o, GPT-4o-mini - Highest quality, best for complex tasks
- **Anthropic**: Claude-3 Opus/Sonnet/Haiku - Excellent reasoning and safety
- **Google**: Gemini 1.5 Pro/Flash - Cost-effective, good balance of speed and quality
- **Groq**: Llama models - Ultra-fast inference
- **Ollama**: Free local models - Best for development and privacy
- **Cohere**: Command models - Strong for enterprise use cases
- **Together**: Open source models - Cost-effective alternatives
- **And 90+ more providers** - See [LiteLLM documentation](https://docs.litellm.ai/docs/providers)

## Quick Start

### Basic Usage

```python
from plugginger.plugins.core.llm_provider.services import LiteLLMProviderFactory

# Auto-detect provider from environment
provider = LiteLLMProviderFactory.create_from_env()

# Generate text
result = await provider.generate_text(
    prompt="Create a hello world function",
    max_tokens=100,
    temperature=0.1
)

print(result["content"])
```

### Provider-Specific Creation

```python
# OpenAI
openai_provider = ProviderFactory.create_provider(
    "openai",
    api_key="your-openai-key",
    model="gpt-4o-mini"
)

# Gemini
gemini_provider = ProviderFactory.create_provider(
    "gemini", 
    api_key="your-google-key",
    model="gemini-1.5-flash"
)

# Ollama
ollama_provider = ProviderFactory.create_provider(
    "ollama",
    model="granite3-dense:2b"
)
```

## Core Classes

### `LLMProvider` (Abstract Base)

Base class for all LLM providers.

```python
from abc import ABC, abstractmethod
from typing import Dict, Any

class LLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    @abstractmethod
    async def generate_text(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> Dict[str, Any]:
        """Generate text response."""
        pass
    
    @abstractmethod
    async def generate_structured(
        self,
        system_message: str,
        user_message: str,
        ebnf_grammar: str,
        max_retries: int = 3,
        temperature: float = 0.1
    ) -> Dict[str, Any]:
        """Generate structured JSON response."""
        pass
```

### `ProviderFactory`

Factory class for creating LLM provider instances.

```python
class ProviderFactory:
    """Factory for creating LLM provider instances."""
    
    @staticmethod
    def create_provider(
        provider_type: str,
        api_key: str | None = None,
        model: str | None = None,
        base_url: str | None = None
    ) -> LLMProvider:
        """Create provider instance."""
        pass
    
    @staticmethod
    def create_from_env() -> LLMProvider:
        """Create provider from environment variables."""
        pass
```

## Provider Implementations

### OpenAI Provider

```python
from plugginger.plugins.core.llm_provider.services.provider_service import OpenAIProvider

provider = OpenAIProvider(
    api_key="your-openai-key",
    model="gpt-4o-mini",  # or "gpt-4o"
    base_url=None  # Optional custom endpoint
)

# Text generation
result = await provider.generate_text(
    prompt="Explain async/await in Python",
    max_tokens=200,
    temperature=0.1
)

# Structured generation with JSON mode
result = await provider.generate_structured(
    system_message="You are a helpful assistant that responds in JSON.",
    user_message="Create a plugin manifest for an email service",
    ebnf_grammar='{"name": string, "description": string, "services": array}',
    max_retries=3
)
```

**Features:**
- ✅ Structured JSON output with `response_format`
- ✅ Comprehensive error handling
- ✅ Token usage tracking
- ✅ Retry logic with exponential backoff

### Gemini Provider

```python
from plugginger.plugins.core.llm_provider.services.provider_service import GeminiProvider

provider = GeminiProvider(
    api_key="your-google-key",
    model="gemini-1.5-flash",  # or "gemini-1.5-pro"
    base_url=None  # Uses Google's REST API
)

# Text generation
result = await provider.generate_text(
    prompt="Explain dependency injection",
    max_tokens=150,
    temperature=0.1
)

# Structured generation with responseMimeType
result = await provider.generate_structured(
    system_message="You are a plugin generator.",
    user_message="Create email service specification",
    ebnf_grammar='{"name": string, "services": array}',
    max_retries=2
)
```

**Features:**
- ✅ Direct REST API integration (no library dependency)
- ✅ JSON mode with `responseMimeType: "application/json"`
- ✅ Fast response times (~450ms)
- ✅ Cost-effective pricing

### Ollama Provider

```python
from plugginger.plugins.core.llm_provider.services.provider_service import OllamaProvider

provider = OllamaProvider(
    api_key="",  # Not needed for Ollama
    model="granite3-dense:2b",  # or "qwen2.5-coder:7b"
    base_url="http://localhost:11434"  # Default Ollama endpoint
)

# Text generation
result = await provider.generate_text(
    prompt="Write a Python function to validate email",
    max_tokens=100,
    temperature=0.1
)

# Structured generation with format parameter
result = await provider.generate_structured(
    system_message="You are a code generator.",
    user_message="Create service class structure",
    ebnf_grammar='{"class_name": string, "methods": array}',
    max_retries=3
)
```

**Features:**
- ✅ Local execution (privacy-friendly)
- ✅ No API key required
- ✅ JSON format support with `format: "json"`
- ✅ Very fast responses (~100ms)
- ✅ Free to use

## Response Format

All providers return standardized response dictionaries:

### Text Generation Response

```python
{
    "content": "Generated text content",
    "model": "model-name-used",
    "provider": "provider-name",
    "tokens_used": 150,
    "success": True,
    "finish_reason": "stop"  # or "length", "error"
}
```

### Structured Generation Response

```python
{
    "content": '{"name": "email_service", "description": "..."}',
    "model": "model-name-used", 
    "provider": "provider-name",
    "tokens_used": 200,
    "success": True,
    "validated": True,  # JSON validation passed
    "retries_used": 1,
    "finish_reason": "stop"
}
```

### Error Response

```python
{
    "content": "",
    "model": "model-name",
    "provider": "provider-name", 
    "tokens_used": 0,
    "success": False,
    "error": "Error description",
    "finish_reason": "error"
}
```

## Environment Configuration

### Auto-Detection Priority

1. `PLUGGINGER_LLM_*` variables (highest priority)
2. Provider-specific variables (`OPENAI_API_KEY`, etc.)
3. Default to first available provider

### Environment Variables

```bash
# Plugginger-specific configuration
export PLUGGINGER_LLM_PROVIDER="gemini"        # Force provider
export PLUGGINGER_LLM_API_KEY="your-key"       # Override API key
export PLUGGINGER_LLM_MODEL="gemini-1.5-pro"   # Force model
export PLUGGINGER_LLM_BASE_URL="custom-url"    # Custom endpoint

# Provider-specific variables
export OPENAI_API_KEY="your-openai-key"
export GOOGLE_API_KEY="your-google-key"
# Ollama uses localhost:11434 by default
```

## Error Handling

### Automatic Fallbacks

```python
# Provider automatically handles:
# 1. Missing libraries → Mock responses
# 2. Invalid API keys → Structured error
# 3. Network issues → Retry with backoff
# 4. Invalid JSON → Retry with improved prompt

result = await provider.generate_structured(...)
if not result["success"]:
    print(f"Error: {result['error']}")
    # Handle fallback logic
```

### Custom Error Handling

```python
try:
    result = await provider.generate_text("Hello")
    if result["success"]:
        print(result["content"])
    else:
        # Handle API errors gracefully
        logger.error(f"LLM error: {result['error']}")
        # Use fallback or template generation
except Exception as e:
    # Handle unexpected errors
    logger.exception("Unexpected LLM provider error")
```

## Performance Optimization

### Model Selection

```python
# Fast development
fast_provider = ProviderFactory.create_provider(
    "ollama", 
    model="granite3-dense:2b"
)

# Balanced production
balanced_provider = ProviderFactory.create_provider(
    "gemini",
    api_key=os.getenv("GOOGLE_API_KEY"),
    model="gemini-1.5-flash"
)

# Highest quality
quality_provider = ProviderFactory.create_provider(
    "openai",
    api_key=os.getenv("OPENAI_API_KEY"), 
    model="gpt-4o"
)
```

### Async Best Practices

```python
import asyncio

async def generate_multiple_plugins():
    """Generate multiple plugins concurrently."""
    provider = ProviderFactory.create_from_env()
    
    tasks = [
        provider.generate_structured(
            system_message="You are a plugin generator",
            user_message=f"Create {service} service",
            ebnf_grammar='{"name": string, "services": array}'
        )
        for service in ["auth", "email", "logging"]
    ]
    
    results = await asyncio.gather(*tasks)
    return results
```

## Testing

### Unit Testing with Mocks

```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_provider_text_generation():
    """Test provider text generation."""
    provider = ProviderFactory.create_provider("ollama")
    
    with patch.object(provider, 'generate_text') as mock_generate:
        mock_generate.return_value = {
            "content": "Hello world",
            "success": True,
            "tokens_used": 10
        }
        
        result = await provider.generate_text("Hello")
        assert result["success"] is True
        assert "Hello world" in result["content"]
```

### Integration Testing

```python
@pytest.mark.asyncio
@pytest.mark.skipif(not os.getenv("GOOGLE_API_KEY"), reason="API key required")
async def test_real_gemini_integration():
    """Test real Gemini API integration."""
    provider = ProviderFactory.create_provider(
        "gemini",
        api_key=os.getenv("GOOGLE_API_KEY")
    )
    
    result = await provider.generate_text("Say hello", max_tokens=10)
    
    assert result["success"] is True
    assert len(result["content"]) > 0
    assert result["provider"] == "gemini"
```

## See Also

- [AI Plugin Generation Guide](../ai-integration/AI_PLUGIN_GENERATION.md)
- [CLI Reference](cli.md)
- [Model Selection Design](../../CLI_MODEL_SELECTION_DESIGN.md)
