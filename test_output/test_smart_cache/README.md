# test_smart_cache

Generated test_smart_cache plugin using Plugginger Framework.

## Installation

```bash
poetry install
```

## Testing

```bash
poetry run pytest
```

## Usage

```python
from plugginger.api.builder import PluggingerAppBuilder

builder = PluggingerAppBuilder(app_name="my_app")
builder.discover_and_include_plugins(directory=".", pattern="manifest.yaml")
app = builder.build()

# Use the plugin service
result = await app.call_service("test_smart_cache.hello")
print(result)
```