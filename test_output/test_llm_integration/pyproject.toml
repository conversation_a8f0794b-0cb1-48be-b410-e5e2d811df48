[tool.poetry]
name = "plugginger-test_llm_integration"
version = "1.0.0"
description = "Generated test_llm_integration plugin"
authors = ["Plugin Generator <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.11,<4.0"
plugginger = ">=1.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
pytest-asyncio = "^1.0.0"
mypy = "^1.15.0"
ruff = "^0.11.11"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
pythonpath = "."

[tool.mypy]
strict = true

[tool.ruff]
line-length = 120
target-version = "py311"