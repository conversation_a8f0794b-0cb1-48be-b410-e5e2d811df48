#!/usr/bin/env python3
"""
Model Selection Workflow Test
"""

import asyncio
import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional


class TaskType(Enum):
    """Types of tasks for model selection."""
    SIMPLE_TEXT = "simple_text"
    STRUCTURED_JSON = "structured_json"
    CODE_GENERATION = "code_generation"


class ModelTier(Enum):
    """Model performance tiers."""
    FAST = "fast"          # Quick responses, lower cost
    BALANCED = "balanced"  # Good balance of speed and quality
    QUALITY = "quality"    # Best quality, higher cost


@dataclass
class ModelSpec:
    """Specification for an LLM model."""
    provider: str
    model_name: str
    tier: ModelTier
    cost_per_1k_tokens: float
    typical_response_time_ms: int
    quality_score: float  # 0.0 to 1.0
    supports_json: bool = True


class ModelSelector:
    """Intelligent model selection based on task requirements."""
    
    def __init__(self):
        self.models = {
            # Ollama Models (Free, Local)
            "ollama/granite3-dense:2b": ModelSpec(
                provider="ollama",
                model_name="granite3-dense:2b",
                tier=ModelTier.FAST,
                cost_per_1k_tokens=0.0,
                typical_response_time_ms=100,  # Very fast!
                quality_score=0.70
            ),
            "ollama/qwen2.5-coder:7b": ModelSpec(
                provider="ollama",
                model_name="qwen2.5-coder:7b",
                tier=ModelTier.BALANCED,
                cost_per_1k_tokens=0.0,
                typical_response_time_ms=500,
                quality_score=0.85
            ),
            
            # Gemini Models (Fast, Cheap)
            "gemini/gemini-1.5-flash": ModelSpec(
                provider="gemini",
                model_name="gemini-1.5-flash",
                tier=ModelTier.FAST,
                cost_per_1k_tokens=0.000075,
                typical_response_time_ms=450,
                quality_score=0.80
            ),
            "gemini/gemini-1.5-pro": ModelSpec(
                provider="gemini",
                model_name="gemini-1.5-pro",
                tier=ModelTier.QUALITY,
                cost_per_1k_tokens=0.00125,
                typical_response_time_ms=1000,
                quality_score=0.92
            ),
            
            # OpenAI Models (High Quality, More Expensive)
            "openai/gpt-4o-mini": ModelSpec(
                provider="openai",
                model_name="gpt-4o-mini",
                tier=ModelTier.BALANCED,
                cost_per_1k_tokens=0.00015,
                typical_response_time_ms=940,
                quality_score=0.85
            ),
            "openai/gpt-4o": ModelSpec(
                provider="openai",
                model_name="gpt-4o",
                tier=ModelTier.QUALITY,
                cost_per_1k_tokens=0.005,
                typical_response_time_ms=2000,
                quality_score=0.95
            ),
        }
    
    def select_model(
        self,
        task_type: TaskType,
        preferred_tier: ModelTier = ModelTier.BALANCED,
        max_cost_per_1k: Optional[float] = None,
        max_response_time_ms: Optional[int] = None,
        available_providers: Optional[List[str]] = None
    ) -> Optional[ModelSpec]:
        """Select the best model for a given task."""
        
        candidates = []
        
        for model_id, spec in self.models.items():
            # Filter by available providers
            if available_providers and spec.provider not in available_providers:
                continue
                
            # Filter by cost
            if max_cost_per_1k is not None and spec.cost_per_1k_tokens > max_cost_per_1k:
                continue
                
            # Filter by response time
            if max_response_time_ms is not None and spec.typical_response_time_ms > max_response_time_ms:
                continue
                
            candidates.append((model_id, spec))
        
        if not candidates:
            return None
        
        # Score candidates
        scored_candidates = []
        for model_id, spec in candidates:
            score = self._calculate_score(spec, task_type, preferred_tier)
            scored_candidates.append((score, model_id, spec))
        
        # Sort by score (highest first)
        scored_candidates.sort(reverse=True)
        
        return scored_candidates[0][2]  # Return best spec
    
    def _calculate_score(self, spec: ModelSpec, task_type: TaskType, preferred_tier: ModelTier) -> float:
        """Calculate a score for a model based on task requirements."""
        score = 0.0
        
        # Base quality score (40% weight)
        score += spec.quality_score * 0.4
        
        # Tier preference (30% weight)
        if spec.tier == preferred_tier:
            score += 0.3
        elif abs(list(ModelTier).index(spec.tier) - list(ModelTier).index(preferred_tier)) == 1:
            score += 0.15  # Adjacent tier gets partial credit
        
        # Task-specific bonuses (20% weight)
        if task_type == TaskType.CODE_GENERATION and "coder" in spec.model_name.lower():
            score += 0.15
        elif task_type == TaskType.SIMPLE_TEXT and spec.tier == ModelTier.FAST:
            score += 0.1
        
        # Cost efficiency (10% weight)
        if spec.cost_per_1k_tokens == 0.0:  # Free models
            score += 0.1
        elif spec.cost_per_1k_tokens < 0.001:  # Very cheap
            score += 0.05
        
        return score


async def test_model_selection():
    """Test model selection for different scenarios."""
    print("🧠 Testing Intelligent Model Selection\n")
    
    selector = ModelSelector()
    
    # Test scenarios
    scenarios = [
        {
            "name": "Fast Simple Text",
            "task_type": TaskType.SIMPLE_TEXT,
            "preferred_tier": ModelTier.FAST,
            "max_response_time_ms": 200,
            "available_providers": ["ollama", "gemini", "openai"]
        },
        {
            "name": "High Quality JSON",
            "task_type": TaskType.STRUCTURED_JSON,
            "preferred_tier": ModelTier.QUALITY,
            "max_cost_per_1k": 0.01,
            "available_providers": ["gemini", "openai"]
        },
        {
            "name": "Code Generation",
            "task_type": TaskType.CODE_GENERATION,
            "preferred_tier": ModelTier.BALANCED,
            "available_providers": ["ollama", "gemini"]
        },
        {
            "name": "Free Models Only",
            "task_type": TaskType.SIMPLE_TEXT,
            "preferred_tier": ModelTier.BALANCED,
            "max_cost_per_1k": 0.0,
            "available_providers": ["ollama"]
        },
        {
            "name": "Ultra Fast Response",
            "task_type": TaskType.SIMPLE_TEXT,
            "preferred_tier": ModelTier.FAST,
            "max_response_time_ms": 300,
            "available_providers": ["ollama", "gemini", "openai"]
        }
    ]
    
    results = []
    
    for scenario in scenarios:
        print(f"📋 Scenario: {scenario['name']}")
        print(f"   Task: {scenario['task_type'].value}")
        print(f"   Tier: {scenario['preferred_tier'].value}")
        print(f"   Providers: {scenario['available_providers']}")
        
        # Remove scenario name for selection
        selection_params = {k: v for k, v in scenario.items() if k != 'name'}
        
        selected = selector.select_model(**selection_params)
        
        if selected:
            print(f"   ✅ Selected: {selected.provider}/{selected.model_name}")
            print(f"      Quality: {selected.quality_score:.2f}")
            print(f"      Cost: ${selected.cost_per_1k_tokens:.6f}/1k tokens")
            print(f"      Speed: ~{selected.typical_response_time_ms}ms")
            print(f"      Tier: {selected.tier.value}")
            results.append(True)
        else:
            print(f"   ❌ No suitable model found!")
            results.append(False)
        
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"📊 Model Selection Results: {passed}/{total} scenarios handled")
    
    if passed == total:
        print("🎉 All model selection scenarios successful!")
    else:
        print("⚠️  Some scenarios couldn't be handled!")
    
    return passed == total


async def main():
    """Run model selection tests."""
    success = await test_model_selection()
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
