# Plugginger Framework - Master Prompt Template

Dieses Dokument definiert die standardisierten Anweisungen für AI-Agenten, die am Plugginger Framework arbeiten.

## 1. FRAMEWORK KONTEXT

Du arbeitest am **Plugginger Framework** - einem universellen Python Plugin Framework mit AI-powered Plugin Generation. Das Framework basiert auf Pluggy und Blinker und bietet:

- **Plugin Management**: Dynamisches Laden und Verwalten von Plugins
- **Event System**: Asynchrone Event-Verarbeitung mit Blinker
- **Hook System**: Erwei<PERSON>bares Hook-System basierend auf Pluggy
- **AI Integration**: Intelligente Plugin-Generierung mit OpenAI, Gemini, Ollama
- **Dependency Injection**: Automatische Service-Injektion
- **Quality Gates**: MyPy --strict, Ruff, Pytest mit hoher Coverage

## 2. ENTWICKLUNGSSTANDARDS

### Code Quality Requirements
- **MyPy --strict**: Vollständige Typisierung, keine `Any` ohne Begründung
- **Ruff**: PEP8-<PERSON>n<PERSON>it<PERSON>, Import-Sortierung, Code-Style
- **Pytest**: >75% Coverage, alle Tests müssen bestehen
- **Docstrings**: US-English, mkdocs-kompatibel
- **LOC Limit**: Max 50 Zeilen pro Modul (WARNING bei Überschreitung)

### Architektur-Prinzipien
- **Dependency Injection**: Protocol-basierte Interfaces
- **Event-Driven**: Asynchrone Event-Verarbeitung
- **Plugin-Based**: Modulare, erweiterbare Architektur
- **Type-Safe**: Strikte Typisierung mit minimaler Any-Nutzung
- **Error Handling**: Comprehensive Exception-Behandlung

## 3. BRANCH-MANAGEMENT

### MANDATORY Workflow für jeden Task
```bash
git checkout main && git pull origin main

# Backup erstellen (überschreibt vorherigen)
git checkout -b backup/$(date +%Y-%m-%d)-$(git rev-parse --short HEAD)
git push origin backup/$(date +%Y-%m-%d)-$(git rev-parse --short HEAD)
git checkout main

# Arbeits-Branch erstellen
git checkout -b s5/<task-name>

# Nach Entwicklung: Quality Gates + PUSH FOR REVIEW
pytest && mypy --strict . && ruff check .
git push origin s5/<task-name>
```

**KRITISCH**: Deine Aufgabe endet mit dem Push und der PR-Erstellung. Kein direkter Merge!

## 4. 🚨 KRITISCHE REGELN

### ❌ ABSOLUT VERBOTEN
- Direkte Commits auf `main` Branch
- **Merge von Branches in `main`. Deine Aufgabe endet mit dem Push und der PR-Erstellung.**
- Löschen oder Auskommentieren von Tests, um Quality Gates zu umgehen.
- Temporäres Deaktivieren von Code, ohne das offizielle Protokoll zu befolgen.
- Mehr als 1 aktiver Work-Branch pro AI-Instanz
- Work-Branches älter als 1 Woche
- Mock-Implementierungen für P0-Critical Features
- Naive Backpressure-Mechanismen (asyncio.sleep)
- Features als COMPLETE markieren wenn Core-Funktionalität gemockt ist

### ✅ ABSOLUT ERFORDERLICH
- Backup vor jedem neuen Task
- Quality Gates vor jedem Push
- **PR-Erstellung statt direktem Merge**
- Branch-Cleanup nach Task-Completion
- ROADMAP.md Update bei Task-Completion
- GitHub Issue-Synchronisation
- Echte LLM API-Integration für P0 Tasks
- Robuste Async-Patterns für Event-Handling
- Akkurate Status-Berichterstattung
- Funktionalitäts-Demonstration vor COMPLETE-Markierung

### 🚨 Protokoll für temporäre Deaktivierungen

Wenn ein Blocker die Fortsetzung eines Tasks unmöglich macht und die einzige Lösung darin besteht, eine Funktionalität oder einen Test vorübergehend zu deaktivieren, ist folgendes Protokoll **zwingend** einzuhalten:

1. **Dokumentiere im Code:** Füge an der Stelle der Deaktivierung einen klaren Kommentar ein:
   `# TODO-AI-BLOCKED: Temporär deaktiviert am YYYY-MM-DD wegen [kurze, klare Begründung]. Muss in Task [neue Issue-Nummer] reaktiviert werden.`
2. **Erstelle einen neuen Task in `ROADMAP.md`:** Füge im **nächstfolgenden Sprint** einen neuen `todo`-Task mit Priorität `P1` hinzu. Die Beschreibung muss lauten: "Reaktivierung von [deaktivierte Funktionalität/Test] und Behebung von Blocker [Begründung]".
3. **Fahre fort:** Erst nach Befolgen von Schritt 1 und 2 darfst du mit dem ursprünglichen Task fortfahren.

Das "Vergessen" von deaktivierten Teilen ist ein kritischer Prozessfehler und wird als Fehlschlag gewertet.

## 5. TASK-AUSFÜHRUNG

### Workflow-Schritte
1. **Information Gathering**: Codebase-Retrieval für Kontext
2. **Planning**: Detaillierter Plan mit betroffenen Dateien
3. **Implementation**: Schrittweise Umsetzung mit Quality Gates
4. **Testing**: Umfassende Tests für neue Funktionalität
5. **Documentation**: Updates für README, ROADMAP, CHANGELOG
6. **PR Creation**: Push und Pull Request erstellen

### Quality Gates (Non-Negotiable)
```bash
# Vor jedem Push
pytest --cov=src --cov-report=term-missing
mypy --strict src/
ruff check src/ tests/
```

### Memory Management
- Verwende `create_entities_memory` für neue Konzepte
- Verwende `add_observations_memory` für Updates
- Verwende `search_nodes_memory` für Kontext-Retrieval

## 6. LLM PROVIDER INTEGRATION

### Unterstützte Provider
- **OpenAI**: Höchste Qualität, teuer (gpt-4o, gpt-4o-mini)
- **Gemini**: Kosteneffizient, schnell (gemini-1.5-flash, gemini-1.5-pro)
- **Ollama**: Kostenlos, lokal (granite3-dense:2b, qwen2.5-coder:7b)

### Model Selection
- **Fast Tier**: Ollama für schnelle Entwicklung
- **Balanced Tier**: Gemini für Production
- **Quality Tier**: OpenAI für komplexe Tasks

### Environment Variables
```bash
export OPENAI_API_KEY="your-key"
export GOOGLE_API_KEY="your-key"
# Ollama: localhost:11434 (default)
```

## 7. DOKUMENTATION

### Erforderliche Updates
- **README.md**: Feature-Updates und Beispiele
- **ROADMAP.md**: Task-Status und Completion-Dates
- **CHANGELOG.md**: Versioned Release Notes
- **docs/**: API-Dokumentation und Guides

### Docstring-Standard
```python
def example_function(param: str) -> dict[str, Any]:
    """Brief description in US-English.
    
    Args:
        param: Parameter description
        
    Returns:
        Dictionary with result data
        
    Raises:
        ValueError: When param is invalid
    """
```

## 8. TESTING

### Test-Kategorien
- **Unit Tests**: Einzelne Funktionen und Klassen
- **Integration Tests**: Provider-Integration mit echten APIs
- **Async Tests**: Event-System und Async-Patterns
- **Error Tests**: Exception-Handling und Fallbacks
- **Performance Tests**: Response-Zeiten und Throughput

### Coverage Requirements
- **Minimum**: 75% (nicht 50% - das wäre Betrug)
- **Target**: >90% für kritische Module
- **Integration**: Echte API-Calls in CI/CD

## 9. ERFOLGS-METRIKEN

### Definition of Done
- ✅ Alle Tests bestehen (pytest exit code 0)
- ✅ MyPy --strict ohne Fehler
- ✅ Ruff check ohne Fehler
- ✅ ROADMAP.md aktualisiert
- ✅ GitHub Issues synchronisiert
- ✅ PR erstellt (nicht gemerged!)
- ✅ Funktionalität demonstriert

### Qualitäts-Indikatoren
- Code Coverage >75%
- Response Times <2s für LLM calls
- Zero production errors
- Comprehensive error handling
- Real API integration (no mocks for P0)

---

**Letzte Aktualisierung**: 2024-12-05  
**Version**: 6.1.0  
**Workflow**: PR-Only (No Direct Merge)
