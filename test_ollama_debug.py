#!/usr/bin/env python3
"""
Ollama Debug Test - Step by Step
"""

import asyncio
import aiohttp
import json
import time


async def test_step_by_step():
    """Debug Ollama step by step."""
    print("🔍 Debugging Ollama step by step...")
    
    # Step 1: Test connection
    print("\n1️⃣ Testing connection to Ollama...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:11434/api/tags", timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status == 200:
                    result = await response.json()
                    models = result.get('models', [])
                    print(f"✅ Connected! Found {len(models)} models")
                    for model in models:
                        print(f"   - {model.get('name', 'unknown')}")
                else:
                    print(f"❌ Connection failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False
    
    # Step 2: Test minimal request
    print("\n2️⃣ Testing minimal request...")
    url = "http://localhost:11434/api/generate"
    data = {
        "model": "granite3-dense:2b",
        "prompt": "Hi",
        "options": {"num_predict": 5},
        "stream": False
    }
    
    print(f"Request URL: {url}")
    print(f"Request Data: {json.dumps(data, indent=2)}")
    
    start_time = time.time()
    
    try:
        print("🔄 Sending request...")
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, timeout=aiohttp.ClientTimeout(total=15)) as response:
                elapsed = time.time() - start_time
                print(f"📡 Response received after {elapsed:.2f}s")
                print(f"Status: {response.status}")
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Success!")
                    print(f"Response keys: {list(result.keys())}")
                    print(f"Response: {result.get('response', 'NO RESPONSE')}")
                    print(f"Done: {result.get('done', 'NO DONE FLAG')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return False
                    
    except asyncio.TimeoutError:
        elapsed = time.time() - start_time
        print(f"⏰ Timeout after {elapsed:.2f}s")
        return False
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Exception after {elapsed:.2f}s: {e}")
        return False


async def main():
    """Run debug test."""
    print("🚀 Starting Ollama Debug Test")
    success = await test_step_by_step()
    
    if success:
        print("\n🎉 Ollama debug test passed!")
    else:
        print("\n❌ Ollama debug test failed!")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
