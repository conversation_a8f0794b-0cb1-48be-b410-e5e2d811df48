<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: G Pages: 1 -->
<svg width="2170pt" height="1639pt"
 viewBox="0.00 0.00 2169.94 1639.38" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 1635.38)">
<title>G</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="white" stroke="none" points="-4,4 -4,-1635.38 2165.94,-1635.38 2165.94,4 -4,4"/>
<!-- app_py -->
<g id="node1" class="node">
<title>app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#633535" stroke="black" cx="2008.47" cy="-18" rx="29.98" ry="18"/>
<text text-anchor="middle" x="2008.47" y="-14.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">app.py</text>
</g>
<!-- plugginger -->
<g id="node2" class="node">
<title>plugginger</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="1908.47" cy="-127" rx="41.45" ry="18"/>
<text text-anchor="middle" x="1908.47" y="-123.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger</text>
</g>
<!-- plugginger&#45;&gt;app_py -->
<g id="edge1" class="edge">
<title>plugginger&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1941.26,-115.56C1970.7,-105.16 2009.21,-88.55 2008.47,-73"/>
<path fill="none" stroke="black" d="M2008.47,-72C2008.08,-64.07 2007.96,-55.46 2007.96,-47.53"/>
<polygon fill="#10f910" stroke="black" points="2011.46,-47.74 2008.04,-37.71 2004.46,-47.68 2011.46,-47.74"/>
</g>
<!-- plugginger__internal -->
<g id="node3" class="node">
<title>plugginger__internal</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="526.47" cy="-1598.68" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="526.47" y="-1601.93" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="526.47" y="-1589.18" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal</text>
</g>
<!-- plugginger__internal&#45;&gt;app_py -->
<g id="edge2" class="edge">
<title>plugginger__internal&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M477.53,-1594.5C422.15,-1588.86 331.24,-1573.23 266.47,-1529.97 251.77,-1520.16 245.14,-1515.74 242.47,-1498.27"/>
<path fill="none" stroke="black" d="M242.47,-1496.27C238.68,-1471.51 107.08,-1348.18 93.47,-1327.16 47.42,-1256.08 5.59,-1237.97 14.47,-1153.75"/>
<path fill="none" stroke="black" d="M14.47,-1152.75C16.23,-1126.98 12.89,-1120.41 14.47,-1094.62"/>
</g>
<!-- plugginger_api_app -->
<g id="node11" class="node">
<title>plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="498.47,-1418.99 428.47,-1418.99 428.47,-1372.74 498.47,-1372.74 498.47,-1418.99"/>
<text text-anchor="middle" x="463.47" y="-1405.49" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="463.47" y="-1392.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="463.47" y="-1379.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_app -->
<g id="edge3" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M242.47,-1496.27C230.54,-1427.33 308.33,-1469.21 408.47,-1428.56 411.63,-1427.28 414.84,-1425.84 418.03,-1424.3"/>
<polygon fill="#10f910" stroke="black" points="419.45,-1427.51 426.74,-1419.83 416.25,-1421.28 419.45,-1427.51"/>
</g>
<!-- plugginger_api_builder -->
<g id="node14" class="node">
<title>plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1227.47,-346.5 1157.47,-346.5 1157.47,-300.25 1227.47,-300.25 1227.47,-346.5"/>
<text text-anchor="middle" x="1192.47" y="-333" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1192.47" y="-320.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1192.47" y="-307.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_builder -->
<g id="edge4" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M14.47,-1152.75C17.26,-1134.75 146.27,-872.52 154.47,-856.25 194.67,-776.42 203.89,-755.88 249.47,-679 275.18,-635.63 281.15,-624.28 311.47,-584 370.43,-505.66 376.02,-472.57 458.47,-419.5 573.14,-345.67 1002.35,-328.88 1145.59,-325.29"/>
<polygon fill="#10f910" stroke="black" points="1145.56,-328.79 1155.47,-325.06 1145.39,-321.79 1145.56,-328.79"/>
</g>
<!-- plugginger_api_events -->
<g id="node16" class="node">
<title>plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="986.47,-997.5 916.47,-997.5 916.47,-951.25 986.47,-951.25 986.47,-997.5"/>
<text text-anchor="middle" x="951.47" y="-984" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="951.47" y="-971.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="951.47" y="-958.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">events</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_events -->
<g id="edge5" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M242.47,-1496.27C239.45,-1478.85 251.27,-1473.58 266.47,-1464.56 359.6,-1409.3 408.02,-1471.46 507.47,-1428.56 645.71,-1368.93 918.24,-1066.4 926.47,-1034.5"/>
<path fill="none" stroke="black" d="M926.47,-1033.5C928.79,-1025.03 932.1,-1016.18 935.55,-1008.01"/>
<polygon fill="#10f910" stroke="black" points="938.63,-1009.7 939.49,-999.14 932.23,-1006.86 938.63,-1009.7"/>
</g>
<!-- plugginger_api_service -->
<g id="node18" class="node">
<title>plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1240.47,-997.5 1170.47,-997.5 1170.47,-951.25 1240.47,-951.25 1240.47,-997.5"/>
<text text-anchor="middle" x="1205.47" y="-984" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1205.47" y="-971.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1205.47" y="-958.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">service</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_service -->
<g id="edge6" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M570.67,-1587.69C601.81,-1580.91 644.54,-1572.08 682.47,-1565.97 748.97,-1555.26 932.24,-1573.72 983.47,-1529.97 1077.68,-1449.5 1011.62,-1376.04 1059.47,-1261.75 1092.46,-1182.94 1129.6,-1176.37 1154.47,-1094.62"/>
<path fill="none" stroke="black" d="M1154.47,-1092.62C1157.63,-1066.99 1140.61,-1056.3 1154.47,-1034.5"/>
</g>
<!-- plugginger__internal_runtime -->
<g id="node4" class="node">
<title>plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1328.47,-548 1258.47,-548 1258.47,-501.75 1328.47,-501.75 1328.47,-548"/>
<text text-anchor="middle" x="1293.47" y="-534.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1293.47" y="-521.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1293.47" y="-509" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime</text>
</g>
<!-- plugginger__internal_runtime_facade -->
<g id="node9" class="node">
<title>plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1339.72,-465.75 1247.22,-465.75 1247.22,-419.5 1339.72,-419.5 1339.72,-465.75"/>
<text text-anchor="middle" x="1293.47" y="-452.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1293.47" y="-439.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1293.47" y="-426.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime_facade</text>
</g>
<!-- plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge7" class="edge">
<title>plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1293.47,-501.52C1293.47,-494.1 1293.47,-485.67 1293.47,-477.59"/>
<polygon fill="blue" stroke="black" points="1296.97,-477.65 1293.47,-467.65 1289.97,-477.65 1296.97,-477.65"/>
</g>
<!-- plugginger__internal_runtime_dispatcher -->
<g id="node5" class="node">
<title>plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1480.47,-643 1410.47,-643 1410.47,-584 1480.47,-584 1480.47,-643"/>
<text text-anchor="middle" x="1445.47" y="-629.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1445.47" y="-616.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1445.47" y="-604" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1445.47" y="-591.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dispatcher</text>
</g>
<!-- plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime -->
<g id="edge8" class="edge">
<title>plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1410.21,-592.41C1388.9,-580.26 1361.51,-564.66 1338.62,-551.61"/>
<polygon fill="blue" stroke="black" points="1340.53,-548.67 1330.11,-546.76 1337.06,-554.75 1340.53,-548.67"/>
</g>
<!-- plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge9" class="edge">
<title>plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1480.53,-591.03C1504.42,-573.65 1528.96,-548.44 1512.47,-525.88"/>
<path fill="none" stroke="black" d="M1512.47,-523.88C1475.37,-473.12 1402.72,-454.41 1351.28,-447.55"/>
<polygon fill="blue" stroke="black" points="1351.85,-444.09 1341.5,-446.36 1351.01,-451.04 1351.85,-444.09"/>
</g>
<!-- plugginger__internal_runtime_executors -->
<g id="node6" class="node">
<title>plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="986.47,-643 916.47,-643 916.47,-584 986.47,-584 986.47,-643"/>
<text text-anchor="middle" x="951.47" y="-629.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="951.47" y="-616.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="951.47" y="-604" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="951.47" y="-591.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">executors</text>
</g>
<!-- plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime -->
<g id="edge10" class="edge">
<title>plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M986.78,-603.56C1048.94,-587.81 1177.86,-555.16 1247.1,-537.62"/>
<polygon fill="blue" stroke="black" points="1247.79,-541.06 1256.62,-535.21 1246.07,-534.27 1247.79,-541.06"/>
</g>
<!-- plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge11" class="edge">
<title>plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M943,-583.74C939.43,-565.25 938.63,-541.8 951.47,-525.88"/>
</g>
<!-- plugginger__internal_runtime_fault_policy -->
<g id="node7" class="node">
<title>plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1483.97,-738 1410.97,-738 1410.97,-679 1483.97,-679 1483.97,-738"/>
<text text-anchor="middle" x="1447.47" y="-724.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1447.47" y="-711.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1447.47" y="-699" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1447.47" y="-686.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">fault_policy</text>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime -->
<g id="edge12" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1410.54,-683.34C1388.01,-666.67 1360.54,-642.5 1344.47,-614.5"/>
<path fill="none" stroke="black" d="M1344.47,-612.5C1334.03,-594.33 1322.17,-574.14 1312.51,-557.83"/>
<polygon fill="blue" stroke="black" points="1315.69,-556.35 1307.58,-549.53 1309.67,-559.92 1315.69,-556.35"/>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge13" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1446.84,-678.58C1446.68,-671.06 1446.5,-662.82 1446.33,-654.82"/>
<polygon fill="blue" stroke="black" points="1449.83,-654.83 1446.12,-644.91 1442.84,-654.98 1449.83,-654.83"/>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge14" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1344.47,-612.5C1319.91,-569.73 1354.63,-547.99 1337.47,-501.75 1333.94,-492.27 1328.33,-483.04 1322.28,-474.83"/>
<polygon fill="blue" stroke="black" points="1325.22,-472.92 1316.28,-467.23 1319.73,-477.25 1325.22,-472.92"/>
</g>
<!-- plugginger__internal_runtime_lifecycle -->
<g id="node8" class="node">
<title>plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1341.47,-915.25 1271.47,-915.25 1271.47,-856.25 1341.47,-856.25 1341.47,-915.25"/>
<text text-anchor="middle" x="1306.47" y="-901.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1306.47" y="-889" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1306.47" y="-876.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1306.47" y="-863.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">lifecycle</text>
</g>
<!-- plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime -->
<g id="edge15" class="edge">
<title>plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1305.42,-855.99C1303.04,-790.27 1297.21,-629.37 1294.68,-559.34"/>
<polygon fill="blue" stroke="black" points="1298.19,-559.52 1294.33,-549.65 1291.19,-559.77 1298.19,-559.52"/>
</g>
<!-- plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge16" class="edge">
<title>plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1285.3,-856.01C1262.81,-822.59 1230.47,-764.88 1230.47,-709.5 1230.47,-709.5 1230.47,-709.5 1230.47,-612.5 1230.47,-562.56 1229.61,-547.57 1249.47,-501.75 1253.52,-492.38 1259.39,-483.15 1265.53,-474.9"/>
<polygon fill="blue" stroke="black" points="1268.13,-477.25 1271.55,-467.23 1262.62,-472.93 1268.13,-477.25"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;app_py -->
<g id="edge17" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1339.79,-438.91C1459.38,-430.16 1785.32,-395.07 2016.47,-264.25 2084.69,-225.64 2165.71,-196.98 2128.47,-128"/>
<path fill="none" stroke="black" d="M2128.47,-126C2095.89,-77.64 2011.26,-131.24 2008.47,-73"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app -->
<g id="edge18" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1246.98,-447.19C1162.94,-454.28 989.58,-472.1 937.47,-501.75 759.54,-602.99 643.55,-1157.87 528.47,-1327.16 519.49,-1340.37 507.85,-1353.38 496.92,-1364.42"/>
<polygon fill="blue" stroke="black" points="494.51,-1361.88 489.82,-1371.39 499.41,-1366.88 494.51,-1361.88"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder -->
<g id="edge19" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1340.06,-440.5C1415.68,-437.42 1556.22,-426 1550.47,-383.5"/>
<path fill="none" stroke="black" d="M1550.47,-382.5C1546.29,-351.69 1335.45,-333.82 1239.33,-327.27"/>
<polygon fill="blue" stroke="black" points="1239.65,-323.78 1229.44,-326.61 1239.18,-330.76 1239.65,-323.78"/>
</g>
<!-- plugginger_api -->
<g id="node10" class="node">
<title>plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1470.97,-1225.75 1385.97,-1225.75 1385.97,-1189.75 1470.97,-1189.75 1470.97,-1225.75"/>
<text text-anchor="middle" x="1428.47" y="-1204.62" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.api</text>
</g>
<!-- plugginger_api&#45;&gt;app_py -->
<g id="edge20" class="edge">
<title>plugginger_api&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1471.36,-1199.7C1523.53,-1189.28 1611.96,-1165.51 1671.47,-1116.75 1805.02,-1007.32 1847.51,-975.79 1922.47,-820.25 1950.25,-762.6 1908.88,-727.65 1950.47,-679 1980.61,-643.73 2010,-668.96 2048.47,-643 2106.66,-603.73 2134.69,-593.24 2154.47,-525.88"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge21" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1404.17,-1189.53C1379.36,-1169.74 1344.47,-1134.76 1344.47,-1094.62 1344.47,-1094.62 1344.47,-1094.62 1344.47,-1033.5 1344.47,-996.28 1332.87,-955.39 1322.49,-926.25"/>
<polygon fill="blue" stroke="black" points="1325.86,-925.28 1319.12,-917.11 1319.29,-927.71 1325.86,-925.28"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge22" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1471.39,-1192.23C1530.13,-1169.5 1631.8,-1119.16 1662.47,-1034.5 1711.69,-898.61 1673.47,-854.03 1673.47,-709.5 1673.47,-709.5 1673.47,-709.5 1673.47,-612.5 1673.47,-473.33 1457.99,-448 1351.55,-443.97"/>
<polygon fill="blue" stroke="black" points="1351.68,-440.47 1341.57,-443.65 1351.46,-447.47 1351.68,-440.47"/>
</g>
<!-- plugginger_core_types -->
<g id="node26" class="node">
<title>plugginger_core_types</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1442.47,-1116.75 1372.47,-1116.75 1372.47,-1070.5 1442.47,-1070.5 1442.47,-1116.75"/>
<text text-anchor="middle" x="1407.47" y="-1103.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1407.47" y="-1090.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">core.</text>
<text text-anchor="middle" x="1407.47" y="-1077.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">types</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_core_types -->
<g id="edge23" class="edge">
<title>plugginger_api&#45;&gt;plugginger_core_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1398.23,-1189.37C1385.3,-1179.58 1374.75,-1166.74 1382.47,-1153.75"/>
</g>
<!-- plugginger_services_manifest_service -->
<g id="node29" class="node">
<title>plugginger_services_manifest_service</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2012.47,-227.25 1912.47,-227.25 1912.47,-181 2012.47,-181 2012.47,-227.25"/>
<text text-anchor="middle" x="1962.47" y="-213.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1962.47" y="-201" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">services.</text>
<text text-anchor="middle" x="1962.47" y="-188.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">manifest_service</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_services_manifest_service -->
<g id="edge24" class="edge">
<title>plugginger_api&#45;&gt;plugginger_services_manifest_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1471.04,-1198.69C1517.86,-1187.75 1592.52,-1163.91 1638.47,-1116.75 1789.05,-962.19 1714.77,-848.38 1848.47,-679 1908.27,-603.24 2002.47,-622.4 2002.47,-525.88 2002.47,-525.88 2002.47,-525.88 2002.47,-382.5 2002.47,-329.94 1981.19,-312.3 2002.47,-264.25"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api -->
<g id="edge25" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M498.83,-1391.51C636.72,-1378.4 1131.54,-1331.2 1147.47,-1327.16 1235.73,-1304.74 1332.26,-1258.98 1386.09,-1231.37"/>
<polygon fill="blue" stroke="black" points="1387.66,-1234.51 1394.93,-1226.8 1384.44,-1228.29 1387.66,-1234.51"/>
</g>
<!-- plugginger_api_app_plugin -->
<g id="node12" class="node">
<title>plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1620.47,-997.5 1550.47,-997.5 1550.47,-951.25 1620.47,-951.25 1620.47,-997.5"/>
<text text-anchor="middle" x="1585.47" y="-984" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1585.47" y="-971.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1585.47" y="-958.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app_plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_app_plugin -->
<g id="edge26" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M510.07,-1390.24C574.77,-1383.87 695.47,-1372.17 798.47,-1363.16 997.46,-1345.75 1052.66,-1375.52 1246.47,-1327.16 1356.44,-1299.71 1400.47,-1306.05 1480.47,-1225.75 1541.7,-1164.28 1569.16,-1061.76 1579.76,-1009"/>
<polygon fill="blue" stroke="black" points="509.77,-1386.75 500.17,-1391.22 510.46,-1393.72 509.77,-1386.75"/>
<polygon fill="blue" stroke="black" points="1583.15,-1009.89 1581.6,-999.41 1576.28,-1008.57 1583.15,-1009.89"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_builder -->
<g id="edge27" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M456.58,-1372.26C444.64,-1333.98 418.56,-1254.28 389.47,-1189.75 357.13,-1118.04 323.96,-1110.71 304.47,-1034.5 295.51,-999.48 301.9,-741.63 318.47,-709.5"/>
</g>
<!-- plugginger_api_plugin -->
<g id="node17" class="node">
<title>plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1138.47,-1317.58 1068.47,-1317.58 1068.47,-1271.33 1138.47,-1271.33 1138.47,-1317.58"/>
<text text-anchor="middle" x="1103.47" y="-1304.08" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1103.47" y="-1291.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1103.47" y="-1278.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_plugin -->
<g id="edge28" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M510.25,-1389.03C595.85,-1378.17 783.3,-1353.58 940.47,-1327.16 980.01,-1320.51 1024.71,-1311.73 1057.28,-1305.09"/>
<polygon fill="blue" stroke="black" points="509.85,-1385.55 500.37,-1390.28 510.73,-1392.49 509.85,-1385.55"/>
<polygon fill="blue" stroke="black" points="1057.69,-1308.58 1066.78,-1303.14 1056.28,-1301.72 1057.69,-1308.58"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_services_manifest_service -->
<g id="edge29" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_services_manifest_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M445.35,-1372.27C435.67,-1359.51 424.03,-1343 415.47,-1327.16 300.07,-1113.69 280.47,-1040.79 280.47,-798.12 280.47,-798.12 280.47,-798.12 280.47,-612.5 280.47,-455.2 396.38,-418.64 549.47,-382.5 1062.89,-261.28 1702.07,-218.91 1900.69,-208.16"/>
<polygon fill="blue" stroke="black" points="1900.72,-211.66 1910.52,-207.63 1900.35,-204.67 1900.72,-211.66"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;app_py -->
<g id="edge30" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1620.86,-961.23C1645.26,-951.57 1677.33,-936.19 1700.47,-915.25 1755.65,-865.29 1740.37,-829.06 1790.47,-774 1836.71,-723.17 1856.25,-718.53 1912.47,-679 2016.58,-605.79 2118.59,-647.99 2154.47,-525.88"/>
<path fill="none" stroke="black" d="M2154.47,-523.88C2170.42,-463.56 2154.47,-445.89 2154.47,-383.5 2154.47,-383.5 2154.47,-383.5 2154.47,-263.25 2154.47,-202.04 2162.66,-178.77 2128.47,-128"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api -->
<g id="edge31" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1571.84,-997.72C1564.96,-1008.86 1556.41,-1022.48 1548.47,-1034.5 1513.91,-1086.81 1471.61,-1146.59 1447.58,-1180.19"/>
<polygon fill="blue" stroke="black" points="1444.88,-1177.94 1441.9,-1188.11 1450.57,-1182.02 1444.88,-1177.94"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge33" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1578.29,-950.98C1555.66,-876.64 1490.7,-632.08 1550.47,-443.62"/>
</g>
<!-- plugginger_api_background -->
<g id="node13" class="node">
<title>plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#46a446" stroke="black" cx="1656.47" cy="-1294.45" rx="51.62" ry="32.7"/>
<text text-anchor="middle" x="1656.47" y="-1304.08" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1656.47" y="-1291.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">api.</text>
<text text-anchor="middle" x="1656.47" y="-1278.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">background</text>
</g>
<!-- plugginger_api_background&#45;&gt;plugginger_api -->
<g id="edge34" class="edge">
<title>plugginger_api_background&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1612.23,-1277.02C1575.07,-1263.21 1521.82,-1243.43 1482.2,-1228.71"/>
<polygon fill="#46a446" stroke="black" points="1483.43,-1225.43 1472.83,-1225.23 1480.99,-1232 1483.43,-1225.43"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_api -->
<g id="edge35" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1192.47,-346.85C1192.47,-370.45 1192.47,-408.65 1192.47,-441.62 1192.47,-709.5 1192.47,-709.5 1192.47,-709.5 1192.47,-819.89 1224.38,-843.75 1249.47,-951.25 1257.06,-983.79 1253.32,-996.78 1263.88,-1023.13"/>
<polygon fill="blue" stroke="black" points="1260.61,-1024.38 1267.85,-1032.12 1267.01,-1021.55 1260.61,-1024.38"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_services_manifest_service -->
<g id="edge36" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_services_manifest_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1227.8,-321.54C1381.7,-317.68 1986.53,-300.24 2002.47,-264.25"/>
<path fill="none" stroke="black" d="M2002.47,-263.25C2006.5,-254.14 2003.93,-244.94 1998.54,-236.64"/>
<polygon fill="blue" stroke="black" points="2001.36,-234.56 1992.41,-228.89 1995.87,-238.9 2001.36,-234.56"/>
</g>
<!-- plugginger_api_depends -->
<g id="node15" class="node">
<title>plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#23c823" stroke="black" cx="1357.47" cy="-1395.86" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1357.47" y="-1405.49" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1357.47" y="-1392.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">api.</text>
<text text-anchor="middle" x="1357.47" y="-1379.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">depends</text>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api -->
<g id="edge37" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1369.35,-1363.71C1382.9,-1328.2 1404.85,-1270.67 1417.84,-1236.59"/>
<polygon fill="#23c823" stroke="black" points="1421.05,-1238.01 1421.34,-1227.42 1414.51,-1235.51 1421.05,-1238.01"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_builder -->
<g id="edge38" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1362.47,-1362.91C1367.23,-1325.29 1371.75,-1261.29 1357.47,-1208.75"/>
<path fill="none" stroke="black" d="M1357.47,-1206.75C1334.53,-1137.13 1141.92,-1059.28 1102.47,-997.5 1000.28,-837.52 1124.51,-723.23 995.47,-584 929.86,-513.21 555.67,-535.74 584.47,-443.62"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_plugin -->
<g id="edge39" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1315.24,-1378.33C1269.43,-1360.41 1196.55,-1331.88 1149.36,-1313.41"/>
<polygon fill="#23c823" stroke="black" points="1150.82,-1310.23 1140.23,-1309.84 1148.27,-1316.75 1150.82,-1310.23"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_core_types -->
<g id="edge40" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_core_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1357.47,-1206.75C1350.63,-1181.62 1369.16,-1176.14 1382.47,-1153.75"/>
</g>
<!-- plugginger_api_events&#45;&gt;plugginger_api -->
<g id="edge41" class="edge">
<title>plugginger_api_events&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M975.61,-997.76C1009.89,-1028.48 1075.7,-1083.64 1140.47,-1116.75 1151.16,-1122.22 1294.3,-1166.03 1374.66,-1190.45"/>
<polygon fill="blue" stroke="black" points="1373.53,-1193.76 1384.12,-1193.32 1375.56,-1187.07 1373.53,-1193.76"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;app_py -->
<g id="edge42" class="edge">
<title>plugginger_api_plugin&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1068.16,-1286.43C897.53,-1250.26 166.47,-1068.76 166.47,-614.5 166.47,-614.5 166.47,-614.5 166.47,-441.62 166.47,-389.51 139.44,-368.93 166.47,-324.38"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge43" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1002.47,-1092.62C1002.47,-1019.94 1061.82,-1023.3 1116.47,-975.38"/>
<path fill="none" stroke="black" d="M1116.47,-973.38C1159.8,-938.23 1219.69,-914.04 1260.58,-900.33"/>
<polygon fill="blue" stroke="black" points="1261.46,-903.72 1269.88,-897.29 1259.28,-897.07 1261.46,-903.72"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge44" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1014.47,-973.38C1012.57,-895.51 1014.47,-876.01 1014.47,-798.12 1014.47,-798.12 1014.47,-798.12 1014.47,-707.5 1014.47,-651.97 1017.11,-635.14 995.47,-584 982.84,-554.16 931.13,-551.1 951.47,-525.88"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api -->
<g id="edge45" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1138.66,-1280.93C1156.96,-1274.68 1179.76,-1267.3 1200.47,-1261.75 1275.49,-1241.66 1296.19,-1244.86 1371.47,-1225.75 1372.47,-1225.49 1373.49,-1225.23 1374.51,-1224.97"/>
<polygon fill="blue" stroke="black" points="1375.43,-1228.34 1384.16,-1222.34 1373.59,-1221.59 1375.43,-1228.34"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin -->
<g id="edge47" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1131.11,-1270.83C1135.19,-1267.7 1139.39,-1264.59 1143.47,-1261.75 1180.61,-1235.86 1193.37,-1234.71 1230.47,-1208.75"/>
<path fill="none" stroke="black" d="M1230.47,-1206.75C1299.8,-1158.23 1293.68,-1118.36 1363.47,-1070.5 1419.17,-1032.3 1492.67,-1004.51 1539.52,-989.15"/>
<polygon fill="blue" stroke="black" points="1540.36,-992.56 1548.8,-986.16 1538.21,-985.9 1540.36,-992.56"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge48" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1084.03,-1270.85C1055.83,-1235.79 1006.47,-1164.98 1002.47,-1094.62"/>
<path fill="none" stroke="black" d="M1002.47,-1092.62C994.04,-1040.92 1015.79,-1027.74 1014.47,-975.38"/>
<path fill="none" stroke="black" d="M1014.47,-973.38C973.43,-841.71 587.84,-663.75 584.47,-525.88"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_core_types -->
<g id="edge49" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_core_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1230.47,-1206.75C1289.08,-1165.73 1345.91,-1215.25 1382.47,-1153.75"/>
<path fill="none" stroke="black" d="M1382.47,-1152.75C1387.11,-1144.94 1391.36,-1136.1 1394.99,-1127.72"/>
<polygon fill="blue" stroke="black" points="1398.22,-1129.07 1398.81,-1118.49 1391.75,-1126.4 1398.22,-1129.07"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_api -->
<g id="edge50" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1238.64,-997.76C1247.34,-1005.02 1256.13,-1013.79 1262.8,-1023.68"/>
<polygon fill="blue" stroke="black" points="1259.68,-1025.28 1267.71,-1032.19 1265.74,-1021.78 1259.68,-1025.28"/>
<path fill="none" stroke="black" d="M1268.47,-1034.5C1298.49,-1086.14 1288.57,-1111.17 1330.47,-1153.75 1345.93,-1169.47 1367.06,-1181.7 1385.73,-1190.43"/>
</g>
<!-- plugginger_config -->
<g id="node19" class="node">
<title>plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="599.47,-903.75 529.47,-903.75 529.47,-867.75 599.47,-867.75 599.47,-903.75"/>
<text text-anchor="middle" x="564.47" y="-889" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="564.47" y="-876.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">config</text>
</g>
<!-- plugginger_config&#45;&gt;app_py -->
<g id="edge51" class="edge">
<title>plugginger_config&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M568.93,-867.48C572.87,-849.98 577.55,-822.15 574.47,-798.12"/>
<path fill="none" stroke="black" d="M574.47,-796.12C551.92,-620.49 508.03,-584.78 444.47,-419.5 417.55,-349.52 357.48,-336.23 378.47,-264.25"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge52" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M599.7,-870.58C627.63,-856.92 663.56,-833.13 673.47,-798.12"/>
<path fill="none" stroke="black" d="M673.47,-796.12C702.4,-693.84 833.87,-644.68 905.16,-625.17"/>
<polygon fill="blue" stroke="black" points="905.83,-628.62 914.6,-622.68 904.04,-621.85 905.83,-628.62"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge53" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M584.47,-612.5C591.46,-546.55 625.21,-531.53 684.47,-501.75 780.13,-453.66 1102.87,-445.27 1235.66,-443.87"/>
<polygon fill="blue" stroke="black" points="1235.29,-447.37 1245.26,-443.78 1235.23,-440.37 1235.29,-447.37"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_app -->
<g id="edge54" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M578.59,-904.14C587.66,-916.47 598.62,-933.86 603.47,-951.25 608.99,-971.05 606.57,-977.18 603.47,-997.5 580.31,-1149.24 560.45,-1185.86 500.47,-1327.16 495.38,-1339.15 488.81,-1351.79 482.63,-1362.87"/>
<polygon fill="blue" stroke="black" points="479.78,-1360.81 477.87,-1371.23 485.86,-1364.27 479.78,-1360.81"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_builder -->
<g id="edge55" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M574.47,-796.12C566.65,-715.66 570.68,-694.16 584.47,-614.5"/>
<path fill="none" stroke="black" d="M584.47,-612.5C593.45,-575.06 584.47,-564.38 584.47,-525.88"/>
</g>
<!-- plugginger_config_models -->
<g id="node20" class="node">
<title>plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="594.47,-997.5 524.47,-997.5 524.47,-951.25 594.47,-951.25 594.47,-997.5"/>
<text text-anchor="middle" x="559.47" y="-984" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="559.47" y="-971.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">config.</text>
<text text-anchor="middle" x="559.47" y="-958.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">models</text>
</g>
<!-- plugginger_config_models&#45;&gt;app_py -->
<g id="edge56" class="edge">
<title>plugginger_config_models&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M541.58,-951.04C528.85,-934.21 512.18,-910.11 501.47,-886.75"/>
<path fill="none" stroke="black" d="M501.47,-884.75C384.31,-629.18 305.44,-535.75 378.47,-264.25"/>
<path fill="none" stroke="black" d="M378.47,-263.25C379.21,-237.43 353.93,-213.2 378.47,-205.12"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge57" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M673.47,-884.75C686.65,-848.58 651.83,-829.97 673.47,-798.12"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge58" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M594.78,-959.41C623.26,-945.82 660.62,-922.02 673.47,-886.75"/>
<path fill="none" stroke="black" d="M673.47,-884.75C683.51,-857.19 662.59,-849.42 659.47,-820.25 657.27,-799.81 656.68,-794.37 659.47,-774 676.51,-649.54 639.48,-582.79 735.47,-501.75 773.16,-469.93 1100.66,-452.07 1235.67,-446"/>
<polygon fill="blue" stroke="black" points="1235.62,-449.51 1245.46,-445.57 1235.31,-442.52 1235.62,-449.51"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_app -->
<g id="edge59" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M554.29,-997.67C549.82,-1016.94 543.2,-1045.55 537.47,-1070.5 513.09,-1176.62 484.49,-1302.34 471.1,-1361.25"/>
<polygon fill="blue" stroke="black" points="467.69,-1360.44 468.89,-1370.96 474.52,-1361.99 467.69,-1360.44"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_builder -->
<g id="edge60" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M501.47,-884.75C485.42,-849.75 488.61,-834.41 501.47,-798.12"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_config -->
<g id="edge61" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M560.75,-951.07C561.38,-940.2 562.14,-927.01 562.81,-915.41"/>
<polygon fill="blue" stroke="black" points="566.3,-915.76 563.38,-905.57 559.31,-915.35 566.3,-915.76"/>
</g>
<!-- plugginger_config_typed_config -->
<g id="node21" class="node">
<title>plugginger_config_typed_config</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="502.59,-997.5 424.34,-997.5 424.34,-951.25 502.59,-951.25 502.59,-997.5"/>
<text text-anchor="middle" x="463.47" y="-984" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="463.47" y="-971.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">config.</text>
<text text-anchor="middle" x="463.47" y="-958.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">typed_config</text>
</g>
<!-- plugginger_config_typed_config&#45;&gt;plugginger_api_builder -->
<g id="edge62" class="edge">
<title>plugginger_config_typed_config&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M466.45,-951.02C471.38,-917.22 482.59,-851.39 501.47,-798.12"/>
<path fill="none" stroke="black" d="M501.47,-796.12C506.11,-781.12 584.47,-541.58 584.47,-525.88"/>
<path fill="none" stroke="black" d="M584.47,-523.88C583.59,-488.22 573.82,-477.67 584.47,-443.62"/>
</g>
<!-- plugginger_config_typed_config&#45;&gt;plugginger_config -->
<g id="edge63" class="edge">
<title>plugginger_config_typed_config&#45;&gt;plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M489.74,-950.84C503.78,-938.8 521.08,-923.96 535.51,-911.59"/>
<polygon fill="blue" stroke="black" points="537.6,-914.4 542.91,-905.24 533.04,-909.09 537.6,-914.4"/>
</g>
<!-- plugginger_core -->
<g id="node22" class="node">
<title>plugginger_core</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="1232.47" cy="-1598.68" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1232.47" y="-1601.93" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1232.47" y="-1589.18" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core</text>
</g>
<!-- plugginger_core&#45;&gt;app_py -->
<g id="edge64" class="edge">
<title>plugginger_core&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1183.48,-1594.05C1037.25,-1583.07 610.25,-1549.68 550.47,-1529.97 404.73,-1481.94 14.47,-1248.07 14.47,-1094.62"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge65" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1266.16,-1581.09C1279.09,-1575.4 1294.19,-1569.57 1308.47,-1565.97 1466.54,-1526.16 1516.44,-1573.72 1673.47,-1529.97 1739.64,-1511.53 1932.15,-1462.48 1952.47,-1396.86"/>
<path fill="none" stroke="black" d="M1952.47,-1394.86C2017.16,-1279.63 1784.68,-958.08 1700.47,-856.25 1633.99,-775.87 1542.56,-695.02 1489.16,-650.18"/>
<polygon fill="#10f910" stroke="black" points="1491.69,-647.73 1481.77,-644.01 1487.2,-653.11 1491.69,-647.73"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge66" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M812.47,-1394.86C769.64,-1158.53 778.05,-1083.27 856.47,-856.25 866.09,-828.38 871.49,-822.24 888.47,-798.12"/>
<path fill="none" stroke="black" d="M888.47,-796.12C914.44,-752.32 984.11,-756.05 963.47,-709.5"/>
<path fill="none" stroke="black" d="M963.47,-707.5C956.17,-691.05 952.94,-671.48 951.62,-654.54"/>
<polygon fill="#10f910" stroke="black" points="955.13,-654.6 951.07,-644.82 948.14,-655 955.13,-654.6"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge67" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1736.47,-1293.45C1724.78,-1232.16 1747.35,-1216.07 1750.47,-1153.75 1752.31,-1116.8 1756.24,-1107.05 1750.47,-1070.5 1742.74,-1021.64 1729.92,-940.13 1686.47,-886.75"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge68" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1166.47,-1394.86C1164.71,-1383.26 1107.35,-982.77 1116.47,-975.38"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge69" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1183.41,-1594.32C1095.4,-1587.39 915.34,-1568.78 867.47,-1529.97 817.74,-1489.66 825.99,-1459.43 812.47,-1396.86"/>
<path fill="none" stroke="black" d="M812.47,-1394.86C744.13,-1220.58 722.07,-1155.09 774.47,-975.38"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app -->
<g id="edge70" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M564.47,-1496.27C533.48,-1479.47 505.54,-1450.34 486.95,-1427.94"/>
<polygon fill="#10f910" stroke="black" points="489.77,-1425.87 480.76,-1420.29 484.33,-1430.27 489.77,-1425.87"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app_plugin -->
<g id="edge71" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1736.47,-1394.86C1742.53,-1351.1 1745.53,-1338.7 1736.47,-1295.45"/>
<path fill="none" stroke="black" d="M1736.47,-1293.45C1725.06,-1239.02 1577.44,-1149.8 1584.47,-1094.62"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_background -->
<g id="edge72" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1268.75,-1582.38C1281.3,-1577.13 1295.48,-1571.24 1308.47,-1565.97 1497.97,-1489.02 1774.66,-1597.8 1736.47,-1396.86"/>
<path fill="none" stroke="black" d="M1736.47,-1394.86C1740.27,-1367.4 1721.24,-1343.04 1700.79,-1325.3"/>
<polygon fill="#10f910" stroke="black" points="1703.14,-1322.7 1693.18,-1319.08 1698.7,-1328.12 1703.14,-1322.7"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_builder -->
<g id="edge73" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1183.6,-1594.71C1048.39,-1585.82 672.17,-1556.38 564.47,-1498.27"/>
<path fill="none" stroke="black" d="M564.47,-1496.27C501.87,-1462.5 475.22,-1472.73 419.47,-1428.56 405.4,-1417.42 408.26,-1408.34 394.47,-1396.86"/>
<path fill="none" stroke="black" d="M394.47,-1394.86C158.91,-1198.81 171.44,-978.4 318.47,-709.5"/>
<path fill="none" stroke="black" d="M318.47,-707.5C332.47,-668.29 583.49,-485.24 584.47,-443.62"/>
<path fill="none" stroke="black" d="M584.47,-441.62C580.54,-416.09 559.95,-391.65 584.47,-383.5"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_depends -->
<g id="edge74" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1246.08,-1575.8C1266.93,-1542.3 1307.09,-1477.78 1333.06,-1436.06"/>
<polygon fill="#10f910" stroke="black" points="1335.91,-1438.12 1338.22,-1427.78 1329.96,-1434.42 1335.91,-1438.12"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_events -->
<g id="edge75" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1214.56,-1576.21C1199.46,-1556.98 1178.44,-1527.39 1166.47,-1498.27"/>
<path fill="none" stroke="black" d="M1166.47,-1496.27C1107.1,-1351.82 996.49,-1361.6 964.47,-1208.75"/>
<path fill="none" stroke="black" d="M964.47,-1206.75C946.8,-1147.85 957.02,-1129.72 940.47,-1070.5 935.84,-1053.97 923.08,-1051.33 926.47,-1034.5"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_plugin -->
<g id="edge76" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1166.47,-1496.27C1156.34,-1453.26 1173.09,-1440.54 1166.47,-1396.86"/>
<path fill="none" stroke="black" d="M1166.47,-1394.86C1162.61,-1369.46 1147.34,-1345.02 1132.8,-1326.74"/>
<polygon fill="#10f910" stroke="black" points="1135.61,-1324.64 1126.52,-1319.19 1130.22,-1329.11 1135.61,-1324.64"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_service -->
<g id="edge77" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1166.47,-1496.27C1159.42,-1466.36 1177.34,-1459.13 1180.47,-1428.56 1188.01,-1354.81 1186.18,-1335.67 1180.47,-1261.75 1174.67,-1186.8 1133.42,-1166.79 1154.47,-1094.62"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_config_models -->
<g id="edge78" class="edge">
<title>plugginger_core&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M564.47,-1496.27C561.78,-1494.81 560.06,-1123.8 559.59,-1008.9"/>
<polygon fill="#10f910" stroke="black" points="563.1,-1009.19 559.56,-999.2 556.1,-1009.21 563.1,-1009.19"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_config_typed_config -->
<g id="edge79" class="edge">
<title>plugginger_core&#45;&gt;plugginger_config_typed_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M394.47,-1394.86C334.64,-1341.03 389.92,-1108.75 418.47,-1033.5 422.05,-1024.05 427.72,-1014.88 433.85,-1006.73"/>
<polygon fill="#10f910" stroke="black" points="436.39,-1009.16 439.92,-999.18 430.93,-1004.78 436.39,-1009.16"/>
</g>
<!-- plugginger_services_error_service -->
<g id="node28" class="node">
<title>plugginger_services_error_service</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2049.34,-997.5 1969.59,-997.5 1969.59,-951.25 2049.34,-951.25 2049.34,-997.5"/>
<text text-anchor="middle" x="2009.47" y="-984" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2009.47" y="-971.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">services.</text>
<text text-anchor="middle" x="2009.47" y="-958.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">error_service</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_services_error_service -->
<g id="edge80" class="edge">
<title>plugginger_core&#45;&gt;plugginger_services_error_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1266.09,-1580.81C1279.02,-1575.09 1294.12,-1569.31 1308.47,-1565.97 1434.68,-1536.62 1771.86,-1582.17 1890.47,-1529.97 1968.46,-1495.65 2028.47,-1482.08 2028.47,-1396.86 2028.47,-1396.86 2028.47,-1396.86 2028.47,-1092.62 2028.47,-1064.05 2022.9,-1032.05 2017.79,-1008.71"/>
<polygon fill="#10f910" stroke="black" points="2021.25,-1008.12 2015.61,-999.15 2014.42,-1009.68 2021.25,-1008.12"/>
</g>
<!-- plugginger_services_type_safety_service -->
<g id="node30" class="node">
<title>plugginger_services_type_safety_service</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1913.59,-820.25 1799.34,-820.25 1799.34,-774 1913.59,-774 1913.59,-820.25"/>
<text text-anchor="middle" x="1856.47" y="-806.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1856.47" y="-794" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">services.</text>
<text text-anchor="middle" x="1856.47" y="-781.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">type_safety_service</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_services_type_safety_service -->
<g id="edge81" class="edge">
<title>plugginger_core&#45;&gt;plugginger_services_type_safety_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1952.47,-1394.86C1979.15,-1314.77 1990.47,-1293.17 1990.47,-1208.75 1990.47,-1208.75 1990.47,-1208.75 1990.47,-1092.62 1990.47,-1064.57 1908.89,-901 1873.03,-830.47"/>
<polygon fill="#10f910" stroke="black" points="1876.24,-829.05 1868.58,-821.73 1870,-832.23 1876.24,-829.05"/>
</g>
<!-- plugginger_core_config -->
<g id="node23" class="node">
<title>plugginger_core_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2fdb2f" stroke="black" cx="215.47" cy="-1598.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="215.47" y="-1608.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="215.47" y="-1595.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="215.47" y="-1582.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_core_config&#45;&gt;app_py -->
<g id="edge82" class="edge">
<title>plugginger_core_config&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M192.34,-1569.44C179.2,-1550.45 165.52,-1524.23 166.47,-1498.27"/>
<path fill="none" stroke="black" d="M166.47,-1496.27C166.89,-1484.59 46.72,-1337.59 41.47,-1327.16 5.84,-1256.43 5.93,-1232.76 0.47,-1153.75 -1.4,-1126.81 12.82,-1121.58 14.47,-1094.62"/>
<path fill="none" stroke="black" d="M14.47,-1092.62C12.49,-744.57 -21.93,-617.04 166.47,-324.38"/>
<path fill="none" stroke="black" d="M166.47,-322.38C211.81,-224.72 276.19,-238.78 378.47,-205.12"/>
<path fill="none" stroke="black" d="M378.47,-203.12C406.14,-193.64 411.7,-186.33 440.47,-181 523.41,-165.63 1884.18,-184.98 1958.47,-145 1975.62,-135.77 2009.4,-92.46 2008.47,-73"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge83" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M263.88,-1590.81C314.74,-1583.78 397.09,-1572.93 468.47,-1565.97 891.75,-1524.69 1033.64,-1615.87 1415.47,-1428.56 1479.69,-1397.06 1496.46,-1383.56 1540.47,-1327.16 1628.69,-1214.08 1640.95,-1173.46 1676.47,-1034.5 1692.76,-970.73 1723.59,-941.1 1686.47,-886.75"/>
<path fill="none" stroke="black" d="M1686.47,-884.75C1659.82,-847.66 1555.22,-777.75 1493.58,-738.36"/>
<polygon fill="#2fdb2f" stroke="black" points="1495.67,-735.54 1485.35,-733.13 1491.91,-741.45 1495.67,-735.54"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_api_app -->
<g id="edge84" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M166.47,-1496.27C167.15,-1465.33 199.24,-1474.74 228.47,-1464.56 305.51,-1437.74 332.12,-1457.32 408.47,-1428.56 411.66,-1427.36 414.89,-1425.98 418.1,-1424.49"/>
<polygon fill="#2fdb2f" stroke="black" points="419.48,-1427.71 426.85,-1420.1 416.34,-1421.46 419.48,-1427.71"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_config_models -->
<g id="edge85" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M166.47,-1496.27C171.46,-1271.05 268.79,-1215.17 441.47,-1070.5 468.18,-1048.12 498.83,-1023.44 522.19,-1004.83"/>
<polygon fill="#2fdb2f" stroke="black" points="524.25,-1007.66 529.9,-998.7 519.9,-1002.18 524.25,-1007.66"/>
</g>
<!-- plugginger_core_constants -->
<g id="node24" class="node">
<title>plugginger_core_constants</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#22e722" stroke="black" cx="925.47" cy="-1497.27" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="925.47" y="-1506.89" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="925.47" y="-1494.14" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="925.47" y="-1481.39" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">constants</text>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge86" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M921.96,-1464.25C918.42,-1424.31 914.96,-1354.13 926.47,-1295.45"/>
<path fill="none" stroke="black" d="M926.47,-1293.45C946.67,-1207.43 940.04,-1181.94 926.47,-1094.62"/>
<path fill="none" stroke="black" d="M926.47,-1092.62C896.55,-964.09 819.07,-910.38 888.47,-798.12"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_builder -->
<g id="edge87" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M886.67,-1476.76C780.23,-1420.45 486.79,-1245.67 415.47,-997.5 409.79,-977.74 412.18,-971.54 415.47,-951.25 434.49,-833.89 565.69,-561.03 584.47,-443.62"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_events -->
<g id="edge88" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M926.47,-1092.62C922.5,-1067.1 921.37,-1059.83 926.47,-1034.5"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_plugin -->
<g id="edge89" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M949.84,-1468.77C982.86,-1431.52 1041.62,-1365.22 1076.11,-1326.31"/>
<polygon fill="#22e722" stroke="black" points="1078.48,-1328.92 1082.49,-1319.11 1073.24,-1324.27 1078.48,-1328.92"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_service -->
<g id="edge90" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M926.47,-1293.45C931.96,-1275.09 1141.61,-1048.72 1154.47,-1034.5"/>
<path fill="none" stroke="black" d="M1154.47,-1033.5C1160.45,-1024.08 1167.93,-1014.65 1175.3,-1006.21"/>
<polygon fill="#22e722" stroke="black" points="1177.76,-1008.71 1181.85,-998.94 1172.56,-1004.02 1177.76,-1008.71"/>
</g>
<!-- plugginger_core_exceptions -->
<g id="node25" class="node">
<title>plugginger_core_exceptions</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f910" stroke="black" cx="1366.47" cy="-1598.68" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1366.47" y="-1608.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1366.47" y="-1595.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1366.47" y="-1582.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">exceptions</text>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge91" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2077.47,-973.38C2065.31,-846.38 2074.34,-774.34 1964.47,-709.5"/>
<path fill="none" stroke="black" d="M1964.47,-707.5C1886.78,-656.07 1604.52,-627.63 1492.12,-618.14"/>
<polygon fill="#10f910" stroke="black" points="1492.6,-614.66 1482.35,-617.32 1492.02,-621.64 1492.6,-614.66"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge92" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2077.47,-1033.5C2073.03,-1008.05 2083.92,-1000.39 2077.47,-975.38"/>
<path fill="none" stroke="black" d="M2077.47,-973.38C2049.42,-864.7 2019.73,-830.01 1922.47,-774 1850.43,-732.52 1601.59,-716.5 1495.74,-711.47"/>
<polygon fill="#10f910" stroke="black" points="1496.11,-707.98 1485.96,-711.01 1495.79,-714.97 1496.11,-707.98"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge93" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2066.47,-1206.75C2055.58,-1185.86 2077.85,-1174.37 2066.47,-1153.75"/>
<path fill="none" stroke="black" d="M2066.47,-1152.75C2014.78,-1059.13 1732.1,-981.27 1629.47,-951.25 1517.08,-918.38 1480.87,-948.06 1368.47,-915.25 1363.06,-913.67 1357.52,-911.7 1352.09,-909.53"/>
<polygon fill="#10f910" stroke="black" points="1353.81,-906.45 1343.23,-905.76 1351.07,-912.9 1353.81,-906.45"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin -->
<g id="edge94" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2066.47,-1496.27C2138.06,-1390.42 2124.83,-1322.43 2066.47,-1208.75"/>
<path fill="none" stroke="black" d="M2066.47,-1206.75C1941.92,-1049.07 1688.55,-1231.39 1648.47,-1034.5"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_background -->
<g id="edge95" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1415.47,-1592.78C1500.87,-1581.3 1675.05,-1544.18 1750.47,-1428.56 1766.35,-1404.22 1763.72,-1389.03 1750.47,-1363.16 1741.19,-1345.05 1724.57,-1330.68 1707.86,-1319.93"/>
<polygon fill="#10f910" stroke="black" points="1709.73,-1316.97 1699.36,-1314.79 1706.11,-1322.96 1709.73,-1316.97"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_builder -->
<g id="edge96" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1964.47,-707.5C1897.16,-662.94 1964.47,-606.6 1964.47,-525.88 1964.47,-525.88 1964.47,-525.88 1964.47,-441.62 1964.47,-367.86 1406.43,-334.77 1239.25,-326.51"/>
<polygon fill="#10f910" stroke="black" points="1239.57,-323.03 1229.41,-326.04 1239.23,-330.02 1239.57,-323.03"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_depends -->
<g id="edge97" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1365.04,-1565.86C1363.52,-1531.88 1361.09,-1477.83 1359.4,-1440.03"/>
<polygon fill="#10f910" stroke="black" points="1362.91,-1440.21 1358.97,-1430.38 1355.92,-1440.52 1362.91,-1440.21"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_events -->
<g id="edge98" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1336.97,-1572.07C1323.48,-1559.84 1307.62,-1544.68 1294.47,-1529.97 1256.92,-1487.99 1249.55,-1475.53 1218.47,-1428.56 1199.91,-1400.53 1206.88,-1383.95 1180.47,-1363.16 1136.38,-1328.45 1104.5,-1360.63 1059.47,-1327.16 1031.54,-1306.4 1038.31,-1288.83 1016.47,-1261.75 995.75,-1236.06 977.05,-1239.26 964.47,-1208.75"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_plugin -->
<g id="edge99" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1353.02,-1567.09C1330.7,-1519 1282.06,-1424.82 1218.47,-1363.16 1198.34,-1343.65 1171.56,-1327.46 1148.9,-1315.81"/>
<polygon fill="#10f910" stroke="black" points="1150.69,-1312.79 1140.18,-1311.45 1147.56,-1319.05 1150.69,-1312.79"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_service -->
<g id="edge100" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1416.08,-1597.76C1553.34,-1597.23 1935.1,-1590.21 2042.47,-1529.97 2057.88,-1521.32 2057.47,-1513.48 2066.47,-1498.27"/>
<path fill="none" stroke="black" d="M2066.47,-1496.27C2119.57,-1406.51 1783.38,-1227.92 1451.47,-1070.5 1383.58,-1038.3 1301.65,-1008.3 1251.57,-990.9"/>
<polygon fill="#10f910" stroke="black" points="1252.8,-987.63 1242.21,-987.67 1250.52,-994.24 1252.8,-987.63"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_config_typed_config -->
<g id="edge101" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_config_typed_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1327.02,-1578.5C1315.48,-1573.64 1302.69,-1568.96 1290.47,-1565.97 1198.83,-1543.54 949.66,-1576.28 867.47,-1529.97 647.86,-1406.25 515.28,-1108.16 475.89,-1008.48"/>
<polygon fill="#10f910" stroke="black" points="479.2,-1007.31 472.3,-999.27 472.67,-1009.86 479.2,-1007.31"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_services_error_service -->
<g id="edge102" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_services_error_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2066.47,-1152.75C2040.92,-1106.56 2086.29,-1086.54 2077.47,-1034.5"/>
<path fill="none" stroke="black" d="M2077.47,-1033.5C2075.32,-1021.2 2067.82,-1010.89 2058.53,-1002.54"/>
<polygon fill="#10f910" stroke="black" points="2060.79,-999.86 2050.79,-996.36 2056.42,-1005.34 2060.79,-999.86"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge103" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1382.47,-707.5C1385.74,-687.22 1396.98,-667.89 1409.09,-652.15"/>
<polygon fill="blue" stroke="black" points="1411.72,-654.47 1415.3,-644.5 1406.28,-650.05 1411.72,-654.47"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge104" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1382.47,-973.38C1370.93,-936.64 1393.32,-923.69 1382.47,-886.75"/>
<path fill="none" stroke="black" d="M1382.47,-884.75C1325.56,-691.08 1063.75,-884.68 963.47,-709.5"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge105" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1421.36,-1070.05C1449.69,-1022.12 1512.2,-905.73 1521.47,-798.12"/>
<path fill="none" stroke="black" d="M1521.47,-796.12C1523.28,-773.97 1509.23,-754.57 1492.9,-739.77"/>
<polygon fill="blue" stroke="black" points="1495.29,-737.21 1485.39,-733.44 1490.78,-742.56 1495.29,-737.21"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge106" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1402.57,-1070.17C1397.46,-1046.58 1389.25,-1008.4 1382.47,-975.38"/>
<path fill="none" stroke="black" d="M1382.47,-973.38C1376.45,-954.23 1363.48,-936.78 1349.93,-922.65"/>
<polygon fill="blue" stroke="black" points="1352.58,-920.34 1343.01,-915.79 1347.65,-925.31 1352.58,-920.34"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge107" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1521.47,-796.12C1531.27,-676.35 1583.38,-622.9 1512.47,-525.88"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_app_plugin -->
<g id="edge108" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1534.47,-1033.5C1542.27,-1024.67 1550.71,-1015.07 1558.45,-1006.26"/>
<polygon fill="blue" stroke="black" points="1560.93,-1008.73 1564.89,-998.91 1555.67,-1004.12 1560.93,-1008.73"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_builder -->
<g id="edge109" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1382.47,-884.75C1351.23,-813.4 1370.05,-786.39 1382.47,-709.5"/>
<path fill="none" stroke="black" d="M1382.47,-707.5C1395.84,-653.6 1375.63,-633.16 1401.47,-584 1443.8,-503.46 1520.03,-529.37 1550.47,-443.62"/>
<path fill="none" stroke="black" d="M1550.47,-441.62C1557.49,-416.76 1553.93,-409.1 1550.47,-383.5"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_events -->
<g id="edge110" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1371.98,-1086.73C1300.48,-1074.34 1134.47,-1042.93 1000.47,-997.5 999.34,-997.12 998.2,-996.72 997.06,-996.31"/>
<polygon fill="blue" stroke="black" points="998.73,-993.2 988.14,-992.83 996.19,-999.72 998.73,-993.2"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_service -->
<g id="edge111" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1372.28,-1072.2C1338.6,-1052.65 1287.62,-1023.06 1250.88,-1001.74"/>
<polygon fill="blue" stroke="black" points="1252.64,-998.71 1242.23,-996.72 1249.13,-1004.76 1252.64,-998.71"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_config_models -->
<g id="edge112" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1372.11,-1087.74C1237.54,-1069.13 757.28,-1002.73 605.67,-981.76"/>
<polygon fill="blue" stroke="black" points="606.39,-978.33 596,-980.43 605.43,-985.26 606.39,-978.33"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_config_typed_config -->
<g id="edge113" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_config_typed_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1372.33,-1090.27C1228.24,-1080.41 682.4,-1040.88 515.47,-997.5 514.96,-997.37 514.44,-997.23 513.93,-997.09"/>
<polygon fill="blue" stroke="black" points="515.11,-993.79 504.52,-994.09 512.99,-1000.46 515.11,-993.79"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_services_error_service -->
<g id="edge114" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_services_error_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1442.88,-1085.73C1544.81,-1065.87 1839.26,-1008.53 1958.46,-985.31"/>
<polygon fill="blue" stroke="black" points="1958.81,-988.81 1967.95,-983.46 1957.47,-981.94 1958.81,-988.81"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_services_type_safety_service -->
<g id="edge115" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_services_type_safety_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1442.94,-1084.84C1471.44,-1076.82 1510.53,-1061.57 1534.47,-1034.5"/>
<path fill="none" stroke="black" d="M1534.47,-1033.5C1558.77,-1006.01 1517.37,-978.92 1541.47,-951.25 1589.05,-896.61 1634.71,-945.67 1700.47,-915.25 1748.02,-893.25 1795.08,-855.06 1824.87,-828.28"/>
<polygon fill="blue" stroke="black" points="1827.02,-831.06 1832.05,-821.73 1822.3,-825.88 1827.02,-831.06"/>
</g>
<!-- plugginger_services -->
<g id="node27" class="node">
<title>plugginger_services</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2075.47,-145 2005.47,-145 2005.47,-109 2075.47,-109 2075.47,-145"/>
<text text-anchor="middle" x="2040.47" y="-130.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2040.47" y="-117.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">services</text>
</g>
<!-- plugginger_services&#45;&gt;app_py -->
<g id="edge116" class="edge">
<title>plugginger_services&#45;&gt;app_py</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2024.23,-108.88C2016.63,-99.16 2009.11,-86.31 2008.47,-73"/>
</g>
<!-- plugginger_services&#45;&gt;plugginger_api_app -->
<g id="edge117" class="edge">
<title>plugginger_services&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2005.21,-141.61C2000.64,-142.97 1995.98,-144.15 1991.47,-145 1659.16,-207.49 797.25,-96.69 465.1,-199.69"/>
<polygon fill="blue" stroke="black" points="464.35,-196.26 455.91,-202.66 466.5,-202.92 464.35,-196.26"/>
<path fill="none" stroke="black" d="M454.47,-205.12C127.79,-308.52 90.47,-542.1 90.47,-884.75 90.47,-975.38 90.47,-975.38 90.47,-975.38 90.47,-1177.15 233.55,-1185.72 377.47,-1327.16 394.14,-1343.54 414.64,-1359.81 431.45,-1372.29"/>
</g>
<!-- plugginger_services&#45;&gt;plugginger_api_builder -->
<g id="edge118" class="edge">
<title>plugginger_services&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M454.47,-205.12C178.45,-310.09 976.75,-321.09 1157.1,-322.24"/>
</g>
<!-- plugginger_services_error_service&#45;&gt;plugginger_services -->
<g id="edge119" class="edge">
<title>plugginger_services_error_service&#45;&gt;plugginger_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1994.65,-951.13C1961.78,-898.71 1890.2,-765.34 1950.47,-679 1977.02,-640.95 2012.93,-672.84 2048.47,-643 2094.56,-604.3 2116.47,-586.07 2116.47,-525.88 2116.47,-525.88 2116.47,-525.88 2116.47,-263.25 2116.47,-220.85 2087.72,-179.44 2065.59,-153.85"/>
<polygon fill="blue" stroke="black" points="2068.38,-151.72 2059.1,-146.61 2063.17,-156.39 2068.38,-151.72"/>
</g>
<!-- plugginger_services_manifest_service&#45;&gt;plugginger_services -->
<g id="edge120" class="edge">
<title>plugginger_services_manifest_service&#45;&gt;plugginger_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1985.87,-180.59C1994.94,-171.85 2005.36,-161.82 2014.64,-152.87"/>
<polygon fill="blue" stroke="black" points="2016.92,-155.54 2021.69,-146.08 2012.06,-150.5 2016.92,-155.54"/>
</g>
<!-- plugginger_services_type_safety_service&#45;&gt;plugginger_services -->
<g id="edge121" class="edge">
<title>plugginger_services_type_safety_service&#45;&gt;plugginger_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1853.19,-773.65C1850.73,-747.53 1851.09,-704.73 1874.47,-679 1918.25,-630.81 1975.3,-693.44 2016.47,-643 2136.07,-496.46 2074.56,-240.9 2049.45,-156.18"/>
<polygon fill="blue" stroke="black" points="2052.8,-155.17 2046.55,-146.62 2046.11,-157.2 2052.8,-155.17"/>
</g>
<!-- pydantic -->
<g id="node31" class="node">
<title>pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#0606f9" stroke="black" cx="726.47" cy="-1598.68" rx="35.49" ry="18"/>
<text text-anchor="middle" x="726.47" y="-1595.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">pydantic</text>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge122" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M728.8,-1580.28C741.78,-1486.29 808.05,-1040.04 907.47,-951.25 958.78,-905.42 1165.63,-891.95 1259.6,-888.16"/>
<polygon fill="#0606f9" stroke="black" points="1259.64,-891.66 1269.5,-887.79 1259.38,-884.67 1259.64,-891.66"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge123" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M691.26,-1595.46C649.64,-1590.74 582.07,-1575.99 550.47,-1529.97 534.01,-1506.01 544.27,-1492.97 550.47,-1464.56 601.41,-1230.93 697.66,-1201.83 774.47,-975.38"/>
<path fill="none" stroke="black" d="M774.47,-973.38C806.97,-761.98 814,-689.73 951.47,-525.88"/>
<path fill="none" stroke="black" d="M951.47,-523.88C987.19,-480.71 1147.83,-458.16 1235.66,-448.88"/>
<polygon fill="#0606f9" stroke="black" points="1235.93,-452.37 1245.52,-447.86 1235.22,-445.41 1235.93,-452.37"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_app_plugin -->
<g id="edge124" class="edge">
<title>pydantic&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1128.47,-1496.27C1144.65,-1489.17 1137.21,-1473.49 1152.47,-1464.56 1254.29,-1404.98 1319.39,-1497.03 1415.47,-1428.56 1550.93,-1332.03 1536.64,-1253.94 1584.47,-1094.62"/>
<path fill="none" stroke="black" d="M1584.47,-1092.62C1597.91,-1056.63 1658.88,-1071.49 1648.47,-1034.5"/>
<path fill="none" stroke="black" d="M1648.47,-1033.5C1645.26,-1022.1 1637.96,-1012.17 1629.42,-1003.9"/>
<polygon fill="#0606f9" stroke="black" points="1631.91,-1001.44 1622.08,-997.48 1627.3,-1006.7 1631.91,-1001.44"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_builder -->
<g id="edge125" class="edge">
<title>pydantic&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M695.05,-1589.79C666.38,-1582.8 622.78,-1572.7 584.47,-1565.97 460.89,-1544.28 413.01,-1592.9 304.47,-1529.97 289.18,-1521.11 293.84,-1509.82 280.47,-1498.27"/>
<path fill="none" stroke="black" d="M280.47,-1496.27C267.09,-1484.72 267.22,-1478.59 256.47,-1464.56 244.13,-1448.48 240.81,-1444.65 228.47,-1428.56 217.71,-1414.54 215.17,-1410.92 204.47,-1396.86"/>
<path fill="none" stroke="black" d="M204.47,-1394.86C145.71,-1317.68 90.47,-1305.75 90.47,-1208.75 90.47,-1208.75 90.47,-1208.75 90.47,-1152.75 90.47,-949.59 391.69,-447.61 584.47,-383.5"/>
<path fill="none" stroke="black" d="M584.47,-382.5C681.28,-330.19 1020.77,-324.49 1145.79,-324.21"/>
<polygon fill="#0606f9" stroke="black" points="1145.69,-327.71 1155.69,-324.2 1145.68,-320.71 1145.69,-327.71"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_plugin -->
<g id="edge126" class="edge">
<title>pydantic&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M760.85,-1593.28C831.98,-1583.32 999.42,-1555.31 1128.47,-1498.27"/>
<path fill="none" stroke="black" d="M1128.47,-1496.27C1158.47,-1483.01 1131.4,-1382.26 1114.56,-1328.7"/>
<polygon fill="#0606f9" stroke="black" points="1117.99,-1327.94 1111.61,-1319.48 1111.32,-1330.07 1117.99,-1327.94"/>
</g>
<!-- pydantic&#45;&gt;plugginger_config_models -->
<g id="edge127" class="edge">
<title>pydantic&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M280.47,-1496.27C197.67,-1423.96 449.03,-1107.67 533.09,-1006.57"/>
<polygon fill="#0606f9" stroke="black" points="535.77,-1008.82 539.49,-998.9 530.39,-1004.34 535.77,-1008.82"/>
</g>
<!-- pydantic&#45;&gt;plugginger_config_typed_config -->
<g id="edge128" class="edge">
<title>pydantic&#45;&gt;plugginger_config_typed_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M204.47,-1394.86C125.53,-1273.76 143.3,-1187.3 228.47,-1070.5 271.56,-1011.4 357.73,-988.94 412.89,-980.46"/>
<polygon fill="#0606f9" stroke="black" points="413.26,-983.94 422.67,-979.07 412.27,-977.01 413.26,-983.94"/>
</g>
</g>
</svg>
