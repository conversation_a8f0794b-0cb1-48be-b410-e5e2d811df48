# Plugginger

**Build complex, AI-ready apps from bite-sized modules – the first framework designed for humans *and* AI agents.**

---

## Why Should I Care?

| ❤️ **Benefit** | **What it means for you** |
|---|---|
| **KI-First Design** | Each plugin describes itself in machine-readable manifests → AI agents can understand, generate, and modify your app autonomously. |
| **Plug-and-Play Architecture** | Drop in ready-made plugins (auth, vector search, OpenAI chat) instead of wiring services by hand. |
| **60-Second Plugins** | From idea to working plugin in under a minute using `plugginger new plugin`. |
| **Framework-Agnostic Future** | Compile your app to pure Flask/FastAPI/Django code – no vendor lock-in. |
| **Async by Default** | Built for real-time APIs, chatbots, and data pipelines without callback hell. |
| **Fault Isolation** | Misbehaving plugins stay sandboxed – your app keeps running. |

---

## Typical Use-Cases

- **AI Chat Applications** combining OpenAI + vector search + web API – assembled in minutes, not days
- **Event-Driven Microservices** where each business domain is an independent plugin
- **Rapid Prototyping** for hackathons and feature experiments without touching core systems
- **AI Agent Workflows** where LLMs autonomously extend your application with new capabilities

---

## Quick Start (10 Minutes)

> You don't need to understand Plugginger internals – just follow these steps.

### Prerequisites
- Python 3.11+
- Git

### 1. Install Plugginger
```bash
pip install plugginger
```

### 2. Clone & Run the Reference App
```bash
git clone -b s1-reference-app-design https://github.com/jkehrhahn/plugginger.git
cd plugginger/examples/ai-chat-reference

python app.py
# → AI-Chat app running on http://localhost:8000
```

### 3. Test Your AI Chat
```bash
# Health check
curl http://localhost:8000/health

# Send a message
curl -X POST http://localhost:8000/chat \
     -H "Content-Type: application/json" \
     -d '{"message": "Hello, Plugginger!"}'

# Explore API docs
open http://localhost:8000/docs
```

### 4. Inspect the Plugin Architecture
```bash
# See the complete app structure (AI-readable JSON)
python -m plugginger.cli inspect app:create_app_for_inspection --json
```

---

## Next Steps for Interested Users

| **Step** | **What to do** | **Learn More** |
|---|---|---|
| **Explore Plugins** | `ls plugins/` – browse the code, each plugin is ≤ 100 LoC | [Plugin Architecture](examples/ai-chat-reference/ARCHITECTURE.md) |
| **Create Your Plugin** | `plugginger new plugin my_service` – generates working plugin template | [Plugin Development](docs/core-api/plugin.md) |
| **Understand Manifests** | Check `plugins/*/manifest.yaml` – see how plugins describe themselves | [Manifest Schema](docs/schemas/) |
| **Build Custom Apps** | Copy the reference app pattern for your use case | [App Builder Guide](docs/core-api/builder.md) |
| **AI Integration** | Use `plugginger inspect --json` for AI agent integration | [AI Agent Compatibility](docs/ai-integration/) |

---

## API Stability (v0.9.0-alpha)

Plugginger follows a clear [API stability policy](docs/BREAKING_CHANGE_POLICY.md) to help you build with confidence:

| **API Level** | **Stability** | **Breaking Changes** | **Use Case** |
|---|---|---|---|
| ✅ **Stable Candidates** | High | Only with major version bump | Production apps |
| 🧪 **Experimental** | None | May change without notice | Prototyping, testing |
| 🔧 **Internal** | None | Implementation details | Framework development |

### Stable Candidate APIs (Safe for Production)
- `plugginger.api.plugin` - Plugin creation (`@plugin`, `PluginBase`)
- `plugginger.api.service` - Service decoration (`@service`)  
- `plugginger.api.events` - Event listeners (`@onevent`)
- `plugginger.api.builder` - Application building (`PluggingerAppBuilder`)
- `plugginger.cli` - Command-line tools (`inspect`, `new plugin`)

### Experimental APIs (Use with Caution)
- `plugginger.experimental.*` - Advanced features that may change
- Compiler functionality (planned for v1.1.0)

📖 **Full Policy:** [Breaking Change Policy](docs/BREAKING_CHANGE_POLICY.md)

---

## What Makes Plugginger Different?

**🤖 AI-Native:** The first framework designed from the ground up for AI agent collaboration. Every plugin has a machine-readable manifest that AI can understand and modify.

**📦 No Lock-In:** Future compiler will generate pure Python code for any framework – develop with Plugginger magic, deploy anywhere.

**⚡ Production Ready:** Despite being alpha, the reference app demonstrates real-world AI chat functionality with proper error handling, testing, and documentation.

**🔍 Introspectable:** `plugginger inspect --json` gives AI agents complete visibility into your app's structure without executing code.

---

## Community & Support

- **🐛 Issues & Ideas** – https://github.com/jkehrhahn/plugginger/issues
- **📚 Documentation** – [docs/](docs/) (comprehensive guides and API reference)
- **💬 Discussions** – GitHub Discussions for questions and feedback
- **🚀 Changelog** – [CHANGELOG.md](CHANGELOG.md) – track new features and breaking changes

**Early alpha:** Bugs expected – we love bug reports and feedback! 

---

## License

MIT — use it, fork it, ship it 🚀

---

> **Next Release:** v0.9.1-alpha with enhanced error handling and configuration system (Est. June 2025)

[1] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/collection_33b2c7de-1d40-4fa8-b8a3-980b5c981d4d/b168a3aa-6b65-48ea-b26d-7dfbf46ac6d8/plugginger.md
[2] https://github.com/panaversity/learn-modern-ai-python/blob/main/README.md
[3] https://benhouston3d.com/blog/agentic-coding-best-practices
[4] https://github.com/devmentors/modular-framework/blob/master/README.md
[5] https://www.datacamp.com/blog/top-ai-frameworks-and-libraries
[6] https://5ly.co/blog/best-ai-frameworks/
[7] https://datasciencedojo.com/blog/python-libraries-for-generative-ai/
[8] https://github.com/eli64s/readme-ai
[9] https://ai.pydantic.dev
[10] https://docs.dify.ai/plugin-dev-en/0211-getting-started-by-prompt
[11] https://www.docker.com/blog/readmeai-an-ai-powered-readme-generator-for-developers/