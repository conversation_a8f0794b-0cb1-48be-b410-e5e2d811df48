# Plugginger

**Build complex, AI-ready apps from bite-sized modules – the first framework designed for humans *and* AI agents.**

---

## Why Should I Care?

| ❤️ **Benefit** | **What it means for you** |
|---|---|
| **AI-Powered Generation** | Generate working plugins from natural language prompts using OpenAI, Gemini, or local Ollama models. |
| **Intelligent Model Selection** | Automatic optimization for speed, cost, or quality based on your task requirements. |
| **Multi-Provider Support** | Choose between OpenAI (quality), Gemini (cost-effective), or Ollama (free/local) based on your needs. |
| **60-Second Plugins** | From idea to working plugin in under a minute using AI-powered `plugginger new plugin`. |
| **Framework-Agnostic Future** | Compile your app to pure Flask/FastAPI/Django code – no vendor lock-in. |
| **Async by Default** | Built for real-time APIs, chatbots, and data pipelines without callback hell. |
| **Fault Isolation** | Misbehaving plugins stay sandboxed – your app keeps running. |

---

## Typical Use-Cases

- **AI-Generated Microservices** – Describe your service in plain English, get production-ready code instantly
- **Rapid MVP Development** – Generate complete plugin ecosystems for hackathons and prototypes in minutes
- **Cost-Optimized AI Development** – Use free Ollama for development, switch to Gemini for production cost efficiency
- **Multi-Model AI Workflows** – Leverage different LLM providers for different tasks (Ollama for speed, OpenAI for quality)
- **AI Agent Collaboration** – LLMs autonomously extend your application with new capabilities using natural language

---

## Quick Start (5 Minutes)

> Experience AI-powered plugin generation in under 5 minutes.

### Prerequisites
- Python 3.11+
- OpenAI API key OR Google API key OR Ollama running locally

### 1. Install Plugginger
```bash
pip install plugginger
# Install optional dependencies for LLM providers
pip install openai google-generativeai aiohttp
```

### 2. Generate Your First AI Plugin
```bash
# Set your API key (choose one)
export OPENAI_API_KEY="your-openai-key"
export GOOGLE_API_KEY="your-google-key"
# OR start Ollama locally: ollama serve

# Generate a plugin with natural language
plugginger new plugin email_service \
  --prompt "Create an email notification service with SMTP support" \
  --model-tier balanced \
  --preferred-provider gemini

# → Plugin generated in ./email_service/ with working code!
```

### 3. Explore AI Model Selection
```bash
# Fast prototyping with local Ollama
plugginger new plugin quick_test \
  --prompt "Simple hello world service" \
  --model-tier fast \
  --preferred-provider ollama \
  --max-response-time 1000

# High-quality generation with OpenAI
plugginger new plugin production_service \
  --prompt "Complex data processing service with validation" \
  --model-tier quality \
  --preferred-provider openai \
  --quality-threshold 0.8

# Cost-conscious development with Gemini
plugginger new plugin budget_service \
  --prompt "Create user authentication service" \
  --model-tier balanced \
  --preferred-provider gemini \
  --max-cost 0.001
```

### 4. Inspect Generated Plugins
```bash
# See the complete plugin structure (AI-readable JSON)
cd email_service
plugginger inspect --json
```

---

## Next Steps for Interested Users

| **Step** | **What to do** | **Learn More** |
|---|---|---|
| **Master AI Generation** | Try different prompts and model tiers to see quality differences | [CLI Model Selection Guide](CLI_MODEL_SELECTION_DESIGN.md) |
| **Optimize for Your Workflow** | Configure cost limits, response times, and provider preferences | [Model Selection Parameters](#ai-model-selection) |
| **Explore Generated Code** | Examine AI-generated plugins to understand the framework patterns | [Plugin Architecture](docs/core-api/plugin.md) |
| **Build Custom Apps** | Combine multiple AI-generated plugins into complete applications | [App Builder Guide](docs/core-api/builder.md) |
| **AI Agent Integration** | Use `plugginger inspect --json` for AI agent integration | [AI Agent Compatibility](docs/ai-integration/) |

---

## AI Model Selection

Plugginger supports intelligent model selection across three LLM providers, each optimized for different use cases:

### Provider Comparison

| **Provider** | **Best For** | **Speed** | **Cost** | **Setup** |
|---|---|---|---|---|
| **Ollama** | Development, Privacy | ⚡ Fast (100ms) | 💰 Free | Local install |
| **Gemini** | Production, Cost-efficiency | ⚡ Fast (450ms) | 💰 Very cheap | API key |
| **OpenAI** | Quality, Complex tasks | 🐌 Slower (940ms) | 💸 Expensive | API key |

### Model Selection Parameters

```bash
# Performance tiers
--model-tier fast      # Prioritize speed (→ Ollama granite3-dense:2b)
--model-tier balanced  # Balance speed/quality (→ Gemini gemini-1.5-flash)
--model-tier quality   # Prioritize quality (→ OpenAI gpt-4o)

# Cost control
--max-cost 0.001      # Maximum cost per 1k tokens
--max-cost 0          # Free models only (→ Ollama)

# Performance requirements
--max-response-time 500   # Maximum response time in milliseconds

# Provider preference
--preferred-provider ollama   # Force specific provider
--preferred-provider gemini
--preferred-provider openai
```

### Automatic Task Detection

Plugginger automatically selects optimal models based on your prompt:

- **Code Generation** → Prefers `qwen2.5-coder:7b` (Ollama) or models with coding specialization
- **Structured JSON** → Prefers models with strong JSON support and validation
- **Simple Text** → Prefers fast models like `granite3-dense:2b` for quick responses

---

## API Stability (v6.1.0-alpha)

Plugginger follows a clear [API stability policy](docs/BREAKING_CHANGE_POLICY.md) to help you build with confidence:

| **API Level** | **Stability** | **Breaking Changes** | **Use Case** |
|---|---|---|---|
| ✅ **Stable Candidates** | High | Only with major version bump | Production apps |
| 🧪 **Experimental** | None | May change without notice | Prototyping, testing |
| 🔧 **Internal** | None | Implementation details | Framework development |

### Stable Candidate APIs (Safe for Production)
- `plugginger.api.plugin` - Plugin creation (`@plugin`, `PluginBase`)
- `plugginger.api.service` - Service decoration (`@service`)
- `plugginger.api.events` - Event listeners (`@onevent`)
- `plugginger.api.builder` - Application building (`PluggingerAppBuilder`)
- `plugginger.cli` - Command-line tools (`inspect`, `new plugin`) **NEW: AI model selection**

### Experimental APIs (Use with Caution)
- `plugginger.experimental.*` - Advanced features that may change
- Compiler functionality (planned for v1.1.0)

📖 **Full Policy:** [Breaking Change Policy](docs/BREAKING_CHANGE_POLICY.md)

---

## What Makes Plugginger Different?

**🤖 AI-Powered Generation:** The first framework with built-in AI plugin generation. Generate working code from natural language using OpenAI, Gemini, or local Ollama models.

**🎯 Intelligent Model Selection:** Automatic optimization for your specific needs – fast local development with Ollama, cost-effective production with Gemini, or highest quality with OpenAI.

**📦 No Lock-In:** Future compiler will generate pure Python code for any framework – develop with Plugginger magic, deploy anywhere.

**⚡ Production Ready:** Real LLM integration with comprehensive error handling, retry logic, and fallback mechanisms. All providers tested with actual API calls.

**🔍 Introspectable:** `plugginger inspect --json` gives AI agents complete visibility into your app's structure without executing code.

---

## Community & Support

- **🐛 Issues & Ideas** – https://github.com/jkehrhahn/plugginger/issues
- **📚 Documentation** – [docs/](docs/) (comprehensive guides and API reference)
- **💬 Discussions** – GitHub Discussions for questions and feedback
- **🚀 Changelog** – [CHANGELOG.md](CHANGELOG.md) – track new features and breaking changes

**Early alpha:** Bugs expected – we love bug reports and feedback! 

---

## License

MIT — use it, fork it, ship it 🚀
