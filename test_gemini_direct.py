#!/usr/bin/env python3
"""
Direct Gemini API Test - Framework Independent
"""

import asyncio
import aiohttp
import json
import time
import os


async def test_gemini_simple():
    """Test simple text generation with Gemini via REST API."""
    print("🔄 Testing Gemini Simple Text Generation...")
    
    api_key = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    data = {
        "contents": [{
            "parts": [{
                "text": "Hello, how are you? Please respond briefly."
            }]
        }],
        "generationConfig": {
            "maxOutputTokens": 50,
            "temperature": 0.1
        }
    }
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    result = await response.json()
                    elapsed = time.time() - start_time
                    
                    candidates = result.get('candidates', [])
                    if candidates:
                        content = candidates[0].get('content', {})
                        parts = content.get('parts', [])
                        if parts:
                            text = parts[0].get('text', '')
                            
                            print(f"✅ Success! ({elapsed:.2f}s)")
                            print(f"   Response: {text[:100]}...")
                            
                            # Check usage metadata
                            usage = result.get('usageMetadata', {})
                            if usage:
                                print(f"   Tokens: {usage.get('totalTokenCount', 0)}")
                            
                            return True
                    
                    print(f"❌ No content in response: {result}")
                    return False
                    
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Exception after {elapsed:.2f}s: {e}")
        return False


async def test_gemini_json():
    """Test JSON generation with Gemini via REST API."""
    print("\n🔄 Testing Gemini JSON Generation...")
    
    api_key = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    data = {
        "contents": [{
            "parts": [{
                "text": "Create a JSON object with 'name' and 'description' for a hello world plugin. Respond only with valid JSON, no additional text."
            }]
        }],
        "generationConfig": {
            "maxOutputTokens": 100,
            "temperature": 0.1,
            "responseMimeType": "application/json"
        }
    }
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    result = await response.json()
                    elapsed = time.time() - start_time
                    
                    candidates = result.get('candidates', [])
                    if candidates:
                        content = candidates[0].get('content', {})
                        parts = content.get('parts', [])
                        if parts:
                            text = parts[0].get('text', '')
                            
                            print(f"✅ Success! ({elapsed:.2f}s)")
                            print(f"   Raw Response: {text}")
                            
                            # Try to parse JSON
                            try:
                                parsed = json.loads(text)
                                print(f"   ✅ Valid JSON: {parsed}")
                                return True
                            except json.JSONDecodeError as e:
                                print(f"   ❌ Invalid JSON: {e}")
                                return False
                    
                    print(f"❌ No content in response: {result}")
                    return False
                    
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Exception after {elapsed:.2f}s: {e}")
        return False


async def test_gemini_models():
    """Test available models in Gemini."""
    print("\n🔄 Testing Gemini Available Models...")
    
    api_key = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"
    url = f"https://generativelanguage.googleapis.com/v1beta/models?key={api_key}"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    result = await response.json()
                    models = result.get('models', [])
                    
                    # Filter for generation models
                    gen_models = [m for m in models if 'generateContent' in m.get('supportedGenerationMethods', [])]
                    
                    print(f"✅ Found {len(gen_models)} generation models:")
                    for model in gen_models[:5]:  # Show first 5
                        name = model.get('name', 'unknown').split('/')[-1]
                        print(f"   - {name}")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


async def main():
    """Run all Gemini tests."""
    print("🚀 Starting Gemini Direct API Tests\n")
    
    results = []
    
    # Test 1: Available models
    results.append(await test_gemini_models())
    
    # Test 2: Simple text generation
    results.append(await test_gemini_simple())
    
    # Test 3: JSON generation
    results.append(await test_gemini_json())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All Gemini tests passed!")
    else:
        print("⚠️  Some Gemini tests failed!")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
