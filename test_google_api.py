#!/usr/bin/env python3
"""
Framework-independent Google API Key test script.
Tests the Google API key with different configurations.
"""

import os
import sys

def test_direct_google_api():
    """Test Google API directly using google-generativeai library."""
    print("=== Testing Direct Google API ===")
    
    try:
        import google.generativeai as genai
        
        api_key = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"
        genai.configure(api_key=api_key)
        
        # Test with Gemini model
        model = genai.GenerativeModel('gemini-1.5-flash')
        response = model.generate_content("Say hello in 3 words")
        
        print(f"✅ SUCCESS: {response.text}")
        return True
        
    except ImportError:
        print("❌ google-generativeai not installed")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_litellm_gemini():
    """Test Google API through LiteLLM."""
    print("\n=== Testing LiteLLM Gemini ===")
    
    try:
        import litellm
        
        api_key = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"
        
        # Test different model formats
        test_configs = [
            ("gemini/gemini-1.5-flash", "Gemini format"),
            ("vertex_ai/gemini-1.5-flash", "Vertex AI format"),
            ("google/gemini-1.5-flash", "Google format"),
            ("gemini-1.5-flash", "Direct model name")
        ]
        
        for model, description in test_configs:
            try:
                print(f"Testing {description}: {model}")
                response = litellm.completion(
                    model=model,
                    messages=[{"role": "user", "content": "Hello"}],
                    api_key=api_key,
                    max_tokens=10
                )
                print(f"✅ SUCCESS ({description}): {response.choices[0].message.content}")
                return True
                
            except Exception as e:
                print(f"❌ FAILED ({description}): {str(e)[:100]}")
                continue
        
        return False
        
    except ImportError:
        print("❌ litellm not installed")
        return False

def test_requests_api():
    """Test Google API using direct HTTP requests."""
    print("\n=== Testing Direct HTTP Requests ===")
    
    try:
        import requests
        
        api_key = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": "Say hello in 3 words"
                }]
            }]
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        response = requests.post(url, json=payload, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if "candidates" in data and data["candidates"]:
                text = data["candidates"][0]["content"]["parts"][0]["text"]
                print(f"✅ SUCCESS: {text}")
                return True
            else:
                print(f"❌ No content in response: {data}")
                return False
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            return False
            
    except ImportError:
        print("❌ requests not installed")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def main():
    """Run all tests."""
    print("Google API Key Test Script")
    print("=" * 50)
    
    api_key = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"
    print(f"Testing API Key: {api_key[:20]}...")
    
    results = []
    
    # Test 1: Direct Google API
    results.append(test_direct_google_api())
    
    # Test 2: LiteLLM
    results.append(test_litellm_gemini())
    
    # Test 3: Direct HTTP
    results.append(test_requests_api())
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    print(f"Direct Google API: {'✅ PASS' if results[0] else '❌ FAIL'}")
    print(f"LiteLLM: {'✅ PASS' if results[1] else '❌ FAIL'}")
    print(f"Direct HTTP: {'✅ PASS' if results[2] else '❌ FAIL'}")
    
    if any(results):
        print("\n🎉 At least one method works - API key is valid!")
        return 0
    else:
        print("\n💥 All methods failed - API key may be invalid or restricted")
        return 1

if __name__ == "__main__":
    sys.exit(main())
