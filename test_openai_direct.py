#!/usr/bin/env python3
"""
Direct OpenAI API Test - Framework Independent
"""

import asyncio
import aiohttp
import json
import time
import os


async def test_openai_simple():
    """Test simple text generation with OpenAI via REST API."""
    print("🔄 Testing OpenAI Simple Text Generation...")
    
    # Check for API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY not set, skipping test")
        return False
    
    url = "https://api.openai.com/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "user", "content": "Hello, how are you? Please respond briefly."}
        ],
        "max_tokens": 50,
        "temperature": 0.1
    }
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    result = await response.json()
                    elapsed = time.time() - start_time
                    
                    choices = result.get('choices', [])
                    if choices:
                        message = choices[0].get('message', {})
                        content = message.get('content', '')
                        
                        print(f"✅ Success! ({elapsed:.2f}s)")
                        print(f"   Response: {content[:100]}...")
                        
                        # Check usage
                        usage = result.get('usage', {})
                        if usage:
                            print(f"   Tokens: {usage.get('total_tokens', 0)}")
                        
                        return True
                    
                    print(f"❌ No choices in response: {result}")
                    return False
                    
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Exception after {elapsed:.2f}s: {e}")
        return False


async def test_openai_json():
    """Test JSON generation with OpenAI via REST API."""
    print("\n🔄 Testing OpenAI JSON Generation...")
    
    # Check for API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ OPENAI_API_KEY not set, skipping test")
        return False
    
    url = "https://api.openai.com/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant that responds in JSON format."},
            {"role": "user", "content": "Create a JSON object with 'name' and 'description' for a hello world plugin."}
        ],
        "max_tokens": 100,
        "temperature": 0.1,
        "response_format": {"type": "json_object"}
    }
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    result = await response.json()
                    elapsed = time.time() - start_time
                    
                    choices = result.get('choices', [])
                    if choices:
                        message = choices[0].get('message', {})
                        content = message.get('content', '')
                        
                        print(f"✅ Success! ({elapsed:.2f}s)")
                        print(f"   Raw Response: {content}")
                        
                        # Try to parse JSON
                        try:
                            parsed = json.loads(content)
                            print(f"   ✅ Valid JSON: {parsed}")
                            return True
                        except json.JSONDecodeError as e:
                            print(f"   ❌ Invalid JSON: {e}")
                            return False
                    
                    print(f"❌ No choices in response: {result}")
                    return False
                    
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Exception after {elapsed:.2f}s: {e}")
        return False


async def main():
    """Run all OpenAI tests."""
    print("🚀 Starting OpenAI Direct API Tests\n")
    
    # Check if API key is available
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("⚠️  OPENAI_API_KEY not set. Set it with:")
        print("   export OPENAI_API_KEY='your-api-key'")
        print("   Skipping OpenAI tests.")
        return False
    
    print(f"🔑 Using API key: {api_key[:10]}...{api_key[-4:]}")
    
    results = []
    
    # Test 1: Simple text generation
    results.append(await test_openai_simple())
    
    # Test 2: JSON generation
    results.append(await test_openai_json())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All OpenAI tests passed!")
    else:
        print("⚠️  Some OpenAI tests failed!")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
