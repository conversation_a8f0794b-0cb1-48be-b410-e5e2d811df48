<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: G Pages: 1 -->
<svg width="5604pt" height="3297pt"
 viewBox="0.00 0.00 5603.59 3297.27" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 3293.27)">
<title>G</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="white" stroke="none" points="-4,4 -4,-3293.27 5599.59,-3293.27 5599.59,4 -4,4"/>
<!-- jsonschema -->
<g id="node1" class="node">
<title>jsonschema</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#cc3333" stroke="black" cx="536" cy="-1138.82" rx="45.58" ry="18"/>
<text text-anchor="middle" x="536" y="-1135.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">jsonschema</text>
</g>
<!-- plugginger_plugins_core_json_validator_services_validation_service -->
<g id="node82" class="node">
<title>plugginger_plugins_core_json_validator_services_validation_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#40bfc0" stroke="black" cx="656" cy="-803.29" rx="74.42" ry="59.75"/>
<text text-anchor="middle" x="656" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="656" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="656" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="656" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">json_validator.</text>
<text text-anchor="middle" x="656" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="656" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">validation_service</text>
</g>
<!-- jsonschema&#45;&gt;plugginger_plugins_core_json_validator_services_validation_service -->
<g id="edge1" class="edge">
<title>jsonschema&#45;&gt;plugginger_plugins_core_json_validator_services_validation_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M645,-899.04C643.88,-890.98 643.59,-882.52 643.86,-874.11"/>
<polygon fill="#cc3333" stroke="black" points="647.35,-874.42 644.46,-864.23 640.36,-874 647.35,-874.42"/>
</g>
<!-- plugginger_schemas_json_app_graph -->
<g id="node108" class="node">
<title>plugginger_schemas_json_app_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#33cccc" stroke="black" cx="477" cy="-803.29" rx="49.5" ry="41.72"/>
<text text-anchor="middle" x="477" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="477" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">schemas.</text>
<text text-anchor="middle" x="477" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">json.</text>
<text text-anchor="middle" x="477" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">app_graph</text>
</g>
<!-- jsonschema&#45;&gt;plugginger_schemas_json_app_graph -->
<g id="edge2" class="edge">
<title>jsonschema&#45;&gt;plugginger_schemas_json_app_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M544.05,-1120.84C569.31,-1067.43 645.37,-906.09 645,-900.04"/>
<path fill="none" stroke="black" d="M645,-899.04C640.06,-863.6 604.29,-880.39 573,-863.04 557.44,-854.41 540.81,-844.48 525.83,-835.28"/>
<polygon fill="#cc3333" stroke="black" points="527.88,-832.44 517.54,-830.15 524.2,-838.39 527.88,-832.44"/>
</g>
<!-- openai -->
<g id="node2" class="node">
<title>openai</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#bfc040" stroke="black" cx="2822" cy="-1138.82" rx="29.98" ry="18"/>
<text text-anchor="middle" x="2822" y="-1135.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">openai</text>
</g>
<!-- plugginger_plugins_core_llm_provider_services_provider_service -->
<g id="node86" class="node">
<title>plugginger_plugins_core_llm_provider_services_provider_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#40bfc0" stroke="black" cx="1592" cy="-803.29" rx="68.59" ry="59.75"/>
<text text-anchor="middle" x="1592" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1592" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="1592" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1592" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">llm_provider.</text>
<text text-anchor="middle" x="1592" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="1592" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">provider_service</text>
</g>
<!-- openai&#45;&gt;plugginger_plugins_core_llm_provider_services_provider_service -->
<g id="edge3" class="edge">
<title>openai&#45;&gt;plugginger_plugins_core_llm_provider_services_provider_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2825.07,-1120.52C2831.91,-1075.75 2842.98,-955.59 2777,-899.04 2730.49,-859.18 1733.06,-882.56 1675,-863.04 1666.65,-860.23 1658.39,-856.28 1650.52,-851.75"/>
<polygon fill="#bfc040" stroke="black" points="1652.41,-848.8 1642.06,-846.52 1648.73,-854.76 1652.41,-848.8"/>
</g>
<!-- packaging -->
<g id="node3" class="node">
<title>packaging</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#53b653" stroke="black" cx="1704" cy="-2161.48" rx="40.08" ry="18"/>
<text text-anchor="middle" x="1704" y="-2158.36" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">packaging</text>
</g>
<!-- plugginger__internal_validation_dependency_validation -->
<g id="node21" class="node">
<title>plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1775.62,-1834.76 1646.38,-1834.76 1646.38,-1775.76 1775.62,-1775.76 1775.62,-1834.76"/>
<text text-anchor="middle" x="1711" y="-1821.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1711" y="-1808.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1711" y="-1795.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">validation.</text>
<text text-anchor="middle" x="1711" y="-1783.01" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dependency_validation</text>
</g>
<!-- packaging&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge4" class="edge">
<title>packaging&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1691.7,-2144.18C1680.67,-2128.54 1665.15,-2103.82 1658,-2079.67"/>
</g>
<!-- packaging_specifiers -->
<g id="node4" class="node">
<title>packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#4cb34c" stroke="black" cx="1560" cy="-2161.48" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="1560" y="-2164.73" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">packaging.</text>
<text text-anchor="middle" x="1560" y="-2151.98" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">specifiers</text>
</g>
<!-- packaging_specifiers&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge5" class="edge">
<title>packaging_specifiers&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1599.48,-2147.47C1630.7,-2134.57 1667.58,-2112.02 1658,-2079.67"/>
<path fill="none" stroke="black" d="M1658,-2077.67C1645.32,-2034.85 1586.31,-2023.64 1613,-1987.84"/>
</g>
<!-- packaging_version -->
<g id="node5" class="node">
<title>packaging_version</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c247" stroke="black" cx="1662" cy="-2244.86" rx="48.44" ry="23.69"/>
<text text-anchor="middle" x="1662" y="-2248.11" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">packaging.</text>
<text text-anchor="middle" x="1662" y="-2235.36" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">version</text>
</g>
<!-- packaging_version&#45;&gt;packaging_specifiers -->
<g id="edge6" class="edge">
<title>packaging_version&#45;&gt;packaging_specifiers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1637.31,-2224.16C1624.25,-2213.74 1608.02,-2200.79 1593.81,-2189.46"/>
<polygon fill="#47c247" stroke="black" points="1596,-2186.73 1586,-2183.23 1591.63,-2192.2 1596,-2186.73"/>
</g>
<!-- packaging_version&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge7" class="edge">
<title>packaging_version&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1658.61,-2220.78C1657.18,-2209.99 1655.7,-2196.95 1655,-2185.17 1653.75,-2164.15 1654.46,-2158.84 1655,-2137.79 1655.67,-2111.93 1665.35,-2104.47 1658,-2079.67"/>
</g>
<!-- plugginger__internal -->
<g id="node6" class="node">
<title>plugginger__internal</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f9f9" stroke="black" cx="3475" cy="-2328.23" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="3475" y="-2331.48" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="3475" y="-2318.73" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal</text>
</g>
<!-- plugginger_api_app -->
<g id="node35" class="node">
<title>plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1228,-2184.61 1158,-2184.61 1158,-2138.36 1228,-2138.36 1228,-2184.61"/>
<text text-anchor="middle" x="1193" y="-2171.11" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1193" y="-2158.36" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1193" y="-2145.61" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_app -->
<g id="edge8" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3425.31,-2325.71C3129.87,-2316.65 1616.61,-2270.2 1605,-2268.55 1469.44,-2249.27 1314.16,-2202.29 1238.81,-2177.84"/>
<polygon fill="#10f9f9" stroke="black" points="1240.31,-2174.65 1229.72,-2174.87 1238.14,-2181.3 1240.31,-2174.65"/>
</g>
<!-- plugginger_api_builder -->
<g id="node38" class="node">
<title>plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1239,-826.41 1169,-826.41 1169,-780.16 1239,-780.16 1239,-826.41"/>
<text text-anchor="middle" x="1204" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1204" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="1204" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_builder -->
<g id="edge9" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3478.54,-2304.25C3480.25,-2287.65 3480.84,-2264.9 3475,-2245.86"/>
<path fill="none" stroke="black" d="M3475,-2243.86C3385.21,-1951.31 3193.56,-1981.56 3006,-1739.76 2817.92,-1497.29 2786.42,-1424.29 2636,-1156.82 2581.56,-1060.03 2595.11,-1016.91 2519,-936.04 2497.95,-913.67 2483.45,-919.88 2460,-900.04"/>
</g>
<!-- plugginger_api_events -->
<g id="node40" class="node">
<title>plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="3498,-1075.24 3428,-1075.24 3428,-1028.99 3498,-1028.99 3498,-1075.24"/>
<text text-anchor="middle" x="3463" y="-1061.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="3463" y="-1048.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="3463" y="-1036.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">events</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_events -->
<g id="edge10" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3475,-2243.86C3464.39,-2209.28 3474.78,-2198.65 3475,-2162.48"/>
<path fill="none" stroke="black" d="M3475,-2160.48C3475.16,-2133.77 3464.91,-2128.35 3462,-2101.79 3454.86,-2036.56 3459.09,-2019.7 3462,-1954.14 3470.45,-1763.52 3455.07,-1711.95 3500,-1526.51 3512.48,-1474.99 3595.02,-1358.35 3590,-1305.57"/>
</g>
<!-- plugginger_api_service -->
<g id="node42" class="node">
<title>plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="3818,-1075.24 3748,-1075.24 3748,-1028.99 3818,-1028.99 3818,-1075.24"/>
<text text-anchor="middle" x="3783" y="-1061.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="3783" y="-1048.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="3783" y="-1036.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">service</text>
</g>
<!-- plugginger__internal&#45;&gt;plugginger_api_service -->
<g id="edge11" class="edge">
<title>plugginger__internal&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3475,-2160.48C3484.31,-2032.42 3590,-2023.85 3590,-1895.45 3590,-1895.45 3590,-1895.45 3590,-1709.26 3590,-1580.85 3727.78,-1201.02 3770.51,-1086.3"/>
<polygon fill="#10f9f9" stroke="black" points="3773.71,-1087.75 3773.93,-1077.16 3767.15,-1085.3 3773.71,-1087.75"/>
</g>
<!-- plugginger__internal_builder_phases -->
<g id="node7" class="node">
<title>plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1463.75,-982.85 1374.25,-982.85 1374.25,-936.6 1463.75,-936.6 1463.75,-982.85"/>
<text text-anchor="middle" x="1419" y="-969.35" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1419" y="-956.6" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1419" y="-943.85" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases</text>
</g>
<!-- plugginger__internal_builder_phases&#45;&gt;plugginger_api_builder -->
<g id="edge12" class="edge">
<title>plugginger__internal_builder_phases&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1373.76,-956.56C1336.46,-951.95 1286.74,-938.46 1268,-900.04"/>
</g>
<!-- plugginger__internal_builder_phases_app_config_resolver -->
<g id="node8" class="node">
<title>plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#339999" stroke="black" cx="794" cy="-2531.05" rx="80.26" ry="41.72"/>
<text text-anchor="middle" x="794" y="-2547.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="794" y="-2534.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">_internal.</text>
<text text-anchor="middle" x="794" y="-2521.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">builder_phases.</text>
<text text-anchor="middle" x="794" y="-2508.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">app_config_resolver</text>
</g>
<!-- plugginger__internal_builder_phases_app_config_resolver&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge13" class="edge">
<title>plugginger__internal_builder_phases_app_config_resolver&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M813.3,-2490.29C830.63,-2454.32 856.56,-2399.82 878,-2351.92 884.05,-2338.42 1294,-1385.87 1294,-1371.07 1294,-1371.07 1294,-1371.07 1294,-1214.95 1294,-1135.49 1333.72,-1123.81 1370,-1053.12"/>
<path fill="none" stroke="black" d="M1370,-1051.12C1378.9,-1031.46 1390.32,-1010.2 1399.9,-993.25"/>
<polygon fill="#339999" stroke="black" points="1402.91,-995.03 1404.84,-984.61 1396.84,-991.56 1402.91,-995.03"/>
</g>
<!-- plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="node9" class="node">
<title>plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1537.88,-1081.62 1398.12,-1081.62 1398.12,-1022.62 1537.88,-1022.62 1537.88,-1081.62"/>
<text text-anchor="middle" x="1468" y="-1068.12" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1468" y="-1055.37" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1468" y="-1042.62" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases.</text>
<text text-anchor="middle" x="1468" y="-1029.87" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dependency_orchestrator</text>
</g>
<!-- plugginger__internal_builder_phases_dependency_orchestrator&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge14" class="edge">
<title>plugginger__internal_builder_phases_dependency_orchestrator&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1452.5,-1022.52C1447.43,-1013.17 1441.77,-1002.73 1436.56,-993.13"/>
<polygon fill="blue" stroke="black" points="1439.71,-991.58 1431.87,-984.46 1433.55,-994.92 1439.71,-991.58"/>
</g>
<!-- plugginger__internal_builder_phases_interface_registrar -->
<g id="node10" class="node">
<title>plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2280,-1739.76 2174,-1739.76 2174,-1680.76 2280,-1680.76 2280,-1739.76"/>
<text text-anchor="middle" x="2227" y="-1726.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2227" y="-1713.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="2227" y="-1700.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases.</text>
<text text-anchor="middle" x="2227" y="-1688.01" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">interface_registrar</text>
</g>
<!-- plugginger__internal_builder_phases_interface_registrar&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge15" class="edge">
<title>plugginger__internal_builder_phases_interface_registrar&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2217.54,-1680.33C2207.79,-1647.88 2194,-1593.51 2194,-1545.51 2194,-1545.51 2194,-1545.51 2194,-1137.82 2194,-975.05 1633.98,-1029.92 1478,-983.41 1477.01,-983.12 1476.01,-982.82 1475.01,-982.5"/>
<polygon fill="blue" stroke="black" points="1476.2,-979.21 1465.61,-979.39 1474,-985.85 1476.2,-979.21"/>
</g>
<!-- plugginger__internal_builder_phases_plugin_instantiator -->
<g id="node11" class="node">
<title>plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1423.75,-1834.76 1316.25,-1834.76 1316.25,-1775.76 1423.75,-1775.76 1423.75,-1834.76"/>
<text text-anchor="middle" x="1370" y="-1821.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1370" y="-1808.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1370" y="-1795.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">builder_phases.</text>
<text text-anchor="middle" x="1370" y="-1783.01" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin_instantiator</text>
</g>
<!-- plugginger__internal_builder_phases_plugin_instantiator&#45;&gt;plugginger__internal_builder_phases -->
<g id="edge16" class="edge">
<title>plugginger__internal_builder_phases_plugin_instantiator&#45;&gt;plugginger__internal_builder_phases</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1370,-1775.32C1370,-1739.48 1370,-1676.55 1370,-1622.64 1370,-1622.64 1370,-1622.64 1370,-1214.95 1370,-1143.02 1338.69,-1117.87 1370,-1053.12"/>
</g>
<!-- plugginger__internal_graph -->
<g id="node12" class="node">
<title>plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2fbcbc" stroke="black" cx="1690" cy="-1986.84" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1690" y="-1996.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1690" y="-1983.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="1690" y="-1970.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">graph</text>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge17" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1723.59,-1962.53C1805.52,-1903.59 2015.66,-1739.41 2108,-1545.51"/>
<path fill="none" stroke="black" d="M2108,-1543.51C2122.73,-1438.79 2150.99,-1402.19 2108,-1305.57"/>
</g>
<!-- plugginger__internal_graph&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge18" class="edge">
<title>plugginger__internal_graph&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1688.21,-1953.94C1687.68,-1936.59 1687.76,-1914.78 1690,-1895.45"/>
</g>
<!-- plugginger__internal_proxy -->
<g id="node13" class="node">
<title>plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1354,-2009.96 1284,-2009.96 1284,-1963.71 1354,-1963.71 1354,-2009.96"/>
<text text-anchor="middle" x="1319" y="-1996.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1319" y="-1983.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1319" y="-1970.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">proxy</text>
</g>
<!-- plugginger__internal_proxy&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge19" class="edge">
<title>plugginger__internal_proxy&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1286.43,-1963.29C1265.35,-1945.7 1243.91,-1920.2 1256,-1895.45"/>
</g>
<!-- plugginger__internal_runtime -->
<g id="node14" class="node">
<title>plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2078,-2443.75 2008,-2443.75 2008,-2397.5 2078,-2397.5 2078,-2443.75"/>
<text text-anchor="middle" x="2043" y="-2430.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2043" y="-2417.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="2043" y="-2404.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime</text>
</g>
<!-- plugginger__internal_runtime_facade -->
<g id="node19" class="node">
<title>plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2048.25,-2351.36 1955.75,-2351.36 1955.75,-2305.11 2048.25,-2305.11 2048.25,-2351.36"/>
<text text-anchor="middle" x="2002" y="-2337.86" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2002" y="-2325.11" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="2002" y="-2312.36" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime_facade</text>
</g>
<!-- plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge20" class="edge">
<title>plugginger__internal_runtime&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2032.87,-2397.28C2028.04,-2386.63 2022.16,-2373.69 2016.85,-2361.96"/>
<polygon fill="blue" stroke="black" points="2020.1,-2360.67 2012.79,-2353.01 2013.73,-2363.57 2020.1,-2360.67"/>
</g>
<!-- plugginger__internal_runtime_dispatcher -->
<g id="node15" class="node">
<title>plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2076,-2560.55 2006,-2560.55 2006,-2501.55 2076,-2501.55 2076,-2560.55"/>
<text text-anchor="middle" x="2041" y="-2547.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2041" y="-2534.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="2041" y="-2521.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="2041" y="-2508.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">dispatcher</text>
</g>
<!-- plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime -->
<g id="edge21" class="edge">
<title>plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2041.53,-2501.28C2041.79,-2487.12 2042.11,-2469.97 2042.38,-2455.21"/>
<polygon fill="blue" stroke="black" points="2045.87,-2455.57 2042.56,-2445.51 2038.88,-2455.44 2045.87,-2455.57"/>
</g>
<!-- plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge22" class="edge">
<title>plugginger__internal_runtime_dispatcher&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2066.05,-2501.3C2088,-2474.49 2115.08,-2436.46 2106,-2421.63"/>
<path fill="none" stroke="black" d="M2106,-2419.63C2097.42,-2405.61 2098.24,-2399.91 2087,-2387.92 2076.64,-2376.87 2063.86,-2366.72 2051.39,-2358.09"/>
<polygon fill="blue" stroke="black" points="2053.48,-2355.27 2043.22,-2352.63 2049.59,-2361.09 2053.48,-2355.27"/>
</g>
<!-- plugginger__internal_runtime_executors -->
<g id="node16" class="node">
<title>plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#28a3a3" stroke="black" cx="2143" cy="-2531.05" rx="49.5" ry="41.72"/>
<text text-anchor="middle" x="2143" y="-2547.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="2143" y="-2534.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">_internal.</text>
<text text-anchor="middle" x="2143" y="-2521.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">runtime.</text>
<text text-anchor="middle" x="2143" y="-2508.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">executors</text>
</g>
<!-- plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime -->
<g id="edge23" class="edge">
<title>plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2113,-2497.52C2099.85,-2483.26 2084.49,-2466.61 2071.51,-2452.54"/>
<polygon fill="#28a3a3" stroke="black" points="2074.16,-2450.25 2064.81,-2445.27 2069.02,-2455 2074.16,-2450.25"/>
</g>
<!-- plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge24" class="edge">
<title>plugginger__internal_runtime_executors&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2133.76,-2489.56C2127.81,-2468.34 2118.77,-2442.49 2106,-2421.63"/>
</g>
<!-- plugginger__internal_runtime_fault_policy -->
<g id="node17" class="node">
<title>plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#20b6b6" stroke="black" cx="2008" cy="-2650.49" rx="51.62" ry="41.72"/>
<text text-anchor="middle" x="2008" y="-2666.49" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2008" y="-2653.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="2008" y="-2640.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">runtime.</text>
<text text-anchor="middle" x="2008" y="-2628.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">fault_policy</text>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime -->
<g id="edge25" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1978,-2530.05C1976.31,-2500.85 1994,-2472.66 2011.34,-2452.35"/>
<polygon fill="#20b6b6" stroke="black" points="2013.87,-2454.78 2017.95,-2445 2008.66,-2450.1 2013.87,-2454.78"/>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge26" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2019.26,-2609.4C2022.7,-2597.19 2026.46,-2583.78 2029.88,-2571.61"/>
<polygon fill="#20b6b6" stroke="black" points="2033.16,-2572.87 2032.5,-2562.3 2026.42,-2570.98 2033.16,-2572.87"/>
</g>
<!-- plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge27" class="edge">
<title>plugginger__internal_runtime_fault_policy&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1993.83,-2609.87C1986.82,-2587.38 1979.53,-2558.52 1978,-2532.05"/>
<path fill="none" stroke="black" d="M1978,-2530.05C1975.07,-2479.36 1928.57,-2470.59 1942,-2421.63"/>
</g>
<!-- plugginger__internal_runtime_lifecycle -->
<g id="node18" class="node">
<title>plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1950,-2560.55 1880,-2560.55 1880,-2501.55 1950,-2501.55 1950,-2560.55"/>
<text text-anchor="middle" x="1915" y="-2547.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1915" y="-2534.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1915" y="-2521.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">runtime.</text>
<text text-anchor="middle" x="1915" y="-2508.8" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">lifecycle</text>
</g>
<!-- plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime -->
<g id="edge28" class="edge">
<title>plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1948.97,-2501.28C1967.17,-2485.86 1989.56,-2466.89 2007.93,-2451.33"/>
<polygon fill="blue" stroke="black" points="2010.07,-2454.11 2015.44,-2444.97 2005.54,-2448.77 2010.07,-2454.11"/>
</g>
<!-- plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge29" class="edge">
<title>plugginger__internal_runtime_lifecycle&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1921.74,-2501.42C1926.99,-2479.5 1934.61,-2448.56 1942,-2421.63"/>
<path fill="none" stroke="black" d="M1942,-2419.63C1947.97,-2397.86 1961.49,-2376.67 1974.19,-2360.28"/>
<polygon fill="blue" stroke="black" points="1976.61,-2362.86 1980.16,-2352.88 1971.16,-2358.47 1976.61,-2362.86"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge30" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2144,-2077.67C2143.17,-2069.9 2066.16,-1813.07 2070,-1806.26"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app -->
<g id="edge31" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1955.46,-2326.2C1824.78,-2322.81 1456.69,-2309.76 1344,-2268.55 1298.19,-2251.79 1253.46,-2217.45 1224.73,-2192.37"/>
<polygon fill="blue" stroke="black" points="1227.27,-2189.95 1217.47,-2185.92 1222.62,-2195.18 1227.27,-2189.95"/>
</g>
<!-- plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder -->
<g id="edge32" class="edge">
<title>plugginger__internal_runtime_facade&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2028.49,-2304.88C2073.65,-2263.94 2159.26,-2172.57 2144,-2079.67"/>
<path fill="none" stroke="black" d="M2144,-2077.67C2138.25,-2047.34 2118.63,-2047.7 2106,-2019.54 2060.73,-1918.63 1999.54,-1870.87 2056,-1775.76 2073.06,-1747.03 2102.76,-1767.08 2122,-1739.76 2217.76,-1603.78 2131.03,-1529.01 2180,-1370.07 2213.34,-1261.87 2262.1,-1249.8 2289,-1139.82"/>
<path fill="none" stroke="black" d="M2289,-1137.82C2285.95,-1024.07 2170.19,-1054.67 2062,-1019.41 2051.4,-1015.96 1273.16,-909.93 1268,-900.04"/>
</g>
<!-- plugginger__internal_validation -->
<g id="node20" class="node">
<title>plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2128,-1239.07 2058,-1239.07 2058,-1192.82 2128,-1192.82 2128,-1239.07"/>
<text text-anchor="middle" x="2093" y="-1225.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2093" y="-1212.82" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="2093" y="-1200.07" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">validation</text>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge33" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2077.94,-1192.43C2066.28,-1176.27 2048.97,-1154.79 2030,-1139.82"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_builder -->
<g id="edge34" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2057.78,-1214.95C1888.27,-1214.39 1157.07,-1204.92 971,-1084.82 957.2,-1075.91 952.53,-1069.54 952,-1053.12"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_events -->
<g id="edge35" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2128.33,-1213.47C2237.6,-1208.64 2579.67,-1191.62 2861,-1156.82 3069.52,-1131.03 3315.84,-1083.05 3416.57,-1062.67"/>
<polygon fill="blue" stroke="black" points="3416.97,-1066.16 3426.07,-1060.74 3415.57,-1059.3 3416.97,-1066.16"/>
</g>
<!-- plugginger__internal_validation&#45;&gt;plugginger_api_service -->
<g id="edge36" class="edge">
<title>plugginger__internal_validation&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2128.47,-1214.9C2318.51,-1214.43 3217.7,-1209.44 3490,-1156.82 3580.59,-1139.32 3680.87,-1098.93 3737.49,-1074.04"/>
<polygon fill="blue" stroke="black" points="3738.77,-1077.3 3746.49,-1070.05 3735.93,-1070.9 3738.77,-1077.3"/>
</g>
<!-- plugginger__internal_validation_dependency_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge37" class="edge">
<title>plugginger__internal_validation_dependency_validation&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1705.19,-1775.37C1702.74,-1756.36 1702.19,-1731.37 1711,-1711.26"/>
</g>
<!-- plugginger__internal_validation_manifest_loader -->
<g id="node22" class="node">
<title>plugginger__internal_validation_manifest_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1189.75,-1334.07 1094.25,-1334.07 1094.25,-1275.07 1189.75,-1275.07 1189.75,-1334.07"/>
<text text-anchor="middle" x="1142" y="-1320.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1142" y="-1307.82" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">_internal.</text>
<text text-anchor="middle" x="1142" y="-1295.07" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">validation.</text>
<text text-anchor="middle" x="1142" y="-1282.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">manifest_loader</text>
</g>
<!-- plugginger__internal_validation_manifest_loader&#45;&gt;plugginger__internal_validation -->
<g id="edge38" class="edge">
<title>plugginger__internal_validation_manifest_loader&#45;&gt;plugginger__internal_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1190.14,-1299.19C1353.51,-1284.31 1885.52,-1235.85 2046.46,-1221.19"/>
<polygon fill="blue" stroke="black" points="2046.69,-1224.68 2056.33,-1220.29 2046.05,-1217.71 2046.69,-1224.68"/>
</g>
<!-- plugginger__internal_validation_manifest_loader&#45;&gt;plugginger_api_builder -->
<g id="edge39" class="edge">
<title>plugginger__internal_validation_manifest_loader&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1146.85,-1274.63C1148.56,-1257.38 1148.67,-1235.32 1142,-1216.95"/>
</g>
<!-- plugginger__internal_validation_name_validation -->
<g id="node23" class="node">
<title>plugginger__internal_validation_name_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#46a4a4" stroke="black" cx="2029" cy="-2871.33" rx="69.12" ry="41.72"/>
<text text-anchor="middle" x="2029" y="-2887.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2029" y="-2874.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="2029" y="-2861.83" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">validation.</text>
<text text-anchor="middle" x="2029" y="-2849.08" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">name_validation</text>
</g>
<!-- plugginger__internal_validation_plugin_validation -->
<g id="node24" class="node">
<title>plugginger__internal_validation_plugin_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2fbcbc" stroke="black" cx="4137" cy="-1448.79" rx="70.71" ry="41.72"/>
<text text-anchor="middle" x="4137" y="-1464.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4137" y="-1452.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">_internal.</text>
<text text-anchor="middle" x="4137" y="-1439.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">validation.</text>
<text text-anchor="middle" x="4137" y="-1426.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugin_validation</text>
</g>
<!-- plugginger__internal_validation_plugin_validation&#45;&gt;plugginger_api_events -->
<g id="edge40" class="edge">
<title>plugginger__internal_validation_plugin_validation&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4075.94,-1427.46C4039.68,-1414.06 3993.59,-1394.64 3956,-1371.07 3867.91,-1315.83 3861.77,-1281.17 3780,-1216.95"/>
<path fill="none" stroke="black" d="M3780,-1214.95C3666.84,-1146.19 3627.36,-1149.82 3512,-1084.82 3510.11,-1083.76 3508.2,-1082.65 3506.28,-1081.52"/>
<polygon fill="#2fbcbc" stroke="black" points="3508.33,-1078.67 3497.97,-1076.45 3504.69,-1084.64 3508.33,-1078.67"/>
</g>
<!-- plugginger__internal_validation_plugin_validation&#45;&gt;plugginger_api_service -->
<g id="edge41" class="edge">
<title>plugginger__internal_validation_plugin_validation&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4105.3,-1411.13C4083.15,-1380.83 4061.25,-1337.73 4084,-1305.57"/>
</g>
<!-- plugginger_ai -->
<g id="node25" class="node">
<title>plugginger_ai</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="5465.5,-36 5386.5,-36 5386.5,0 5465.5,0 5465.5,-36"/>
<text text-anchor="middle" x="5426" y="-14.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.ai</text>
</g>
<!-- plugginger_ai_event_matcher -->
<g id="node26" class="node">
<title>plugginger_ai_event_matcher</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#409696" stroke="black" cx="5065" cy="-288.36" rx="63.29" ry="32.7"/>
<text text-anchor="middle" x="5065" y="-297.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="5065" y="-285.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">ai.</text>
<text text-anchor="middle" x="5065" y="-272.49" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">event_matcher</text>
</g>
<!-- plugginger_ai_grammars -->
<g id="node27" class="node">
<title>plugginger_ai_grammars</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="5524,-311.49 5454,-311.49 5454,-265.24 5524,-265.24 5524,-311.49"/>
<text text-anchor="middle" x="5489" y="-297.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="5489" y="-285.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">ai.</text>
<text text-anchor="middle" x="5489" y="-272.49" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">grammars</text>
</g>
<!-- plugginger_ai_json_validator -->
<g id="node28" class="node">
<title>plugginger_ai_json_validator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="5468.5,-219.66 5383.5,-219.66 5383.5,-173.41 5468.5,-173.41 5468.5,-219.66"/>
<text text-anchor="middle" x="5426" y="-206.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="5426" y="-193.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">ai.</text>
<text text-anchor="middle" x="5426" y="-180.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">json_validator</text>
</g>
<!-- plugginger_ai_grammars&#45;&gt;plugginger_ai_json_validator -->
<g id="edge42" class="edge">
<title>plugginger_ai_grammars&#45;&gt;plugginger_ai_json_validator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5473.43,-265.16C5465.84,-254.33 5456.57,-241.12 5448.25,-229.26"/>
<polygon fill="blue" stroke="black" points="5451.31,-227.52 5442.7,-221.34 5445.58,-231.54 5451.31,-227.52"/>
</g>
<!-- plugginger_ai_plugin_generator -->
<g id="node30" class="node">
<title>plugginger_ai_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="5475.25,-127.83 5376.75,-127.83 5376.75,-81.58 5475.25,-81.58 5475.25,-127.83"/>
<text text-anchor="middle" x="5426" y="-114.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="5426" y="-101.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">ai.</text>
<text text-anchor="middle" x="5426" y="-88.83" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin_generator</text>
</g>
<!-- plugginger_ai_grammars&#45;&gt;plugginger_ai_plugin_generator -->
<g id="edge43" class="edge">
<title>plugginger_ai_grammars&#45;&gt;plugginger_ai_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5490.4,-265.1C5491.09,-241.5 5489.88,-203.55 5478,-173.41 5472.82,-160.27 5464.29,-147.53 5455.62,-136.71"/>
<polygon fill="blue" stroke="black" points="5458.47,-134.66 5449.35,-129.27 5453.11,-139.17 5458.47,-134.66"/>
</g>
<!-- plugginger_ai_json_validator&#45;&gt;plugginger_ai_plugin_generator -->
<g id="edge44" class="edge">
<title>plugginger_ai_json_validator&#45;&gt;plugginger_ai_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5426,-173.33C5426,-163.15 5426,-150.86 5426,-139.56"/>
<polygon fill="blue" stroke="black" points="5429.5,-139.79 5426,-129.79 5422.5,-139.79 5429.5,-139.79"/>
</g>
<!-- plugginger_ai_llm_provider -->
<g id="node29" class="node">
<title>plugginger_ai_llm_provider</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="5326.75,-219.66 5249.25,-219.66 5249.25,-173.41 5326.75,-173.41 5326.75,-219.66"/>
<text text-anchor="middle" x="5288" y="-206.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="5288" y="-193.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">ai.</text>
<text text-anchor="middle" x="5288" y="-180.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">llm_provider</text>
</g>
<!-- plugginger_ai_llm_provider&#45;&gt;plugginger_ai -->
<g id="edge45" class="edge">
<title>plugginger_ai_llm_provider&#45;&gt;plugginger_ai</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5300.29,-172.93C5314.78,-147.21 5340.43,-104.57 5368,-72 5376.37,-62.12 5386.51,-52.27 5395.95,-43.8"/>
<polygon fill="blue" stroke="black" points="5398,-46.66 5403.22,-37.44 5393.39,-41.39 5398,-46.66"/>
</g>
<!-- plugginger_ai_llm_provider&#45;&gt;plugginger_ai_plugin_generator -->
<g id="edge46" class="edge">
<title>plugginger_ai_llm_provider&#45;&gt;plugginger_ai_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5322.47,-173.1C5340.5,-161.36 5362.77,-146.87 5382.02,-134.33"/>
<polygon fill="blue" stroke="black" points="5383.75,-137.38 5390.23,-128.99 5379.94,-131.51 5383.75,-137.38"/>
</g>
<!-- plugginger_ai_plugin_generator&#45;&gt;plugginger_ai -->
<g id="edge47" class="edge">
<title>plugginger_ai_plugin_generator&#45;&gt;plugginger_ai</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5426,-81.45C5426,-71.08 5426,-58.62 5426,-47.57"/>
<polygon fill="blue" stroke="black" points="5429.5,-47.81 5426,-37.81 5422.5,-47.81 5429.5,-47.81"/>
</g>
<!-- plugginger_ai_service_matcher -->
<g id="node31" class="node">
<title>plugginger_ai_service_matcher</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#409696" stroke="black" cx="5215" cy="-288.36" rx="68.59" ry="32.7"/>
<text text-anchor="middle" x="5215" y="-297.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="5215" y="-285.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">ai.</text>
<text text-anchor="middle" x="5215" y="-272.49" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">service_matcher</text>
</g>
<!-- plugginger_ai_types -->
<g id="node32" class="node">
<title>plugginger_ai_types</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#06f9f9" stroke="black" cx="5465" cy="-389.77" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="5465" y="-399.39" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="5465" y="-386.64" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">ai.</text>
<text text-anchor="middle" x="5465" y="-373.89" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">types</text>
</g>
<!-- plugginger_ai_types&#45;&gt;plugginger_ai -->
<g id="edge48" class="edge">
<title>plugginger_ai_types&#45;&gt;plugginger_ai</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5590,-195.53C5572.33,-127.35 5506.95,-72.48 5464.27,-42.92"/>
<polygon fill="#06f9f9" stroke="black" points="5466.24,-40.03 5456,-37.32 5462.32,-45.83 5466.24,-40.03"/>
</g>
<!-- plugginger_ai_types&#45;&gt;plugginger_ai_event_matcher -->
<g id="edge49" class="edge">
<title>plugginger_ai_types&#45;&gt;plugginger_ai_event_matcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5418.44,-377.71C5391.49,-371.42 5356.89,-363.53 5326,-357.06 5242.3,-339.55 5218.65,-346.46 5137,-321.06 5131.89,-319.48 5126.66,-317.63 5121.46,-315.64"/>
<polygon fill="#06f9f9" stroke="black" points="5122.88,-312.44 5112.29,-311.97 5120.27,-318.94 5122.88,-312.44"/>
</g>
<!-- plugginger_ai_types&#45;&gt;plugginger_ai_grammars -->
<g id="edge50" class="edge">
<title>plugginger_ai_types&#45;&gt;plugginger_ai_grammars</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5472.66,-357.04C5475.29,-346.16 5478.23,-333.95 5480.9,-322.93"/>
<polygon fill="#06f9f9" stroke="black" points="5484.24,-323.99 5483.18,-313.45 5477.43,-322.35 5484.24,-323.99"/>
</g>
<!-- plugginger_ai_types&#45;&gt;plugginger_ai_json_validator -->
<g id="edge51" class="edge">
<title>plugginger_ai_types&#45;&gt;plugginger_ai_json_validator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5426,-287.36C5416.69,-270.54 5416.34,-249.09 5418.49,-231.37"/>
<polygon fill="#06f9f9" stroke="black" points="5421.93,-231.99 5420.01,-221.57 5415.02,-230.92 5421.93,-231.99"/>
</g>
<!-- plugginger_ai_types&#45;&gt;plugginger_ai_llm_provider -->
<g id="edge52" class="edge">
<title>plugginger_ai_types&#45;&gt;plugginger_ai_llm_provider</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5455.07,-357.36C5448.16,-337.31 5438.04,-311.09 5426,-289.36"/>
<path fill="none" stroke="black" d="M5426,-287.36C5415.87,-269.07 5372.28,-242.36 5336.76,-222.82"/>
<polygon fill="#06f9f9" stroke="black" points="5338.78,-219.94 5328.32,-218.24 5335.44,-226.09 5338.78,-219.94"/>
</g>
<!-- plugginger_ai_types&#45;&gt;plugginger_ai_plugin_generator -->
<g id="edge53" class="edge">
<title>plugginger_ai_types&#45;&gt;plugginger_ai_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5498.61,-365.56C5543.71,-331.31 5615.38,-264.05 5590,-197.53"/>
<path fill="none" stroke="black" d="M5590,-195.53C5578.34,-150.53 5528.4,-127.77 5486.67,-116.44"/>
<polygon fill="#06f9f9" stroke="black" points="5487.73,-113.1 5477.17,-114.05 5486.01,-119.89 5487.73,-113.1"/>
</g>
<!-- plugginger_ai_types&#45;&gt;plugginger_ai_service_matcher -->
<g id="edge54" class="edge">
<title>plugginger_ai_types&#45;&gt;plugginger_ai_service_matcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5422.87,-372.02C5383.07,-356.19 5322.85,-332.24 5277.46,-314.2"/>
<polygon fill="#06f9f9" stroke="black" points="5278.83,-310.98 5268.25,-310.53 5276.25,-317.48 5278.83,-310.98"/>
</g>
<!-- plugginger_ai_wiring_analyzer -->
<g id="node33" class="node">
<title>plugginger_ai_wiring_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#378181" stroke="black" cx="5183" cy="-104.7" rx="65.94" ry="32.7"/>
<text text-anchor="middle" x="5183" y="-114.33" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="5183" y="-101.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">ai.</text>
<text text-anchor="middle" x="5183" y="-88.83" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">wiring_analyzer</text>
</g>
<!-- plugginger_ai_types&#45;&gt;plugginger_ai_wiring_analyzer -->
<g id="edge55" class="edge">
<title>plugginger_ai_types&#45;&gt;plugginger_ai_wiring_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5435.41,-363.31C5402.03,-335.22 5345.53,-289.5 5293,-255.66 5246.52,-225.71 5144.57,-237.29 5183,-197.53"/>
</g>
<!-- plugginger_api -->
<g id="node34" class="node">
<title>plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2946.5,-2889.33 2861.5,-2889.33 2861.5,-2853.33 2946.5,-2853.33 2946.5,-2889.33"/>
<text text-anchor="middle" x="2904" y="-2868.21" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.api</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge56" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2714,-2419.63C2684.13,-2381.67 2666.27,-2382.64 2629,-2351.92 2453.24,-2207.09 2373.8,-2203.11 2239,-2019.54 2150.88,-1899.55 2068.26,-1854.73 2108,-1711.26"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge57" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2714,-2419.63C2648.82,-2334.22 2638.2,-2308.77 2576,-2221.17 2464.64,-2064.33 2438.85,-2023.55 2322,-1870.76 2299.63,-1841.51 2285.49,-1839.67 2270,-1806.26"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge58" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2752,-2530.05C2673.08,-2429.51 2620.12,-2436.74 2502,-2387.92 2372.12,-2334.25 2335.65,-2327.46 2197,-2304.55 2066.97,-2283.05 1727.74,-2316.58 1605,-2268.55 1577.74,-2257.88 1517.85,-2210.4 1503,-2185.17 1478.84,-2144.11 1484,-2127.31 1484,-2079.67 1484,-2079.67 1484,-2079.67 1484,-1985.84 1484,-1929.67 1443.75,-1876.85 1411.09,-1843.23"/>
<polygon fill="blue" stroke="black" points="1413.59,-1840.78 1404.04,-1836.18 1408.63,-1845.72 1413.59,-1840.78"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_proxy -->
<g id="edge59" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2892.84,-2853.08C2883.22,-2837.8 2869.28,-2814.71 2859,-2793.61 2831.5,-2737.16 2793.22,-2579.43 2752,-2532.05"/>
<path fill="none" stroke="black" d="M2752,-2530.05C2694.15,-2462.26 2461.48,-2416.29 2377,-2387.92 2252.29,-2346.05 2222.49,-2327.74 2093,-2304.55 1864.53,-2263.62 1791.04,-2345.32 1572,-2268.55 1502.38,-2244.15 1290.81,-2152.98 1299,-2079.67"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge60" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2861.33,-2856.06C2771.71,-2826.03 2557.34,-2754.01 2378,-2692.21 2272.03,-2655.69 2248.04,-2638.61 2140,-2608.77 2063.04,-2587.51 2035.98,-2607.32 1964,-2572.77 1960.86,-2571.26 1957.75,-2569.51 1954.71,-2567.59"/>
<polygon fill="blue" stroke="black" points="1956.75,-2564.75 1946.54,-2561.9 1952.74,-2570.49 1956.75,-2564.75"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge61" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2696,-2759.91C2666.64,-2738.56 2654.98,-2638.4 2634,-2608.77 2587.9,-2543.65 2570.46,-2526.75 2500,-2489.33 2442.5,-2458.79 2416.65,-2481.61 2358,-2453.33 2314.74,-2432.47 2316.04,-2407.06 2272,-2387.92 2185.15,-2350.18 2153.37,-2376.78 2062,-2351.92 2061.17,-2351.7 2060.34,-2351.47 2059.51,-2351.23"/>
<polygon fill="blue" stroke="black" points="2060.59,-2347.9 2050,-2348.27 2058.51,-2354.58 2060.59,-2347.9"/>
</g>
<!-- plugginger_api&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge62" class="edge">
<title>plugginger_api&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2752,-2530.05C2720.47,-2489.88 2746.03,-2461.39 2714,-2421.63"/>
<path fill="none" stroke="black" d="M2714,-2419.63C2664.02,-2357.57 2620.41,-2385.18 2548,-2351.92 2281.51,-2229.52 2219.88,-2185.36 1978,-2019.54 1894.51,-1962.3 1803.7,-1886.62 1752.42,-1842.5"/>
<polygon fill="blue" stroke="black" points="1754.93,-1840.04 1745.07,-1836.16 1750.36,-1845.34 1754.93,-1840.04"/>
</g>
<!-- plugginger_cli_cmd_core_freeze -->
<g id="node44" class="node">
<title>plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1152.62,-504.72 1053.38,-504.72 1053.38,-458.47 1152.62,-458.47 1152.62,-504.72"/>
<text text-anchor="middle" x="1103" y="-491.22" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1103" y="-478.47" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="1103" y="-465.72" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_core_freeze</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge63" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2904,-2649.49C2839.4,-2073.81 2650,-1950.36 2650,-1371.07 2650,-1371.07 2650,-1371.07 2650,-1051.12 2650,-963.91 2610.3,-921.38 2526,-899.04 2465.29,-882.95 1444.38,-903.09 1396,-863.04 1339.46,-816.24 1375.96,-592.55 1324,-540.72 1273.94,-490.78 1235.8,-522.67 1163.71,-505.1"/>
<polygon fill="blue" stroke="black" points="1164.86,-501.79 1154.29,-502.54 1163.02,-508.54 1164.86,-501.79"/>
</g>
<!-- plugginger_cli_cmd_inspect -->
<g id="node45" class="node">
<title>plugginger_cli_cmd_inspect</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="843.75,-504.72 766.25,-504.72 766.25,-458.47 843.75,-458.47 843.75,-504.72"/>
<text text-anchor="middle" x="805" y="-491.22" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="805" y="-478.47" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="805" y="-465.72" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_inspect</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_inspect -->
<g id="edge64" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_inspect</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2861.36,-2862.76C2762.47,-2845.17 2523.18,-2802.36 2487,-2793.61 2334.96,-2756.87 2283.65,-2773.46 2150,-2692.21 2105.84,-2665.36 2114.66,-2632.99 2069,-2608.77 2063.54,-2605.87 1197.95,-2455.01 1192,-2453.33 879.8,-2364.98 738.12,-2418.83 513,-2185.17 350.1,-2016.09 341.81,-1915.12 356,-1680.76 360.27,-1610.28 363.21,-1590.05 394,-1526.51 429.18,-1453.92 440.69,-1436.8 486,-1370.07 553.47,-1270.71 597.1,-1263.65 652,-1156.82 737.79,-989.87 830.22,-907.59 739,-743.54 711.28,-693.7 628.18,-732.83 630,-675.83"/>
</g>
<!-- plugginger_cli_cmd_new -->
<g id="node46" class="node">
<title>plugginger_cli_cmd_new</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2bacac" stroke="black" cx="1687" cy="-389.77" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1687" y="-399.39" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1687" y="-386.64" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">cli.</text>
<text text-anchor="middle" x="1687" y="-373.89" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">cmd_new</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_new -->
<g id="edge65" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_new</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2946.9,-2864.34C3107.21,-2841.92 3662.05,-2764.22 3666,-2761.91"/>
<path fill="none" stroke="black" d="M3666,-2759.91C3735.94,-2718.98 3719.26,-2673.51 3768,-2608.77 4132.78,-2124.27 4301.51,-2063.02 4644,-1562.51 4669.05,-1525.9 5017.96,-906 5029,-863.04 5053.44,-767.95 5096.73,-713.21 5029,-642.13 4902.23,-509.09 1803.13,-648.93 1725,-482.6"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate -->
<g id="node49" class="node">
<title>plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1287.62,-504.72 1170.38,-504.72 1170.38,-458.47 1287.62,-458.47 1287.62,-504.72"/>
<text text-anchor="middle" x="1229" y="-491.22" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="1229" y="-478.47" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="1229" y="-465.72" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_stubs_generate</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge66" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2943.09,-2852.96C2976.45,-2835.53 3021.41,-2805.06 3037,-2761.91"/>
<path fill="none" stroke="black" d="M3037,-2759.91C3070.31,-2667.74 2942,-2427.24 2942,-2329.23 2942,-2329.23 2942,-2329.23 2942,-1985.84 2942,-1934 2936.13,-1920.91 2923,-1870.76 2900.25,-1783.85 2885.45,-1764.91 2854,-1680.76 2831.45,-1620.44 2802,-1609.91 2802,-1545.51 2802,-1545.51 2802,-1545.51 2802,-1447.79 2802,-1415.08 2757.04,-1337.54 2764,-1305.57"/>
</g>
<!-- plugginger_cli_utils -->
<g id="node50" class="node">
<title>plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#0bdfdf" stroke="black" cx="1026" cy="-573.43" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1026" y="-583.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1026" y="-570.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">cli.</text>
<text text-anchor="middle" x="1026" y="-557.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">utils</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_cli_utils -->
<g id="edge67" class="edge">
<title>plugginger_api&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2861.11,-2853.19C2827.09,-2838.93 2778.81,-2817.22 2739,-2793.61 2718.58,-2781.5 2715.34,-2775.68 2696,-2761.91"/>
<path fill="none" stroke="black" d="M2696,-2759.91C2687.93,-2754.16 2096.35,-2391.21 2087,-2387.92 1768.81,-2276.16 840.28,-2403.21 583,-2185.17 474.89,-2093.55 525.53,-2012.4 530,-1870.76 536.58,-1662.24 528.57,-1601.96 603,-1407.07 612.76,-1381.51 840.15,-1007.01 854,-983.41 885.33,-930.04 898.02,-919.21 924,-863.04 963.22,-778.25 996.67,-673.83 1013.78,-616.71"/>
<polygon fill="blue" stroke="black" points="1017.06,-617.97 1016.55,-607.38 1010.35,-615.97 1017.06,-617.97"/>
</g>
<!-- plugginger_discovery_discovery -->
<g id="node62" class="node">
<title>plugginger_discovery_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="334,-596.55 264,-596.55 264,-550.3 334,-550.3 334,-596.55"/>
<text text-anchor="middle" x="299" y="-583.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="299" y="-570.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">discovery.</text>
<text text-anchor="middle" x="299" y="-557.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">discovery</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_discovery_discovery -->
<g id="edge68" class="edge">
<title>plugginger_api&#45;&gt;plugginger_discovery_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2861.03,-2861.28C2814.62,-2851.86 2738.4,-2837.41 2672,-2829.61 2407.56,-2798.55 2337.74,-2830.15 2074,-2793.61 1864.91,-2764.65 1814.13,-2745.97 1610,-2692.21 1314.89,-2614.47 1251.65,-2556.71 954,-2489.33 846.23,-2464.93 811.15,-2492.96 708,-2453.33 632.19,-2424.2 614.08,-2408.68 556,-2351.92 432.3,-2231.03 81.29,-1881.1 114,-1711.26"/>
</g>
<!-- plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="node78" class="node">
<title>plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2795.12,-839.16 2674.88,-839.16 2674.88,-767.41 2795.12,-767.41 2795.12,-839.16"/>
<text text-anchor="middle" x="2735" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2735" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="2735" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">core.</text>
<text text-anchor="middle" x="2735" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">json_validator.</text>
<text text-anchor="middle" x="2735" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">json_validator_plugin</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="edge69" class="edge">
<title>plugginger_api&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3208,-2649.49C3377.36,-2104.69 3324,-1941.59 3324,-1371.07 3324,-1371.07 3324,-1371.07 3324,-1137.82 3324,-1095 3403.82,-1003.38 3400,-960.73"/>
<path fill="none" stroke="black" d="M3400,-958.73C3385.76,-867.06 3291.09,-916.6 3200,-899.04 3026.9,-865.67 2967.74,-930.8 2805,-863.04 2795.32,-859.01 2785.97,-853.14 2777.43,-846.66"/>
<polygon fill="blue" stroke="black" points="2779.65,-843.95 2769.68,-840.39 2775.25,-849.4 2779.65,-843.95"/>
</g>
<!-- plugginger_plugins_core_llm_provider_llm_provider_plugin -->
<g id="node84" class="node">
<title>plugginger_plugins_core_llm_provider_llm_provider_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2127.38,-839.16 2014.62,-839.16 2014.62,-767.41 2127.38,-767.41 2127.38,-839.16"/>
<text text-anchor="middle" x="2071" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2071" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="2071" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">core.</text>
<text text-anchor="middle" x="2071" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">llm_provider.</text>
<text text-anchor="middle" x="2071" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">llm_provider_plugin</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin -->
<g id="edge70" class="edge">
<title>plugginger_api&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3132,-2759.91C3142.33,-2748.49 3137.98,-2741.36 3146,-2728.21 3168.83,-2690.78 3195.24,-2693.43 3208,-2651.49"/>
<path fill="none" stroke="black" d="M3208,-2649.49C3230.5,-2575.54 3204.97,-2029.07 3186,-1954.14 3145.56,-1794.4 3020,-1787.41 3020,-1622.64 3020,-1622.64 3020,-1622.64 3020,-1543.51 3020,-1385.23 3030.67,-1342.54 3082,-1192.82 3093.74,-1158.59 3100.47,-1151.28 3120,-1120.82 3140.48,-1088.88 3160.9,-1089.4 3172,-1053.12"/>
<path fill="none" stroke="black" d="M3172,-1051.12C3183.7,-985 3229.33,-934.99 3172,-900.04"/>
<path fill="none" stroke="black" d="M3172,-899.04C3069.91,-845.71 2242.76,-906.28 2136,-863.04 2126.68,-859.26 2117.84,-853.52 2109.85,-847.07"/>
<polygon fill="blue" stroke="black" points="2112.2,-844.48 2102.35,-840.56 2107.61,-849.76 2112.2,-844.48"/>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="node93" class="node">
<title>plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="3693.25,-839.16 3564.75,-839.16 3564.75,-767.41 3693.25,-767.41 3693.25,-839.16"/>
<text text-anchor="middle" x="3629" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="3629" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="3629" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">core.</text>
<text text-anchor="middle" x="3629" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">wiring_analyzer.</text>
<text text-anchor="middle" x="3629" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">wiring_analyzer_plugin</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="edge71" class="edge">
<title>plugginger_api&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2946.79,-2867.39C3013.54,-2861.36 3145.04,-2843.64 3244,-2793.61 3305.41,-2762.57 3313.13,-2741.64 3361,-2692.21 3444.58,-2605.91 3666,-2365.99 3666,-2245.86 3666,-2245.86 3666,-2245.86 3666,-2160.48 3666,-2066.17 4008.23,-1487.79 4057,-1407.07 4104.06,-1329.19 4143.7,-1324.87 4174,-1239.07 4186.58,-1203.46 4234.95,-907.83 4198,-900.04"/>
<path fill="none" stroke="black" d="M4198,-899.04C3981.59,-854.13 3907.66,-944 3702,-863.04 3691.89,-859.06 3682.08,-853.15 3673.11,-846.6"/>
<polygon fill="blue" stroke="black" points="3675.33,-843.89 3665.29,-840.52 3671.04,-849.42 3675.33,-843.89"/>
</g>
<!-- plugginger_plugins_core_loader -->
<g id="node94" class="node">
<title>plugginger_plugins_core_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#409696" stroke="black" cx="1263" cy="-573.43" rx="51.62" ry="32.7"/>
<text text-anchor="middle" x="1263" y="-583.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="1263" y="-570.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugins.</text>
<text text-anchor="middle" x="1263" y="-557.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">core_loader</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_plugins_core_loader -->
<g id="edge72" class="edge">
<title>plugginger_api&#45;&gt;plugginger_plugins_core_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2905.8,-2852.89C2909.26,-2815.63 2915.51,-2725.64 2904,-2651.49"/>
<path fill="none" stroke="black" d="M2904,-2649.49C2887.12,-2557.85 2862.79,-2540.55 2830,-2453.33 2813.06,-2408.28 2806.08,-2397.95 2792,-2351.92 2661.32,-1924.9 2574,-1817.64 2574,-1371.07 2574,-1371.07 2574,-1371.07 2574,-1214.95 2574,-1071.86 2647.99,-943.51 2512,-899.04 2391.02,-859.47 1472.27,-934.59 1367,-863.04 1292.02,-812.08 1302.67,-764.94 1286,-675.83"/>
</g>
<!-- plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin -->
<g id="node96" class="node">
<title>plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="4451.38,-839.16 4326.62,-839.16 4326.62,-767.41 4451.38,-767.41 4451.38,-839.16"/>
<text text-anchor="middle" x="4389" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="4389" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="4389" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">internal.</text>
<text text-anchor="middle" x="4389" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">ai_orchestrator.</text>
<text text-anchor="middle" x="4389" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">ai_orchestrator_plugin</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin -->
<g id="edge73" class="edge">
<title>plugginger_api&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2946.75,-2864.68C3035.61,-2852.52 3237.21,-2822.58 3300,-2793.61 3321.16,-2783.85 3613.3,-2550.14 3628,-2532.05"/>
<path fill="none" stroke="black" d="M3628,-2530.05C3674.1,-2478.82 3658.26,-2449.09 3690,-2387.92 3927.23,-1930.8 3962.09,-1789.53 4307,-1407.07 4366.47,-1341.12 4388.27,-1331.3 4457,-1275.07 4533.9,-1212.16 4591.17,-1228.99 4635,-1139.82"/>
<path fill="none" stroke="black" d="M4635,-1137.82C4661.24,-1030.7 4787.22,-974.66 4706,-900.04"/>
<path fill="none" stroke="black" d="M4706,-899.04C4608.97,-846.16 4560.97,-907.92 4460,-863.04 4450.55,-858.84 4441.35,-853.01 4432.87,-846.66"/>
<polygon fill="blue" stroke="black" points="4435.17,-844.01 4425.16,-840.54 4430.82,-849.49 4435.17,-844.01"/>
</g>
<!-- plugginger_plugins_internal_plugin_generator_plugin_generator_plugin -->
<g id="node101" class="node">
<title>plugginger_plugins_internal_plugin_generator_plugin_generator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="4870.88,-839.16 4737.12,-839.16 4737.12,-767.41 4870.88,-767.41 4870.88,-839.16"/>
<text text-anchor="middle" x="4804" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="4804" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="4804" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">internal.</text>
<text text-anchor="middle" x="4804" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin_generator.</text>
<text text-anchor="middle" x="4804" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin_generator_plugin</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin -->
<g id="edge74" class="edge">
<title>plugginger_api&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3628,-2530.05C3845.02,-2311.01 4239.36,-1638.6 4443,-1407.07 4499.48,-1342.86 4516.01,-1328.23 4583,-1275.07 4604.3,-1258.17 4780.83,-1166.31 4787,-1139.82"/>
<path fill="none" stroke="black" d="M4787,-1137.82C4802.84,-1086.51 4798.71,-1071.68 4811,-1019.41 4823.57,-965.94 4853.96,-953.86 4843,-900.04"/>
<path fill="none" stroke="black" d="M4843,-899.04C4836.48,-882.97 4829.26,-865.4 4822.83,-849.79"/>
<polygon fill="blue" stroke="black" points="4826.15,-848.66 4819.1,-840.75 4819.68,-851.33 4826.15,-848.66"/>
</g>
<!-- plugginger_plugins_internal_loader -->
<g id="node104" class="node">
<title>plugginger_plugins_internal_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#35a1a1" stroke="black" cx="1816" cy="-573.43" rx="63.29" ry="32.7"/>
<text text-anchor="middle" x="1816" y="-583.05" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1816" y="-570.3" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="1816" y="-557.55" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">internal_loader</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_plugins_internal_loader -->
<g id="edge75" class="edge">
<title>plugginger_api&#45;&gt;plugginger_plugins_internal_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3628,-2530.05C3670.36,-2488.64 3693.59,-2493.64 3737,-2453.33 3771.14,-2421.63 4774.31,-1195.01 4801,-1156.82 4877.19,-1047.8 4866.98,-1000.48 4953,-899.04 4969.12,-880.03 4982.07,-883.7 4996,-863.04 5011.34,-840.28 5007.25,-830.61 5015,-804.29"/>
<path fill="none" stroke="black" d="M5015,-802.29C5023.76,-746.77 5059.49,-710.18 5015,-675.83"/>
<path fill="none" stroke="black" d="M5015,-673.83C4877.68,-567.82 2062.81,-641.64 1893,-606.13 1886.13,-604.69 1879.09,-602.66 1872.2,-600.31"/>
<polygon fill="blue" stroke="black" points="1873.55,-597.08 1862.96,-596.92 1871.14,-603.65 1873.55,-597.08"/>
</g>
<!-- plugginger_schemas_generator -->
<g id="node106" class="node">
<title>plugginger_schemas_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2914,-1917.57 2844,-1917.57 2844,-1871.32 2914,-1871.32 2914,-1917.57"/>
<text text-anchor="middle" x="2879" y="-1904.07" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2879" y="-1891.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">schemas.</text>
<text text-anchor="middle" x="2879" y="-1878.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">generator</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_schemas_generator -->
<g id="edge76" class="edge">
<title>plugginger_api&#45;&gt;plugginger_schemas_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2946.71,-2860.84C2997.74,-2847.35 3082.41,-2817.82 3132,-2761.91"/>
<path fill="none" stroke="black" d="M3132,-2759.91C3158.82,-2729.68 3090.39,-2008.94 3058,-1987.84"/>
<path fill="none" stroke="black" d="M3058,-1985.84C3014.34,-1958.85 2961.59,-1932.96 2924.71,-1915.87"/>
<polygon fill="blue" stroke="black" points="2926.2,-1912.7 2915.65,-1911.7 2923.27,-1919.06 2926.2,-1912.7"/>
</g>
<!-- plugginger_stubgen -->
<g id="node110" class="node">
<title>plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#35a1a1" stroke="black" cx="3057" cy="-1894.45" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="3057" y="-1897.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="3057" y="-1884.95" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">stubgen</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_stubgen -->
<g id="edge77" class="edge">
<title>plugginger_api&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3037,-2759.91C3066.8,-2679.2 3074.82,-2071.23 3096,-1987.84"/>
</g>
<!-- plugginger_testing_helpers -->
<g id="node113" class="node">
<title>plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="5355,-1917.57 5285,-1917.57 5285,-1871.32 5355,-1871.32 5355,-1917.57"/>
<text text-anchor="middle" x="5320" y="-1904.07" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="5320" y="-1891.32" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing.</text>
<text text-anchor="middle" x="5320" y="-1878.57" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">helpers</text>
</g>
<!-- plugginger_api&#45;&gt;plugginger_testing_helpers -->
<g id="edge78" class="edge">
<title>plugginger_api&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3666,-2759.91C3914.45,-2623.5 3943.99,-2538.69 4184,-2387.92 4576.85,-2141.15 5114.71,-1960.44 5273.86,-1909.8"/>
<polygon fill="blue" stroke="black" points="5274.74,-1913.19 5283.22,-1906.83 5272.63,-1906.52 5274.74,-1913.19"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge79" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1216.41,-2138.06C1230.52,-2122.99 1247.38,-2101.89 1256,-2079.67"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger__internal_proxy -->
<g id="edge80" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1228.48,-2148.97C1257.73,-2136.99 1295.06,-2114.97 1299,-2079.67"/>
<path fill="none" stroke="black" d="M1299,-2077.67C1301.12,-2058.7 1305.61,-2037.88 1309.81,-2021.06"/>
<polygon fill="blue" stroke="black" points="1313.14,-2022.16 1312.25,-2011.6 1306.36,-2020.41 1313.14,-2022.16"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api -->
<g id="edge81" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1228.23,-2177.35C1253.47,-2188.44 1287.99,-2204.49 1317,-2221.17 1349.25,-2239.71 1350.78,-2256.57 1386,-2268.55 1527.38,-2316.6 1922.13,-2240.44 2057,-2304.55 2082.68,-2316.75 2074.98,-2338.42 2100,-2351.92 2190.67,-2400.85 2236.9,-2341.75 2329,-2387.92 2366.2,-2406.57 2359.29,-2433.73 2396,-2453.33 2464.51,-2489.91 2500.81,-2448.69 2567,-2489.33 2711.24,-2577.89 2675.96,-2674.29 2796,-2793.61 2816.65,-2814.14 2843.29,-2833.13 2864.95,-2847.08"/>
<polygon fill="blue" stroke="black" points="2862.9,-2849.93 2873.23,-2852.31 2866.64,-2844.01 2862.9,-2849.93"/>
</g>
<!-- plugginger_api_app_plugin -->
<g id="node36" class="node">
<title>plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2318,-2009.96 2248,-2009.96 2248,-1963.71 2318,-1963.71 2318,-2009.96"/>
<text text-anchor="middle" x="2283" y="-1996.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2283" y="-1983.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="2283" y="-1970.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">app_plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_app_plugin -->
<g id="edge82" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1239.52,-2157.89C1339.87,-2151.86 1583.93,-2134.82 1786,-2101.79 1953.64,-2074.39 2149.2,-2024.05 2236.64,-2000.55"/>
<polygon fill="blue" stroke="black" points="1239.58,-2154.38 1229.8,-2158.47 1239.99,-2161.37 1239.58,-2154.38"/>
<polygon fill="blue" stroke="black" points="2237.54,-2003.93 2246.29,-1997.94 2235.72,-1997.17 2237.54,-2003.93"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_builder -->
<g id="edge83" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1191.06,-2138.09C1183.56,-2042.66 1161.37,-1661.61 1256,-1371.07"/>
</g>
<!-- plugginger_api_plugin -->
<g id="node41" class="node">
<title>plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2901,-2101.79 2831,-2101.79 2831,-2055.54 2901,-2055.54 2901,-2101.79"/>
<text text-anchor="middle" x="2866" y="-2088.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2866" y="-2075.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">api.</text>
<text text-anchor="middle" x="2866" y="-2062.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin</text>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_api_plugin -->
<g id="edge84" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1239.78,-2156.57C1300.44,-2151.61 1409.56,-2143.08 1503,-2137.79 1903.57,-2115.15 2004.18,-2119.41 2405,-2101.79 2556.61,-2095.13 2736.2,-2086.22 2819.4,-2082.03"/>
<polygon fill="blue" stroke="black" points="1239.5,-2153.08 1229.82,-2157.39 1240.07,-2160.06 1239.5,-2153.08"/>
<polygon fill="blue" stroke="black" points="2819.4,-2085.53 2829.21,-2081.53 2819.05,-2078.54 2819.4,-2085.53"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_cli_cmd_inspect -->
<g id="edge85" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_cli_cmd_inspect</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1157.66,-2157.63C1034.13,-2146.98 624.07,-2105.65 530,-2019.54 454.58,-1950.51 260,-1242.07 260,-1139.82 260,-1139.82 260,-1139.82 260,-1051.12 260,-994.04 273.98,-973.55 317,-936.04 394.02,-868.89 463.3,-935.84 535,-863.04 574.11,-823.33 543.39,-790.75 573,-743.54 593.9,-710.21 628.75,-715.15 630,-675.83"/>
</g>
<!-- plugginger_api_app&#45;&gt;plugginger_testing_helpers -->
<g id="edge86" class="edge">
<title>plugginger_api_app&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1228.3,-2157.35C1285.8,-2152.37 1403.29,-2142.72 1503,-2137.79 2127.78,-2106.96 2285.43,-2136.49 2910,-2101.79 3865.01,-2048.74 5028.41,-1926.75 5273.38,-1900.49"/>
<polygon fill="blue" stroke="black" points="5273.6,-1903.98 5283.17,-1899.44 5272.85,-1897.02 5273.6,-1903.98"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge87" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2292.52,-1963.28C2299.31,-1945.27 2307.35,-1919.21 2308,-1895.45"/>
<path fill="none" stroke="black" d="M2308,-1893.45C2309.15,-1851.19 2287.78,-1844.61 2270,-1806.26"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api -->
<g id="edge88" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2318.49,-2001.73C2405.21,-2037.9 2626.81,-2144.37 2709.24,-2316.85"/>
<polygon fill="blue" stroke="black" points="2706.02,-2318.22 2713.37,-2325.86 2712.39,-2315.31 2706.02,-2318.22"/>
<path fill="none" stroke="black" d="M2714,-2329.23C2767.09,-2451.74 2780,-2482.72 2824,-2608.77 2855.77,-2699.78 2887.46,-2810.93 2899.22,-2853.04"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge90" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2308,-1893.45C2310.58,-1798.58 2320.52,-1770.28 2289,-1680.76 2282.61,-1662.62 2271.57,-1662.83 2265,-1644.76 2252.44,-1610.2 2258,-1599.23 2256,-1562.51 2251.34,-1477.11 2227.54,-1450.72 2256,-1370.07 2268.25,-1335.35 2302.29,-1341.95 2308,-1305.57"/>
<path fill="none" stroke="black" d="M2308,-1303.57C2314,-1230.55 2271.02,-1210.85 2289,-1139.82"/>
</g>
<!-- plugginger_api_app_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge91" class="edge">
<title>plugginger_api_app_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2308.08,-1963.54C2364.8,-1910.52 2498,-1770.29 2498,-1622.64 2498,-1622.64 2498,-1622.64 2498,-1303.57 2498,-1123.67 2637.12,-1028.3 2512,-899.04 2426.41,-810.61 1500.8,-940.28 1405,-863.04 1290.01,-770.33 1411.49,-659.73 1324,-540.72 1315.33,-528.93 1303.48,-519.03 1291.09,-510.93"/>
<polygon fill="blue" stroke="black" points="1293.2,-508.12 1282.84,-505.88 1289.54,-514.09 1293.2,-508.12"/>
</g>
<!-- plugginger_api_background -->
<g id="node37" class="node">
<title>plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3ab0b0" stroke="black" cx="2904" cy="-2981.76" rx="51.62" ry="32.7"/>
<text text-anchor="middle" x="2904" y="-2991.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2904" y="-2978.63" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">api.</text>
<text text-anchor="middle" x="2904" y="-2965.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">background</text>
</g>
<!-- plugginger_api_background&#45;&gt;plugginger_api -->
<g id="edge92" class="edge">
<title>plugginger_api_background&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2904,-2948.81C2904,-2933.68 2904,-2915.76 2904,-2901.06"/>
<polygon fill="#3ab0b0" stroke="black" points="2907.5,-2901.12 2904,-2891.12 2900.5,-2901.12 2907.5,-2901.12"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_api -->
<g id="edge93" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1217.04,-826.73C1225.63,-839.42 1238.03,-854.44 1253,-863.04 1298.34,-889.07 2133.35,-997.89 2181,-1019.41 2306.7,-1076.2 2422,-1077.02 2422,-1214.95 2422,-1449.79 2422,-1449.79 2422,-1449.79 2422,-1501.01 2431.84,-1513.29 2446,-1562.51 2505.78,-1770.35 2537.75,-1816.8 2613,-2019.54 2626.59,-2056.17 2620.87,-2070.31 2644,-2101.79 2659.37,-2122.72 2677.18,-2115.22 2690,-2137.79 2729.94,-2208.14 2689.29,-2243.99 2710.42,-2316.29"/>
<polygon fill="blue" stroke="black" points="2707.09,-2317.38 2713.53,-2325.8 2713.74,-2315.2 2707.09,-2317.38"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge94" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1183,-673.83C1185.93,-629.75 1187.04,-618.42 1183,-574.43"/>
<path fill="none" stroke="black" d="M1183,-572.43C1180.79,-548.3 1164.15,-527.59 1146.61,-512.16"/>
<polygon fill="blue" stroke="black" points="1149.09,-509.67 1139.16,-505.98 1144.63,-515.06 1149.09,-509.67"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_cmd_inspect -->
<g id="edge95" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_cmd_inspect</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1197.13,-780.06C1190.11,-754.92 1180.55,-712.69 1183,-675.83"/>
<path fill="none" stroke="black" d="M1183,-673.83C1189.65,-573.87 1063.34,-636.88 968,-606.13 925.24,-592.34 904.59,-605.35 872,-574.43"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_cmd_new -->
<g id="edge96" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_cmd_new</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1183,-673.83C1190.78,-556.9 1622.15,-688 1706,-606.13 1745.75,-567.32 1748.72,-532.83 1725,-482.6"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge97" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1183,-572.43C1181.09,-551.57 1190.99,-530.6 1202.09,-514.18"/>
<polygon fill="blue" stroke="black" points="1204.77,-516.45 1207.8,-506.29 1199.1,-512.34 1204.77,-516.45"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_cli_utils -->
<g id="edge98" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_cli_utils</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1183,-673.83C1186.24,-625.06 1130.66,-599.2 1084.74,-586.21"/>
<polygon fill="blue" stroke="black" points="1085.64,-582.83 1075.08,-583.65 1083.85,-589.6 1085.64,-582.83"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_discovery_discovery -->
<g id="edge99" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_discovery_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1157.96,-796.49C996.58,-776.17 466.05,-709.28 462,-707.54 407.15,-683.96 355.91,-636.25 326.02,-604.82"/>
<polygon fill="blue" stroke="black" points="1157.07,-799.91 1167.43,-797.68 1157.94,-792.96 1157.07,-799.91"/>
<polygon fill="blue" stroke="black" points="328.96,-602.83 319.57,-597.92 323.84,-607.62 328.96,-602.83"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_plugins_core_loader -->
<g id="edge100" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_plugins_core_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1183,-673.83C1184.77,-647.19 1202.61,-623.6 1221,-606.09"/>
<polygon fill="blue" stroke="black" points="1223.22,-608.8 1228.3,-599.5 1218.53,-603.6 1223.22,-608.8"/>
</g>
<!-- plugginger_api_builder&#45;&gt;plugginger_plugins_internal_loader -->
<g id="edge101" class="edge">
<title>plugginger_api_builder&#45;&gt;plugginger_plugins_internal_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1239.23,-789.91C1325.1,-759.62 1551.84,-678.94 1739,-606.13 1744.96,-603.81 1751.15,-601.35 1757.32,-598.86"/>
<polygon fill="blue" stroke="black" points="1758.3,-602.25 1766.25,-595.24 1755.66,-595.76 1758.3,-602.25"/>
</g>
<!-- plugginger_api_depends -->
<g id="node39" class="node">
<title>plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#05e5e5" stroke="black" cx="1589" cy="-2981.76" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1589" y="-2991.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1589" y="-2978.63" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">api.</text>
<text text-anchor="middle" x="1589" y="-2965.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">depends</text>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge102" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1662,-2419.63C1656.84,-2322.93 1762.65,-2347.3 1819,-2268.55 1946.84,-2089.88 1848.75,-1971.08 1994,-1806.26"/>
<path fill="none" stroke="black" d="M1994,-1804.26C2030.65,-1750.11 2089.84,-1774.08 2108,-1711.26"/>
<path fill="none" stroke="black" d="M2108,-1709.26C2128.21,-1639.35 2091.87,-1616.48 2108,-1545.51"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge103" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1584.61,-2948.85C1576.86,-2882.86 1565.43,-2727.63 1610,-2608.77 1617.12,-2589.79 1626.61,-2589.53 1638,-2572.77 1649.81,-2555.39 1659.53,-2552.91 1662,-2532.05"/>
<path fill="none" stroke="black" d="M1662,-2530.05C1670.75,-2416.02 1337.82,-2283.84 1280,-2185.17 1255.69,-2143.68 1238.6,-2124.5 1256,-2079.67"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge104" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1662,-2530.05C1667.66,-2482.19 1675.15,-2467.98 1662,-2421.63"/>
<path fill="none" stroke="black" d="M1662,-2419.63C1647.94,-2370.06 1621.04,-2348.45 1648,-2304.55 1666.52,-2274.4 1695.03,-2294.57 1719,-2268.55 1746.11,-2239.11 1745.3,-2224.44 1753,-2185.17 1757.05,-2164.51 1764.02,-2155.74 1753,-2137.79 1727.1,-2095.61 1673.36,-2126.72 1658,-2079.67"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api -->
<g id="edge105" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1637.38,-2973.51C1688.21,-2966.17 1770.54,-2955.07 1842,-2949.05 2209.93,-2918.07 2304.94,-2953.05 2672,-2913.05 2733.4,-2906.36 2803.11,-2893.39 2849.95,-2883.88"/>
<polygon fill="#05e5e5" stroke="black" points="2850.48,-2887.34 2859.57,-2881.91 2849.07,-2880.49 2850.48,-2887.34"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_builder -->
<g id="edge106" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1563.72,-2953.32C1495.72,-2878.31 1306.27,-2661.56 1192,-2453.33 1072.24,-2235.09 1142,-2144.39 1142,-1895.45 1142,-1895.45 1142,-1895.45 1142,-1543.51 1142,-1473.44 1178.7,-1462.94 1221,-1407.07 1234.47,-1389.28 1248.22,-1391.99 1256,-1371.07"/>
<path fill="none" stroke="black" d="M1256,-1370.07C1292.32,-1233.86 1196.39,-1174.54 1268,-1053.12"/>
</g>
<!-- plugginger_api_depends&#45;&gt;plugginger_api_plugin -->
<g id="edge107" class="edge">
<title>plugginger_api_depends&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1626.63,-2960.08C1708.77,-2914.52 1909.52,-2800.91 2069,-2692.21 2374.41,-2484.04 2720,-2201.05 2830.24,-2109.53"/>
<polygon fill="#05e5e5" stroke="black" points="2832.35,-2112.32 2837.8,-2103.24 2827.88,-2106.94 2832.35,-2112.32"/>
</g>
<!-- plugginger_api_events&#45;&gt;plugginger_api -->
<g id="edge108" class="edge">
<title>plugginger_api_events&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3476,-1139.82C3355.72,-1512.87 3502.39,-1629.21 3538,-2019.54 3543.72,-2082.23 3552,-2097.53 3552,-2160.48 3552,-2245.86 3552,-2245.86 3552,-2245.86 3552,-2293.75 3561.68,-2313.57 3533,-2351.92 3511.57,-2380.57 3489.25,-2365.97 3461,-2387.92 3358.54,-2467.56 3345.14,-2502.97 3270,-2608.77 3212.72,-2689.42 3226.67,-2736.35 3146,-2793.61 3084.9,-2836.99 2998.23,-2856.59 2946.9,-2864.86"/>
</g>
<!-- plugginger_api_events&#45;&gt;plugginger_schemas_generator -->
<g id="edge109" class="edge">
<title>plugginger_api_events&#45;&gt;plugginger_schemas_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3471.95,-1075.5C3476.58,-1089.79 3480.83,-1108.91 3478.47,-1126.69"/>
<polygon fill="blue" stroke="black" points="3475.08,-1125.82 3476.33,-1136.34 3481.91,-1127.34 3475.08,-1125.82"/>
<path fill="none" stroke="black" d="M3476,-1139.82C3453.73,-1197.23 3393.73,-1151.59 3348,-1192.82 3209.26,-1317.92 3249.11,-1405.43 3148,-1562.51 3075.06,-1675.82 3071.63,-1721.87 2965,-1804.26"/>
<path fill="none" stroke="black" d="M2965,-1806.26C2939.76,-1825.76 2914.58,-1852.34 2898.04,-1871.01"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge110" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2869.32,-2055.16C2871.99,-2037 2875.8,-2010.78 2879,-1987.84"/>
<path fill="none" stroke="black" d="M2879,-1985.84C2883.95,-1950.3 2845.2,-1952.54 2835,-1918.14 2820.86,-1870.42 2852.47,-1854.44 2840,-1806.26"/>
<path fill="none" stroke="black" d="M2840,-1804.26C2706.38,-1433.97 2285.9,-1656.73 2108,-1305.57"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge111" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2836.19,-2055.05C2782.57,-2013.79 2667.34,-1922.47 2580,-1834.76 2568.32,-1823.03 2569.27,-1816.17 2556,-1806.26"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge112" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2830.58,-2068.37C2763.99,-2051.07 2615.46,-2013.42 2489,-1987.84"/>
<path fill="none" stroke="black" d="M2489,-1985.84C2417.09,-1971.3 2399.58,-1964.82 2327,-1954.14 2167.04,-1930.58 2125.04,-1941.1 1965,-1918.14 1771.06,-1890.31 1544.09,-1843.69 1435.01,-1820.4"/>
<polygon fill="blue" stroke="black" points="1435.88,-1817.01 1425.36,-1818.33 1434.41,-1823.85 1435.88,-1817.01"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge113" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2830.7,-2086.47C2783.18,-2096.01 2696.26,-2114.79 2624,-2137.79 2552.73,-2160.48 2055.78,-2339.23 1999,-2387.92 1990.07,-2395.58 1958.03,-2451.89 1936.21,-2491.29"/>
<polygon fill="blue" stroke="black" points="1933.27,-2489.38 1931.5,-2499.82 1939.39,-2492.76 1933.27,-2489.38"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge114" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2830.66,-2083.13C2766.14,-2089.95 2625.31,-2107.19 2510,-2137.79 2341.8,-2182.43 2151.6,-2261.39 2058.9,-2301.83"/>
<polygon fill="blue" stroke="black" points="2057.77,-2298.5 2050.01,-2305.72 2060.57,-2304.91 2057.77,-2298.5"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge115" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2489,-1985.84C2229.74,-1935.88 1925.6,-1860.85 1786.88,-1825.72"/>
<polygon fill="blue" stroke="black" points="1788,-1822.39 1777.44,-1823.32 1786.28,-1829.17 1788,-1822.39"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api -->
<g id="edge116" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2867.11,-2102C2869.62,-2148.63 2876.58,-2259.66 2890,-2351.92 2898.97,-2413.6 2911.75,-2427.32 2918,-2489.33 2931.57,-2623.88 2930.19,-2658.93 2918,-2793.61 2916.54,-2809.73 2913.39,-2827.52 2910.45,-2841.91"/>
<polygon fill="blue" stroke="black" points="2907.05,-2841.1 2908.38,-2851.61 2913.89,-2842.56 2907.05,-2841.1"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin -->
<g id="edge118" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2830.66,-2072.61C2762.04,-2062.74 2606.02,-2040.1 2475,-2019.54 2425.18,-2011.73 2368.18,-2002.25 2329.32,-1995.7"/>
<polygon fill="blue" stroke="black" points="2330.3,-1992.32 2319.85,-1994.11 2329.13,-1999.22 2330.3,-1992.32"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_api_builder -->
<g id="edge119" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2840,-1804.26C2836.16,-1763.11 2844.33,-1752.37 2840,-1711.26"/>
<path fill="none" stroke="black" d="M2840,-1709.26C2834.64,-1658.32 2795.13,-1663.31 2764,-1622.64"/>
<path fill="none" stroke="black" d="M2764,-1620.64C2721.26,-1564.79 2709.47,-1551.23 2674,-1490.51 2635.01,-1423.76 2626.67,-1405.86 2598,-1334.07 2522.91,-1146.09 2597.56,-1048.54 2460,-900.04"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge120" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2764,-1620.64C2747.16,-1591.81 2770.8,-1578.2 2764,-1545.51"/>
<path fill="none" stroke="black" d="M2764,-1543.51C2759.04,-1519.66 2755.24,-1514.3 2750,-1490.51 2715.98,-1335.93 2688,-1298.1 2688,-1139.82 2688,-1139.82 2688,-1139.82 2688,-1051.12 2688,-981.11 2705.37,-937.69 2647,-899.04 2534.86,-824.77 1541.7,-947.47 1437,-863.04 1379.55,-816.71 1410.77,-591.27 1357,-540.72 1295.06,-482.48 1250.02,-524.89 1164.08,-505.16"/>
<polygon fill="blue" stroke="black" points="1165.13,-501.82 1154.58,-502.73 1163.4,-508.6 1165.13,-501.82"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_inspect -->
<g id="edge121" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_inspect</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2830.64,-2076.45C2727.2,-2072.92 2416.16,-2062.47 2158,-2055.54 2113.37,-2054.35 582.22,-2051.47 551,-2019.54 535.02,-2003.2 511.65,-1927.97 579,-1598.51 612.76,-1433.37 649.44,-1399.87 700,-1239.07 752.23,-1072.96 767.24,-1031.84 810,-863.04 826.11,-799.45 919.59,-619.58 872,-574.43"/>
<path fill="none" stroke="black" d="M872,-572.43C853.87,-555.29 837.09,-532.78 824.85,-514.58"/>
<polygon fill="blue" stroke="black" points="827.79,-512.67 819.38,-506.24 821.94,-516.52 827.79,-512.67"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge122" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2764,-1543.51C2749.6,-1468.23 2753.13,-1446.94 2764,-1371.07"/>
<path fill="none" stroke="black" d="M2764,-1370.07C2769.38,-1341.91 2757.9,-1333.58 2764,-1305.57"/>
<path fill="none" stroke="black" d="M2764,-1303.57C2767,-1289.78 2769.6,-1286.42 2778,-1275.07 2800.47,-1244.71 2852.98,-1252.42 2840,-1216.95"/>
<path fill="none" stroke="black" d="M2840,-1214.95C2823.64,-1170.23 2777.11,-1195.97 2750,-1156.82 2683.26,-1060.43 2784.18,-973.96 2694,-899.04 2590.29,-812.87 1581.81,-952.59 1481,-863.04 1372.72,-766.85 1528.56,-649.56 1433,-540.72 1399.92,-503.05 1344.33,-489 1299.52,-484.11"/>
<polygon fill="blue" stroke="black" points="1299.88,-480.63 1289.59,-483.18 1299.22,-487.6 1299.88,-480.63"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="edge123" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2840,-1709.26C2807.41,-1550.57 3096,-1611.79 3096,-1449.79 3096,-1449.79 3096,-1449.79 3096,-1214.95 3096,-1144.63 3165.05,-1151.95 3186,-1084.82 3210.6,-1006 3241.57,-960.11 3186,-899.04 3128.76,-836.14 2883.45,-895.86 2805,-863.04 2795.33,-858.99 2785.98,-853.12 2777.44,-846.63"/>
<polygon fill="blue" stroke="black" points="2779.66,-843.93 2769.69,-840.36 2775.26,-849.37 2779.66,-843.93"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin -->
<g id="edge124" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2764,-1370.07C2772.56,-1309.36 2916,-1201.13 2916,-1139.82 2916,-1139.82 2916,-1139.82 2916,-958.73 2916,-897.16 2850.65,-914.28 2791,-899.04 2649.76,-862.94 2270.55,-919.12 2136,-863.04 2126.72,-859.17 2117.89,-853.38 2109.91,-846.92"/>
<polygon fill="blue" stroke="black" points="2112.26,-844.32 2102.42,-840.4 2107.67,-849.61 2112.26,-844.32"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="edge125" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2879,-1985.84C2889.1,-1926.45 2932.37,-1932.34 2980,-1895.45"/>
<path fill="none" stroke="black" d="M2980,-1893.45C3026.33,-1857.56 3160.88,-1460.92 3184,-1407.07 3209.28,-1348.19 3210.73,-1331 3242,-1275.07 3309.44,-1154.44 3327.62,-1123.09 3419,-1019.41 3471.52,-959.83 3499.84,-959.94 3552,-900.04"/>
<path fill="none" stroke="black" d="M3552,-899.04C3563.63,-881.64 3577.74,-863.57 3590.69,-847.94"/>
<polygon fill="blue" stroke="black" points="3593.03,-850.59 3596.77,-840.68 3587.67,-846.1 3593.03,-850.59"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin -->
<g id="edge126" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2980,-1893.45C2990.32,-1885.3 2989.6,-1879.96 2999,-1870.76 3017.31,-1852.85 3024.45,-1851.3 3044,-1834.76 3298.89,-1619.13 3467.67,-1638.83 3604,-1334.07 3655.05,-1219.95 3590,-1178.14 3590,-1053.12 3590,-1053.12 3590,-1053.12 3590,-958.73 3590,-899.56 3651.85,-914.36 3709,-899.04 3839.73,-863.98 4190.9,-912.21 4317,-863.04 4327.15,-859.08 4336.96,-853.12 4345.9,-846.49"/>
<polygon fill="blue" stroke="black" points="4348.01,-849.28 4353.68,-840.34 4343.67,-843.79 4348.01,-849.28"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin -->
<g id="edge127" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2901.37,-2074.61C2950.55,-2069.19 3041.28,-2054.93 3110,-2019.54 3720.72,-1705.04 3669.7,-1349.57 4236,-960.73"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_schemas_generator -->
<g id="edge128" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_schemas_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2879,-1985.84C2881.6,-1967.15 2881.76,-1946.07 2881.2,-1928.98"/>
<polygon fill="blue" stroke="black" points="2884.71,-1929.21 2880.78,-1919.37 2877.72,-1929.51 2884.71,-1929.21"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_stubgen -->
<g id="edge129" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2901.39,-2078.56C2960.81,-2077.84 3076.1,-2066.16 3096,-1987.84"/>
<path fill="none" stroke="black" d="M3096,-1985.84C3101.24,-1965.21 3092.15,-1943.25 3081.43,-1926.15"/>
<polygon fill="blue" stroke="black" points="3084.38,-1924.26 3075.88,-1917.94 3078.58,-1928.18 3084.38,-1924.26"/>
</g>
<!-- plugginger_api_plugin&#45;&gt;plugginger_testing_helpers -->
<g id="edge130" class="edge">
<title>plugginger_api_plugin&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2901.48,-2072.93C3040.59,-2054.55 3562.35,-1987.29 3994,-1954.14 4494.04,-1915.73 5102.23,-1900.21 5273.28,-1896.42"/>
<polygon fill="blue" stroke="black" points="5273.17,-1899.92 5283.09,-1896.2 5273.02,-1892.92 5273.17,-1899.92"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_api -->
<g id="edge131" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_api</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3791.03,-1075.57C3801.36,-1106.51 3818,-1164.21 3818,-1214.95 3818,-1371.07 3818,-1371.07 3818,-1371.07 3818,-1515.71 3756.25,-1541.27 3718,-1680.76 3670.16,-1855.2 3628,-1896.79 3628,-2077.67 3628,-2162.48 3628,-2162.48 3628,-2162.48 3628,-2333.07 3334.62,-2701.55 3191,-2793.61 3118.7,-2839.96 3019.02,-2858.47 2958.18,-2865.75"/>
<polygon fill="blue" stroke="black" points="2957.97,-2862.26 2948.42,-2866.86 2958.75,-2869.21 2957.97,-2862.26"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="edge132" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3775.74,-1028.71C3765.64,-998.64 3745.96,-943.92 3723,-900.04"/>
<path fill="none" stroke="black" d="M3723,-899.04C3690.55,-859.64 2852.61,-881.43 2805,-863.04 2795,-859.18 2785.41,-853.26 2776.7,-846.65"/>
<polygon fill="blue" stroke="black" points="2779.11,-844.1 2769.14,-840.51 2774.7,-849.53 2779.11,-844.1"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin -->
<g id="edge133" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3747.54,-1039.22C3655.93,-1009.1 3403.32,-930.07 3186,-899.04 3070.44,-882.53 2244.21,-906.84 2136,-863.04 2126.68,-859.26 2117.83,-853.52 2109.85,-847.08"/>
<polygon fill="blue" stroke="black" points="2112.2,-844.48 2102.35,-840.57 2107.6,-849.77 2112.2,-844.48"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="edge134" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3723,-899.04C3712.94,-879.81 3697.65,-861.96 3682.32,-847.07"/>
<polygon fill="blue" stroke="black" points="3685.11,-844.88 3675.42,-840.59 3680.31,-849.99 3685.11,-844.88"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin -->
<g id="edge135" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3723,-899.04C3692.35,-840.45 4255.41,-887.1 4317,-863.04 4327.15,-859.07 4336.95,-853.11 4345.89,-846.48"/>
<polygon fill="blue" stroke="black" points="4348,-849.27 4353.68,-840.32 4343.66,-843.78 4348,-849.27"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin -->
<g id="edge136" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3818.47,-1050.13C3916.9,-1046.51 4189.25,-1030.28 4236,-960.73"/>
<path fill="none" stroke="black" d="M4236,-958.73C4360.26,-773.84 4525.06,-954.91 4728,-863.04 4737.81,-858.6 4747.51,-852.66 4756.52,-846.26"/>
<polygon fill="blue" stroke="black" points="4758.5,-849.15 4764.43,-840.37 4754.32,-843.54 4758.5,-849.15"/>
</g>
<!-- plugginger_api_service&#45;&gt;plugginger_schemas_generator -->
<g id="edge137" class="edge">
<title>plugginger_api_service&#45;&gt;plugginger_schemas_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3747.83,-1069.8C3646.65,-1118.33 3362,-1258.82 3362,-1303.57 3362,-1371.07 3362,-1371.07 3362,-1371.07 3362,-1579.82 3227.3,-1601.39 3071,-1739.76 3032.61,-1773.74 3010.9,-1770.13 2973.72,-1797.49"/>
<polygon fill="blue" stroke="black" points="2971.95,-1794.43 2966.2,-1803.33 2976.24,-1799.96 2971.95,-1794.43"/>
</g>
<!-- plugginger_cli -->
<g id="node43" class="node">
<title>plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="1143.62,-306.36 1062.38,-306.36 1062.38,-270.36 1143.62,-270.36 1143.62,-306.36"/>
<text text-anchor="middle" x="1103" y="-285.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.cli</text>
</g>
<!-- plugginger_cli&#45;&gt;plugginger_ai_plugin_generator -->
<g id="edge138" class="edge">
<title>plugginger_cli&#45;&gt;plugginger_ai_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1143.96,-283.68C1223.49,-276.7 1404.43,-261.73 1557,-255.66 1658.04,-251.63 5104.5,-260.5 5197,-219.66 5222.68,-208.32 5216.61,-188.92 5240,-173.41 5278.32,-148 5327.39,-130.66 5365.51,-119.89"/>
<polygon fill="blue" stroke="black" points="5366.27,-123.31 5374.99,-117.29 5364.42,-116.56 5366.27,-123.31"/>
</g>
<!-- plugginger_cli&#45;&gt;plugginger_ai_wiring_analyzer -->
<g id="edge139" class="edge">
<title>plugginger_cli&#45;&gt;plugginger_ai_wiring_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1143.96,-283.76C1223.5,-276.93 1404.45,-262.2 1557,-255.66 1657.64,-251.34 5112.99,-269.97 5183,-197.53"/>
</g>
<!-- plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli -->
<g id="edge140" class="edge">
<title>plugginger_cli_cmd_core_freeze&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1101.52,-457.99C1100.68,-439.77 1100.27,-413.53 1103,-390.77"/>
</g>
<!-- plugginger_cli_cmd_inspect&#45;&gt;plugginger_ai_wiring_analyzer -->
<g id="edge141" class="edge">
<title>plugginger_cli_cmd_inspect&#45;&gt;plugginger_ai_wiring_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M795.9,-458.14C787.94,-434.7 781.01,-400.07 805,-390.77"/>
</g>
<!-- plugginger_cli_cmd_inspect&#45;&gt;plugginger_cli -->
<g id="edge142" class="edge">
<title>plugginger_cli_cmd_inspect&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M844.12,-462.07C847.1,-460.81 850.09,-459.59 853,-458.47 902.98,-439.22 922.09,-450.05 968,-422.47 1015.56,-393.91 1059.02,-345.25 1083.16,-315.34"/>
<polygon fill="blue" stroke="black" points="1085.6,-317.9 1089.07,-307.89 1080.11,-313.55 1085.6,-317.9"/>
</g>
<!-- plugginger_cli_cmd_new&#45;&gt;plugginger_ai_plugin_generator -->
<g id="edge143" class="edge">
<title>plugginger_cli_cmd_new&#45;&gt;plugginger_ai_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1736.91,-388.88C2097.67,-389.47 4310.96,-388.94 4974,-289.36"/>
<path fill="none" stroke="black" d="M4974,-287.36C4990.24,-284.92 4979.15,-264.49 4993,-255.66 5122.24,-173.23 5200.25,-290.83 5336,-219.66 5347.48,-213.64 5344.77,-205.49 5355,-197.53"/>
<path fill="none" stroke="black" d="M5355,-195.53C5360.88,-190.96 5384.21,-160.96 5402.54,-136.87"/>
<polygon fill="#2bacac" stroke="black" points="5405.1,-139.28 5408.36,-129.2 5399.52,-135.05 5405.1,-139.28"/>
</g>
<!-- plugginger_cli_cmd_new&#45;&gt;plugginger_cli -->
<g id="edge144" class="edge">
<title>plugginger_cli_cmd_new&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1639.21,-380.63C1530.83,-362.19 1267.13,-317.3 1154.92,-298.2"/>
<polygon fill="#2bacac" stroke="black" points="1155.85,-294.81 1145.4,-296.58 1154.67,-301.71 1155.85,-294.81"/>
</g>
<!-- plugginger_cli_cmd_project_run -->
<g id="node47" class="node">
<title>plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="958.5,-504.72 861.5,-504.72 861.5,-458.47 958.5,-458.47 958.5,-504.72"/>
<text text-anchor="middle" x="910" y="-491.22" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="910" y="-478.47" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cli.</text>
<text text-anchor="middle" x="910" y="-465.72" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">cmd_project_run</text>
</g>
<!-- plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli -->
<g id="edge145" class="edge">
<title>plugginger_cli_cmd_project_run&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M958.66,-478.06C1013.35,-472.18 1095.55,-452.97 1103,-390.77"/>
<path fill="none" stroke="black" d="M1103,-388.77C1105.84,-365.07 1105.61,-337.97 1104.8,-318.05"/>
<polygon fill="blue" stroke="black" points="1108.3,-318 1104.32,-308.19 1101.31,-318.35 1108.3,-318"/>
</g>
<!-- plugginger_cli_cmd_schema -->
<g id="node48" class="node">
<title>plugginger_cli_cmd_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3ab0b0" stroke="black" cx="902" cy="-389.77" rx="56.92" ry="32.7"/>
<text text-anchor="middle" x="902" y="-399.39" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="902" y="-386.64" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">cli.</text>
<text text-anchor="middle" x="902" y="-373.89" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">cmd_schema</text>
</g>
<!-- plugginger_cli_cmd_schema&#45;&gt;plugginger_cli -->
<g id="edge146" class="edge">
<title>plugginger_cli_cmd_schema&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M944.59,-367.74C971.63,-354.38 1007.37,-336.71 1039,-321.06 1044.98,-318.11 1051.27,-314.99 1057.49,-311.91"/>
<polygon fill="#3ab0b0" stroke="black" points="1059.03,-315.06 1066.44,-307.48 1055.92,-308.78 1059.03,-315.06"/>
</g>
<!-- plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli -->
<g id="edge147" class="edge">
<title>plugginger_cli_cmd_stubs_generate&#45;&gt;plugginger_cli</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1181.94,-458.01C1145.15,-438.72 1100.58,-410.97 1103,-390.77"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_ai_wiring_analyzer -->
<g id="edge148" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_ai_wiring_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1019.39,-540.62C1005.74,-470.58 979.37,-307.77 1025,-289.36"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge149" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1049.94,-544.5C1058.34,-534.7 1067.82,-523.63 1076.43,-513.6"/>
<polygon fill="#0bdfdf" stroke="black" points="1078.88,-516.12 1082.73,-506.25 1073.56,-511.56 1078.88,-516.12"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_inspect -->
<g id="edge150" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_inspect</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M983.06,-556.43C950.22,-544.01 903.62,-526.01 854.68,-505.35"/>
<polygon fill="#0bdfdf" stroke="black" points="856.17,-502.18 845.6,-501.49 853.44,-508.62 856.17,-502.18"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge151" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M994.3,-547.88C979.97,-536.78 962.92,-523.58 947.91,-511.96"/>
<polygon fill="#0bdfdf" stroke="black" points="950.13,-509.24 940.08,-505.89 945.84,-514.78 950.13,-509.24"/>
</g>
<!-- plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge152" class="edge">
<title>plugginger_cli_utils&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1066.59,-554.46C1095.51,-541.67 1134.9,-524.24 1167.65,-509.75"/>
<polygon fill="#0bdfdf" stroke="black" points="1168.91,-513.02 1176.64,-505.77 1166.08,-506.61 1168.91,-513.02"/>
</g>
<!-- plugginger_config -->
<g id="node51" class="node">
<title>plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#05dbdb" stroke="black" cx="763" cy="-2871.33" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="763" y="-2874.58" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="763" y="-2861.83" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge153" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M788,-2649.49C794.13,-2628.77 796.22,-2605.12 796.56,-2584.49"/>
<polygon fill="#05dbdb" stroke="black" points="800.06,-2584.58 796.57,-2574.58 793.06,-2584.57 800.06,-2584.58"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge154" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M788,-2649.49C793.96,-2629.34 796.68,-2623.14 812,-2608.77 837.8,-2584.56 861.17,-2600.61 883,-2572.77 951.6,-2485.3 871.43,-2416.73 940,-2329.23"/>
<path fill="none" stroke="black" d="M940,-2327.23C968.15,-2287.95 948.56,-2266.97 964,-2221.17 983.78,-2162.5 1072.84,-1972.26 1090,-1954.14 1150.02,-1890.73 1243.33,-1849.08 1305.53,-1826.6"/>
<polygon fill="#05dbdb" stroke="black" points="1306.31,-1830.04 1314.57,-1823.4 1303.97,-1823.44 1306.31,-1830.04"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge155" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M807.9,-2860.95C936.49,-2833.64 1316.64,-2749.89 1624,-2651.49"/>
<path fill="none" stroke="black" d="M1624,-2649.49C1723.06,-2618.31 1988.89,-2612.11 2085,-2572.77 2089.36,-2570.99 2093.7,-2568.83 2097.95,-2566.44"/>
<polygon fill="#05dbdb" stroke="black" points="2099.62,-2569.53 2106.34,-2561.34 2095.98,-2563.55 2099.62,-2569.53"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge156" class="edge">
<title>plugginger_config&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M796.51,-2853.77C922.75,-2791.74 1382.47,-2569.19 1776,-2421.63"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_app -->
<g id="edge157" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M940,-2327.23C1006.37,-2242.55 1105.89,-2333.72 1168,-2245.86"/>
<path fill="none" stroke="black" d="M1168,-2243.86C1178.02,-2229.68 1184.11,-2211.38 1187.77,-2195.72"/>
<polygon fill="#05dbdb" stroke="black" points="1191.1,-2196.87 1189.71,-2186.37 1184.25,-2195.44 1191.1,-2196.87"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_api_builder -->
<g id="edge158" class="edge">
<title>plugginger_config&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M724,-2759.91C707.49,-2706.45 772.42,-2705.23 788,-2651.49"/>
<path fill="none" stroke="black" d="M788,-2649.49C801.99,-2601.24 725.13,-2618.79 705,-2572.77 690.14,-2538.79 685.78,-2521.04 705,-2489.33 722.93,-2459.75 748.4,-2476.59 774,-2453.33 813.81,-2417.17 815.68,-2399.89 840,-2351.92 933.2,-2168.09 953.05,-2117.85 1004,-1918.14 1034.32,-1799.27 1026.47,-1766.44 1042,-1644.76 1062.94,-1480.68 1013.69,-1424.33 1085,-1275.07 1100.6,-1242.42 1154.34,-1250.96 1142,-1216.95"/>
<path fill="none" stroke="black" d="M1142,-1214.95C1102.37,-1111.35 934.23,-1162.61 952,-1053.12"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge159" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M724,-2759.91C651.51,-2461.14 555.62,-2405.97 511,-2101.79 501.47,-2036.86 508.07,-2019.7 511,-1954.14 514.56,-1874.62 514.44,-1854.36 527,-1775.76 570.76,-1502.04 656.28,-1451.51 804,-1216.95"/>
</g>
<!-- plugginger_config&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge160" class="edge">
<title>plugginger_config&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M754.17,-2847.69C745.84,-2825.9 733.27,-2791.92 724,-2761.91"/>
<path fill="none" stroke="black" d="M724,-2759.91C712.8,-2723.66 691.11,-2724.98 672,-2692.21 622.66,-2607.61 630.07,-2576.79 586,-2489.33 554.1,-2426.02 539.67,-2413.76 505,-2351.92 423.15,-2205.94 396.84,-2172.09 328,-2019.54 308.2,-1975.67 299.14,-1965.39 290,-1918.14 268.44,-1806.61 235.94,-1511.77 280,-1407.07 353.33,-1232.81 488.88,-1289.06 624,-1156.82 668.44,-1113.33 901.09,-737.37 910,-675.83"/>
<path fill="none" stroke="black" d="M910,-673.83C916.42,-618.84 914.48,-554.35 912.34,-516.07"/>
<polygon fill="#05dbdb" stroke="black" points="915.86,-516.35 911.77,-506.58 908.88,-516.77 915.86,-516.35"/>
</g>
<!-- plugginger_config_models -->
<g id="node52" class="node">
<title>plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#05dbdb" stroke="black" cx="886" cy="-2981.76" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="886" y="-2991.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="886" y="-2978.63" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config.</text>
<text text-anchor="middle" x="886" y="-2965.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">models</text>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge161" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M686,-2870.33C648.72,-2825.98 724.41,-2664.39 767.27,-2581.63"/>
<polygon fill="#05dbdb" stroke="black" points="770.35,-2583.29 771.88,-2572.81 764.15,-2580.05 770.35,-2583.29"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge162" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M880.1,-2948.96C869.26,-2883.5 850.53,-2729.68 888,-2608.77 888.76,-2606.31 938.45,-2534.1 940,-2532.05"/>
<path fill="none" stroke="black" d="M940,-2530.05C970.7,-2473.82 951.54,-2450.76 964,-2387.92 978.78,-2313.36 977.67,-2293.18 1002,-2221.17 1015.04,-2182.59 1019.96,-2173.25 1040,-2137.79 1088.71,-2051.62 1095.19,-2023.31 1166,-1954.14 1200.16,-1920.77 1235.03,-1938.35 1256,-1895.45"/>
<path fill="none" stroke="black" d="M1256,-1893.45C1266.18,-1870.55 1285.96,-1852.19 1306.22,-1838.33"/>
<polygon fill="#05dbdb" stroke="black" points="1308.09,-1841.29 1314.58,-1832.91 1304.28,-1835.42 1308.09,-1841.29"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge163" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M927.5,-2963.27C956.73,-2950.46 996.48,-2932.06 1030,-2913.05 1087.78,-2880.28 1095,-2860.13 1154,-2829.61 1178.8,-2816.79 1597.36,-2659.87 1624,-2651.49"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge164" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M907.48,-2952.09C971.04,-2869.1 1167.19,-2625.92 1382,-2489.33 1564.75,-2373.13 1828.4,-2341.07 1943.99,-2332.38"/>
<polygon fill="#05dbdb" stroke="black" points="1944.13,-2335.88 1953.86,-2331.68 1943.63,-2328.9 1944.13,-2335.88"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_app -->
<g id="edge165" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M940,-2530.05C957.55,-2501.21 1159.62,-2361.94 1168,-2329.23"/>
<path fill="none" stroke="black" d="M1168,-2327.23C1174.15,-2291.59 1147.12,-2275.39 1168,-2245.86"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_api_builder -->
<g id="edge166" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M572,-2530.05C565.85,-2464.77 648.04,-2495.78 698,-2453.33 743.25,-2414.88 753.85,-2402.48 785,-2351.92 894.58,-2174.07 911.6,-2119.83 966,-1918.14 1020.68,-1715.41 988.56,-1656.02 1028,-1449.79"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze -->
<g id="edge167" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_core_freeze</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M849.11,-2959.44C841.9,-2955.71 834.31,-2952.06 827,-2949.05 774.71,-2927.56 747.85,-2949.93 705,-2913.05 689.86,-2900.03 698.7,-2887.75 686,-2872.33"/>
<path fill="none" stroke="black" d="M686,-2870.33C635.57,-2809.1 584.48,-2610.39 572,-2532.05"/>
<path fill="none" stroke="black" d="M572,-2530.05C561.16,-2490.82 542.6,-2488.43 522,-2453.33 444.24,-2320.86 360.35,-2290 381,-2137.79 426.64,-1801.45 418.23,-1691.79 603,-1407.07 646,-1340.81 662.98,-1327.56 722,-1275.07 755.38,-1245.38 777.25,-1252.72 804,-1216.95"/>
<path fill="none" stroke="black" d="M804,-1214.95C970.2,-967.8 998.31,-891.37 1084,-606.13 1092.95,-576.35 1097.83,-541.2 1100.4,-516.02"/>
<polygon fill="#05dbdb" stroke="black" points="1103.85,-516.67 1101.31,-506.38 1096.88,-516.01 1103.85,-516.67"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge168" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M849.57,-2959.25C842.25,-2955.49 834.51,-2951.88 827,-2949.05 760.83,-2924.1 731.22,-2951.71 672,-2913.05 518.83,-2813.07 190,-2428.77 190,-2245.86 190,-2245.86 190,-2245.86 190,-1985.84 190,-1825.27 206.6,-1388.75 320,-1275.07 380.41,-1214.51 415.16,-1226.01 494,-1192.82 536.38,-1174.98 556.73,-1187.48 591,-1156.82 645.49,-1108.09 707.56,-929.03 739,-863.04 799.77,-735.47 867.25,-581.33 895.9,-515.25"/>
<polygon fill="#05dbdb" stroke="black" points="898.96,-517.01 899.72,-506.44 892.53,-514.23 898.96,-517.01"/>
</g>
<!-- plugginger_config_models&#45;&gt;plugginger_config -->
<g id="edge169" class="edge">
<title>plugginger_config_models&#45;&gt;plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M856.85,-2955.06C838,-2938.44 813.48,-2916.83 794.23,-2899.86"/>
<polygon fill="#05dbdb" stroke="black" points="796.75,-2897.42 786.93,-2893.43 792.12,-2902.67 796.75,-2897.42"/>
</g>
<!-- plugginger_config_typed_config -->
<g id="node53" class="node">
<title>plugginger_config_typed_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2db4b4" stroke="black" cx="763" cy="-2981.76" rx="55.33" ry="32.7"/>
<text text-anchor="middle" x="763" y="-2991.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="763" y="-2978.63" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config.</text>
<text text-anchor="middle" x="763" y="-2965.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">typed_config</text>
</g>
<!-- plugginger_config_typed_config&#45;&gt;plugginger_api_builder -->
<g id="edge170" class="edge">
<title>plugginger_config_typed_config&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M725.52,-2957.48C708.04,-2945.52 687.69,-2929.93 672,-2913.05 629.16,-2866.97 624.08,-2849.92 596,-2793.61 535.18,-2671.65 489.2,-2606.97 558,-2489.33 574.48,-2461.16 595.69,-2472.64 622,-2453.33 648.54,-2433.85 710.8,-2378.67 730,-2351.92 806.33,-2245.57 952,-1937.17 952,-1806.26 952,-1806.26 952,-1806.26 952,-1709.26 952,-1592.71 1012.76,-1564.1 990,-1449.79"/>
</g>
<!-- plugginger_config_typed_config&#45;&gt;plugginger_config -->
<g id="edge171" class="edge">
<title>plugginger_config_typed_config&#45;&gt;plugginger_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M763,-2948.81C763,-2935.57 763,-2920.19 763,-2906.71"/>
<polygon fill="#2db4b4" stroke="black" points="766.5,-2906.96 763,-2896.97 759.5,-2906.97 766.5,-2906.96"/>
</g>
<!-- plugginger_core -->
<g id="node54" class="node">
<title>plugginger_core</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f9f9" stroke="black" cx="2544" cy="-3184.57" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="2544" y="-3187.82" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2544" y="-3175.07" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge172" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2501.43,-3172.26C2452.97,-3159.25 2371.51,-3136.95 2302,-3115.87 2212.5,-3088.73 2193.88,-3067.92 2102,-3050.46 1947.16,-3021.03 1518.33,-3082.14 1396,-2982.76"/>
<path fill="none" stroke="black" d="M1396,-2980.76C1380.37,-2968.06 1310.59,-2843.5 1296,-2829.61 1164.39,-2704.35 970.81,-2608.56 866.68,-2562.42"/>
<polygon fill="#10f9f9" stroke="black" points="868.22,-2559.27 857.66,-2558.45 865.4,-2565.68 868.22,-2559.27"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge173" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2543.46,-3160.54C2541.49,-3130.86 2533.53,-3079.59 2503,-3050.46 2459.34,-3008.8 2287.67,-3019.76 2240,-2982.76"/>
<path fill="none" stroke="black" d="M2240,-2980.76C2225.66,-2968.6 2224.68,-2962.93 2212,-2949.05 2197.16,-2932.8 2190.01,-2931.5 2178,-2913.05 2156.06,-2879.35 2170.22,-2859.21 2143,-2829.61 2119.59,-2804.15 2103.99,-2810.85 2074,-2793.61 2000.67,-2751.49 1965.74,-2758.31 1913,-2692.21 1877.91,-2648.22 1880.47,-2628.24 1871,-2572.77 1864.76,-2536.21 1867.7,-2526.27 1871,-2489.33 1872.72,-2470.13 1983.19,-1822.23 1994,-1806.26"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge174" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2506,-2870.33C2513.42,-2771.89 2548.63,-2750.1 2544,-2651.49"/>
<path fill="none" stroke="black" d="M2544,-2649.49C2510.67,-2421.88 2220,-2475.89 2220,-2245.86 2220,-2245.86 2220,-2245.86 2220,-2077.67 2220,-2022.12 2229.08,-2008.79 2239,-1954.14 2251,-1888.07 2293.76,-1869.07 2270,-1806.26"/>
<path fill="none" stroke="black" d="M2270,-1804.26C2262.67,-1786.29 2253.77,-1766.75 2245.98,-1750.23"/>
<polygon fill="#10f9f9" stroke="black" points="2249.16,-1748.76 2241.7,-1741.23 2242.84,-1751.77 2249.16,-1748.76"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge175" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1548,-2759.91C1541.66,-2576.3 1475.46,-2099.42 1363,-1954.14 1329.8,-1911.25 1233.83,-1944.95 1256,-1895.45"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_graph -->
<g id="edge176" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2316,-3082.16C2271.32,-3068.84 2261.15,-3062.09 2216,-3050.46 2137,-3030.1 2111.55,-3045.24 2036,-3014.46 1959.42,-2983.26 1938.87,-2971.12 1880,-2913.05 1666.33,-2702.31 1646.9,-2516.78 1776,-2245.86"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_proxy -->
<g id="edge177" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2506.03,-3169.15C2472.13,-3156.02 2421.29,-3135.72 2378,-3115.87 2349.87,-3102.97 2345.66,-3093.01 2316,-3084.16"/>
<path fill="none" stroke="black" d="M2316,-3082.16C2271.32,-3068.84 2261.87,-3058.8 2216,-3050.46 2141.01,-3036.83 1583.46,-3069.75 1531,-3014.46 1510.99,-2993.37 1529.68,-2978.09 1531,-2949.05 1534.78,-2865.62 1548.68,-2845.42 1548,-2761.91"/>
<path fill="none" stroke="black" d="M1548,-2759.91C1529.92,-2616.79 1330.79,-2301.98 1294,-2162.48"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge178" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2593.57,-3182.5C2780.13,-3178.17 3433.59,-3159.94 3515,-3115.87 3530.54,-3107.45 3538.3,-3101.82 3539,-3084.16"/>
<path fill="none" stroke="black" d="M3539,-3082.16C3543.54,-2966.95 2595.9,-3054.96 2506,-2982.76"/>
<path fill="none" stroke="black" d="M2506,-2980.76C2468.88,-2950.95 2459.14,-2944.04 2423,-2913.05 2311.72,-2817.62 2258.85,-2814.49 2178,-2692.21 2167.45,-2676.24 2169.21,-2669.9 2164,-2651.49"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge179" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2506,-2980.76C2433.58,-2923.54 2443.96,-2881.57 2416,-2793.61 2396.59,-2732.56 2428.94,-2703.82 2392,-2651.49"/>
<path fill="none" stroke="black" d="M2392,-2649.49C2327.19,-2587.88 2285.21,-2614.26 2206,-2572.77 2200.94,-2570.12 2195.76,-2567.18 2190.66,-2564.13"/>
<polygon fill="#10f9f9" stroke="black" points="2192.62,-2561.23 2182.27,-2558.97 2188.95,-2567.19 2192.62,-2561.23"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge180" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2578.78,-3167.56C2598.91,-3156.24 2622.47,-3138.96 2634,-3115.87 2646.98,-3089.86 2651.85,-3073.4 2634,-3050.46 2594.02,-2999.08 2551.49,-3040.91 2492,-3014.46 2417.79,-2981.47 2403.18,-2964.07 2340,-2913.05 2298.57,-2879.59 2294.47,-2864.23 2254,-2829.61 2215.96,-2797.08 2206.11,-2788.97 2164,-2761.91"/>
<path fill="none" stroke="black" d="M2164,-2759.91C2126.54,-2736.38 2085.51,-2707.75 2054.93,-2685.81"/>
<polygon fill="#10f9f9" stroke="black" points="2057.18,-2683.12 2047.02,-2680.12 2053.09,-2688.8 2057.18,-2683.12"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge181" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2240,-2980.76C2186.88,-2931.26 2222.38,-2887.08 2178,-2829.61 2147.41,-2790 2129.88,-2789.33 2088,-2761.91"/>
<path fill="none" stroke="black" d="M2088,-2759.91C2031.14,-2719.91 1989.63,-2747.12 1947,-2692.21 1920.36,-2657.9 1914.15,-2607.3 1913.45,-2572.2"/>
<polygon fill="#10f9f9" stroke="black" points="1916.95,-2572.43 1913.41,-2562.45 1909.95,-2572.46 1916.95,-2572.43"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge182" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2506,-2980.76C2468.91,-2950 2506,-2920.52 2506,-2872.33"/>
<path fill="none" stroke="black" d="M2506,-2870.33C2506,-2808.1 2445.57,-2656.8 2406,-2608.77 2381.02,-2578.45 2126.51,-2455.14 2106,-2421.63"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge183" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2506.88,-3168.45C2478.28,-3156.07 2438.3,-3137.18 2406,-3115.87 2368.81,-3091.32 2370.6,-3068.84 2330,-3050.46 2225.33,-3003.07 2172.62,-3073.41 2074,-3014.46 2043.05,-2995.96 2053.75,-2972.08 2026,-2949.05 1997.55,-2925.44 1980.87,-2934.85 1951,-2913.05 1891.97,-2869.98 1870.71,-2858.96 1838,-2793.61 1707.25,-2532.38 1831.76,-2427.47 1794,-2137.79 1783.12,-2054.35 1793.07,-2025.19 1748,-1954.14 1728.36,-1923.17 1685.77,-1931.88 1690,-1895.45"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation_manifest_loader -->
<g id="edge184" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation_manifest_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2536.57,-3160.68C2525.38,-3130.16 2501.14,-3076.91 2461,-3050.46 2372.77,-2992.32 2316.64,-3068.77 2226,-3014.46 2177.69,-2985.51 2179.89,-2960.79 2150,-2913.05 2127.86,-2877.7 2139.92,-2855.24 2107,-2829.61 2051.2,-2786.16 2011.24,-2832.25 1952,-2793.61 1904.82,-2762.85 1901.79,-2742.28 1876,-2692.21 1694.98,-2340.73 1956,-2201.62 1956,-1806.26 1956,-1806.26 1956,-1806.26 1956,-1447.79 1956,-1371.16 1389.03,-1323.38 1201.46,-1309.67"/>
<polygon fill="#10f9f9" stroke="black" points="1201.92,-1306.19 1191.69,-1308.96 1201.41,-1313.17 1201.92,-1306.19"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation_name_validation -->
<g id="edge185" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation_name_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2528.59,-3161.77C2505.69,-3131.24 2459.56,-3076.57 2406,-3050.46 2302.72,-3000.12 2250.69,-3069.81 2150,-3014.46 2110.62,-2992.82 2078.41,-2952.92 2057.14,-2920.9"/>
<polygon fill="#10f9f9" stroke="black" points="2060.09,-2919.01 2051.72,-2912.51 2054.21,-2922.81 2060.09,-2919.01"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger__internal_validation_plugin_validation -->
<g id="edge186" class="edge">
<title>plugginger_core&#45;&gt;plugginger__internal_validation_plugin_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4125,-3082.16C4210.63,-2949.77 4116.01,-2882.39 4149,-2728.21 4190.46,-2534.42 4304.11,-2502.06 4288,-2304.55 4276.41,-2162.37 4236,-2130.48 4236,-1987.84 4236,-1987.84 4236,-1987.84 4236,-1893.45 4236,-1771.91 4173.55,-1741.69 4198,-1622.64"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_ai_event_matcher -->
<g id="edge187" class="edge">
<title>plugginger_core&#45;&gt;plugginger_ai_event_matcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5243,-2419.63C5225.31,-2383.56 5251.52,-2368.49 5243,-2329.23"/>
<path fill="none" stroke="black" d="M5243,-2327.23C5225.89,-2248.43 5167,-2243.12 5167,-2162.48 5167,-2162.48 5167,-2162.48 5167,-1985.84 5167,-1683.03 5205,-1608.38 5205,-1305.57 5205,-1305.57 5205,-1305.57 5205,-572.43 5205,-489.94 5204.44,-464.26 5167,-390.77"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_ai_json_validator -->
<g id="edge188" class="edge">
<title>plugginger_core&#45;&gt;plugginger_ai_json_validator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5319,-2530.05C5315.2,-2440.88 5319,-2418.48 5319,-2329.23 5319,-2329.23 5319,-2329.23 5319,-2243.86 5319,-2170.88 5267.66,-2131.52 5319,-2079.67"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_ai_llm_provider -->
<g id="edge189" class="edge">
<title>plugginger_core&#45;&gt;plugginger_ai_llm_provider</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2593.59,-3182.53C2856.53,-3176.75 4077.4,-3146.6 4125,-3084.16"/>
<path fill="none" stroke="black" d="M4125,-3082.16C4158.72,-3037.94 4078.75,-3029.75 4049,-2982.76"/>
<path fill="none" stroke="black" d="M4049,-2980.76C3993.92,-2893.76 3932.86,-2856.73 3973,-2761.91"/>
<path fill="none" stroke="black" d="M3973,-2759.91C4005.86,-2597.66 3998.9,-2547.26 3954,-2387.92 3840.14,-1983.82 3181.9,-1052.22 2791,-899.04 2678.9,-855.11 1803.99,-935.72 1708,-863.04 1663.57,-829.4 1692.66,-794.45 1670,-743.54 1634.59,-663.97 1555.47,-660.12 1571,-574.43"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_ai_plugin_generator -->
<g id="edge190" class="edge">
<title>plugginger_core&#45;&gt;plugginger_ai_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5312,-388.77C5298.24,-347.44 5320.61,-224.26 5355,-197.53"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_ai_service_matcher -->
<g id="edge191" class="edge">
<title>plugginger_core&#45;&gt;plugginger_ai_service_matcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5243,-2327.23C5130.79,-1473.38 5497.93,-1231.66 5312,-390.77"/>
<path fill="none" stroke="black" d="M5312,-388.77C5303.24,-362.46 5282.78,-339.73 5262.9,-322.67"/>
<polygon fill="#10f9f9" stroke="black" points="5265.26,-320.09 5255.32,-316.44 5260.82,-325.5 5265.26,-320.09"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_ai_wiring_analyzer -->
<g id="edge192" class="edge">
<title>plugginger_core&#45;&gt;plugginger_ai_wiring_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2494.92,-3180.02C2345.17,-3169.03 1900.47,-3135.13 1838,-3115.87 1780.33,-3098.08 1777.23,-3066.33 1719,-3050.46 1589.12,-3015.05 632.29,-3066.15 508,-3014.46 447.5,-2989.3 447.61,-2959.1 401,-2913.05 364.24,-2876.73 358.95,-2863.59 320,-2829.61 219.37,-2741.82 76.38,-2785.03 78,-2651.49"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app -->
<g id="edge193" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1396,-2980.76C1383.33,-2970.34 1205.7,-2438.03 1206,-2421.63"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_app_plugin -->
<g id="edge194" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2544,-2649.49C2536.89,-2531.82 2530.36,-2501.83 2500,-2387.92 2475.26,-2295.08 2456.41,-2275.97 2425,-2185.17 2408.9,-2138.61 2416.13,-2122.04 2391,-2079.67"/>
<path fill="none" stroke="black" d="M2391,-2077.67C2372.93,-2054.45 2347.91,-2033.16 2326.49,-2017.15"/>
<polygon fill="#10f9f9" stroke="black" points="2328.62,-2014.36 2318.48,-2011.29 2324.49,-2020.01 2328.62,-2014.36"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_background -->
<g id="edge195" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3539,-3082.16C3541.27,-3024.66 3127.51,-2995.28 2966.77,-2986.04"/>
<polygon fill="#10f9f9" stroke="black" points="2967.33,-2982.57 2957.15,-2985.49 2966.94,-2989.55 2967.33,-2982.57"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_builder -->
<g id="edge196" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2506,-2870.33C2510.11,-2832.7 2531.88,-2829.8 2543,-2793.61 2582.72,-2664.34 2560.03,-2623.84 2546,-2489.33 2533.56,-2370.1 2539.8,-2338.86 2517,-2221.17 2483.07,-2045.99 2449.75,-2008.25 2408,-1834.76 2387.85,-1751.03 2386.19,-1729.34 2370,-1644.76 2341.19,-1494.24 2291.9,-1457.97 2308,-1305.57"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_depends -->
<g id="edge197" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2316,-3082.16C2271.32,-3068.84 2261.68,-3059.79 2216,-3050.46 2052.39,-3017.04 2007.98,-3032.84 1842,-3014.46 1775.73,-3007.12 1699.87,-2997.44 1648.64,-2990.72"/>
<polygon fill="#10f9f9" stroke="black" points="1649.32,-2987.28 1638.95,-2989.45 1648.41,-2994.22 1649.32,-2987.28"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_events -->
<g id="edge198" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4198,-2419.63C4193.37,-2226.95 3856,-1815.36 3856,-1622.64 3856,-1622.64 3856,-1622.64 3856,-1543.51 3856,-1356.32 3682.15,-1379.89 3590,-1216.95"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_plugin -->
<g id="edge199" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3539,-3082.16C3552.27,-2745.77 3436.2,-2658.59 3236,-2387.92 3221.38,-2368.16 3209.94,-2370.64 3194,-2351.92 3126.78,-2273.03 3142.56,-2222.41 3058,-2162.48"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_api_service -->
<g id="edge200" class="edge">
<title>plugginger_core&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5079,-3082.16C5082.23,-3073.76 4924.12,-2462.08 4922,-2453.33 4888.57,-2315.28 4690.81,-1323.61 4597,-1216.95"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_config_models -->
<g id="edge201" class="edge">
<title>plugginger_core&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2498.16,-3175.04C2430.34,-3162.38 2299.27,-3137.75 2188,-3115.87 2044.71,-3087.69 2010.87,-3068.83 1866,-3050.46 1560.04,-3011.66 1480.41,-3039.4 1173,-3014.46 1094.3,-3008.07 1003.81,-2997.55 945.82,-2990.39"/>
<polygon fill="#10f9f9" stroke="black" points="946.52,-2986.95 936.17,-2989.19 945.66,-2993.9 946.52,-2986.95"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_config_typed_config -->
<g id="edge202" class="edge">
<title>plugginger_core&#45;&gt;plugginger_config_typed_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1852,-3082.16C1821.25,-3078.64 1819.95,-3058.27 1790,-3050.46 1582.99,-2996.49 1035.51,-3066.47 828,-3014.46 822.87,-3013.17 817.66,-3011.45 812.55,-3009.47"/>
<polygon fill="#10f9f9" stroke="black" points="814.19,-3006.37 803.62,-3005.69 811.46,-3012.81 814.19,-3006.37"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_discovery_discovery -->
<g id="edge203" class="edge">
<title>plugginger_core&#45;&gt;plugginger_discovery_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2494.93,-3180.03C2343.4,-3168.91 1891.49,-3134.34 1866,-3115.87 1853.53,-3106.83 1867.3,-3085.91 1852,-3084.16"/>
<path fill="none" stroke="black" d="M1852,-3082.16C1821.25,-3078.65 1819.98,-3058.16 1790,-3050.46 1660.58,-3017.23 711,-3066.66 588,-3014.46 528.57,-2989.24 525.4,-2962.59 484,-2913.05 314.99,-2710.79 123.79,-2683.7 152,-2421.63"/>
</g>
<!-- plugginger_implementations_container -->
<g id="node65" class="node">
<title>plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#38a8a8" stroke="black" cx="4263" cy="-2760.91" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="4263" y="-2770.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4263" y="-2757.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">implementations.</text>
<text text-anchor="middle" x="4263" y="-2745.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">container</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_container -->
<g id="edge204" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2593.7,-3183.05C2889.48,-3179.8 4408.14,-3160.95 4495,-3115.87 4510.69,-3107.73 4511.8,-3100.3 4519,-3084.16"/>
<path fill="none" stroke="black" d="M4519,-3082.16C4533.33,-3050.06 4373.09,-2876.79 4299.18,-2799.35"/>
<polygon fill="#10f9f9" stroke="black" points="4301.95,-2797.18 4292.5,-2792.37 4296.89,-2802.02 4301.95,-2797.18"/>
</g>
<!-- plugginger_implementations_events -->
<g id="node66" class="node">
<title>plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#309191" stroke="black" cx="4613" cy="-2420.63" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="4613" y="-2430.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="4613" y="-2417.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">implementations.</text>
<text text-anchor="middle" x="4613" y="-2404.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_events -->
<g id="edge205" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4861,-2980.76C4805.03,-2927.25 4785.58,-2718.62 4747,-2651.49"/>
</g>
<!-- plugginger_implementations_services -->
<g id="node67" class="node">
<title>plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#339999" stroke="black" cx="4450" cy="-2420.63" rx="72.3" ry="32.7"/>
<text text-anchor="middle" x="4450" y="-2430.25" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">plugginger.</text>
<text text-anchor="middle" x="4450" y="-2417.5" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">implementations.</text>
<text text-anchor="middle" x="4450" y="-2404.75" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_implementations_services -->
<g id="edge206" class="edge">
<title>plugginger_core&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2593.79,-3182.82C2941.8,-3177.53 4998.2,-3145.07 5055,-3115.87 5070.72,-3107.79 5073.43,-3100.94 5079,-3084.16"/>
<path fill="none" stroke="black" d="M5079,-3082.16C5084.57,-3065.39 5070.09,-3059.65 5055,-3050.46 4974.88,-3001.65 4934.18,-3049.12 4847,-3014.46 4689.46,-2951.84 4648.41,-2920.52 4536,-2793.61 4513.71,-2768.45 4516.62,-2756.2 4498,-2728.21 4474.33,-2692.61 4439.38,-2694.23 4440,-2651.49"/>
</g>
<!-- plugginger_interfaces_events -->
<g id="node69" class="node">
<title>plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2fbcbc" stroke="black" cx="4594" cy="-2760.91" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="4594" y="-2770.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4594" y="-2757.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces.</text>
<text text-anchor="middle" x="4594" y="-2745.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">events</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_events -->
<g id="edge207" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5079,-3082.16C5084.57,-3065.39 5069.29,-3060.86 5055,-3050.46 4981.17,-2996.71 4936.25,-3034.5 4861,-2982.76"/>
<path fill="none" stroke="black" d="M4861,-2980.76C4846.44,-2970.74 4849.44,-2961.6 4837,-2949.05 4774.12,-2885.65 4689.35,-2825.01 4638.35,-2790.72"/>
<polygon fill="#10f9f9" stroke="black" points="4640.44,-2787.91 4630.18,-2785.26 4636.56,-2793.73 4640.44,-2787.91"/>
</g>
<!-- plugginger_interfaces_services -->
<g id="node70" class="node">
<title>plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#2fbcbc" stroke="black" cx="4440" cy="-2760.91" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="4440" y="-2770.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4440" y="-2757.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces.</text>
<text text-anchor="middle" x="4440" y="-2745.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_interfaces_services -->
<g id="edge208" class="edge">
<title>plugginger_core&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4519,-3082.16C4573.2,-3006.27 4505.98,-2964.68 4519,-2872.33"/>
</g>
<!-- plugginger_manifest_converter -->
<g id="node75" class="node">
<title>plugginger_manifest_converter</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="658,-1644.76 588,-1644.76 588,-1598.51 658,-1598.51 658,-1644.76"/>
<text text-anchor="middle" x="623" y="-1631.26" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="623" y="-1618.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">manifest.</text>
<text text-anchor="middle" x="623" y="-1605.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">converter</text>
</g>
<!-- plugginger_core&#45;&gt;plugginger_manifest_converter -->
<g id="edge209" class="edge">
<title>plugginger_core&#45;&gt;plugginger_manifest_converter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1852,-3082.16C1821.25,-3078.64 1819.97,-3058.2 1790,-3050.46 1555.14,-2989.78 928.63,-3092.66 699,-3014.46 625.93,-2989.58 611.01,-2969.16 558,-2913.05 424.07,-2771.29 385.76,-2675.45 444,-2489.33 493.13,-2332.31 580.28,-2331.75 655,-2185.17 697.2,-2102.4 699.85,-2077.55 724,-1987.84"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="edge210" class="edge">
<title>plugginger_core&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4049,-2980.76C3983.85,-2876.81 4190.55,-2574.12 4212,-2453.33 4223.67,-2387.64 4209.86,-2368.83 4192,-2304.55 4051.54,-1798.88 4154.84,-1573.89 3794,-1192.82 3691.53,-1084.61 3640.78,-1085.1 3507,-1019.41 3467.21,-999.88 3448.08,-1011.75 3414,-983.41 3404.89,-975.84 3408.43,-969.05 3400,-960.73"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin -->
<g id="edge211" class="edge">
<title>plugginger_core&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3973,-2759.91C3989.42,-2726.59 4000.98,-2722.83 4022,-2692.21 4032.15,-2677.42 4199.1,-2439.52 4198,-2421.63"/>
<path fill="none" stroke="black" d="M4198,-2419.63C4196.54,-2389.43 4075.11,-2211.35 4060,-2185.17 3910.23,-1925.7 3894.51,-1847.57 3728,-1598.51 3702.03,-1559.67 3241.43,-970.6 3210,-936.04 3194.35,-918.83 3192.62,-910.81 3172,-900.04"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="edge212" class="edge">
<title>plugginger_core&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5079,-3082.16C5115.7,-2993.82 5123.24,-2967.93 5127,-2872.33"/>
<path fill="none" stroke="black" d="M5127,-2870.33C5112.24,-2730.04 4959.11,-2405.42 4925,-2268.55 4884.35,-2105.44 4863,-2063.54 4863,-1895.45 4863,-1895.45 4863,-1895.45 4863,-1620.64 4863,-1390.86 4673,-1369.6 4673,-1139.82 4673,-1139.82 4673,-1139.82 4673,-1051.12 4673,-829.59 4414.91,-945.05 4198,-900.04"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_plugins_core_loader -->
<g id="edge213" class="edge">
<title>plugginger_core&#45;&gt;plugginger_plugins_core_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2506,-2980.76C2497.61,-2973.8 2562.71,-2804.27 2565,-2793.61 2582.29,-2713.12 2581.04,-2691 2577,-2608.77 2570.33,-2473.07 2563.35,-2439.42 2547,-2304.55 2537.95,-2229.87 2533.82,-2211.34 2518,-2137.79 2489.63,-2005.86 2384,-1680.46 2384,-1545.51 2384,-1545.51 2384,-1545.51 2384,-1214.95 2384,-1066.1 2584.95,-1020.85 2498,-900.04"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin -->
<g id="edge214" class="edge">
<title>plugginger_core&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5127,-2870.33C5127.54,-2670.91 5165.66,-2617.27 5127,-2421.63"/>
<path fill="none" stroke="black" d="M5127,-2419.63C5092.97,-2291.77 4977,-2294.79 4977,-2162.48 4977,-2162.48 4977,-2162.48 4977,-1985.84 4977,-1916.66 4901.22,-1437.32 4885,-1370.07 4857.59,-1256.43 4842.8,-1229.99 4801,-1120.82 4762.8,-1021.06 4799.8,-951.15 4706,-900.04"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin -->
<g id="edge215" class="edge">
<title>plugginger_core&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5127,-2419.63C5102.93,-2304.48 5053,-2013.09 5053,-1895.45 5053,-1895.45 5053,-1895.45 5053,-1709.26 5053,-1337.69 4982.79,-1244.31 4843,-900.04"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_plugins_internal_loader -->
<g id="edge216" class="edge">
<title>plugginger_core&#45;&gt;plugginger_plugins_internal_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5079,-3082.16C5085.18,-3068.05 5084.12,-3063.05 5093,-3050.46 5106.41,-3031.45 5116.98,-3033.02 5131,-3014.46 5229.46,-2884.14 5320.2,-2555.82 5319,-2532.05"/>
<path fill="none" stroke="black" d="M5319,-2530.05C5316.5,-2471.25 5271.15,-2473.3 5243,-2421.63"/>
<path fill="none" stroke="black" d="M5243,-2419.63C5183.2,-2309.84 5129,-2287.5 5129,-2162.48 5129,-2162.48 5129,-2162.48 5129,-1303.57 5129,-1161.79 5127.23,-762.47 5015,-675.83"/>
</g>
<!-- plugginger_core&#45;&gt;plugginger_stubgen -->
<g id="edge217" class="edge">
<title>plugginger_core&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2593.69,-3183.76C2771.22,-3183.9 3373.12,-3179.56 3553,-3115.87 3649.85,-3081.58 3742,-3085.5 3742,-2982.76 3742,-2982.76 3742,-2982.76 3742,-2870.33 3742,-2677.62 3413.27,-2453.29 3346,-2351.92 3246.83,-2202.49 3259.58,-2144.35 3172,-1987.84"/>
</g>
<!-- plugginger_core_config -->
<g id="node55" class="node">
<title>plugginger_core_config</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#22e7e7" stroke="black" cx="1243" cy="-3184.57" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1243" y="-3194.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1243" y="-3181.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1243" y="-3168.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">config</text>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge218" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1271.89,-3158C1322.71,-3113.48 1432.12,-3019.73 1531,-2949.05 1680.1,-2842.48 1867.09,-2732.16 1956.18,-2680.93"/>
<polygon fill="#22e7e7" stroke="black" points="1957.79,-2684.04 1964.72,-2676.02 1954.3,-2677.96 1957.79,-2684.04"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_api_app -->
<g id="edge219" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1244.33,-3151.44C1244.82,-3103.75 1239.95,-3012.99 1201,-2949.05 1187.31,-2926.58 1169.38,-2934.4 1154,-2913.05 1093.32,-2828.83 1095.12,-2794.59 1078,-2692.21 1063.13,-2603.27 1055.16,-2576.56 1078,-2489.33 1098.67,-2410.36 1154.12,-2409.67 1168,-2329.23"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_config_models -->
<g id="edge220" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1205.81,-3162.65C1140.39,-3125.85 1004.91,-3049.64 933.08,-3009.24"/>
<polygon fill="#22e7e7" stroke="black" points="935.13,-3006.38 924.7,-3004.52 931.7,-3012.48 935.13,-3006.38"/>
</g>
<!-- plugginger_core_config&#45;&gt;plugginger_implementations_events -->
<g id="edge221" class="edge">
<title>plugginger_core_config&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1291.52,-3176.77C1471.37,-3151.67 2100.08,-3064.93 2302,-3050.46 2744.49,-3018.75 3860.52,-3088.03 4298,-3014.46 4441.37,-2990.35 4502.46,-3011.98 4609,-2913.05 4662.21,-2863.65 4659.02,-2833.52 4671,-2761.91"/>
</g>
<!-- plugginger_core_constants -->
<g id="node56" class="node">
<title>plugginger_core_constants</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f9f9" stroke="black" cx="3253" cy="-2871.33" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="3253" y="-2880.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="3253" y="-2868.21" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="3253" y="-2855.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">constants</text>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge222" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3232.12,-2841.25C3211.01,-2812.16 3177.13,-2766.38 3146,-2728.21 3005.05,-2555.37 2915.35,-2550.59 2814,-2351.92 2769.68,-2265.04 2811.79,-2223.91 2766,-2137.79 2755.67,-2118.36 2745.43,-2119.23 2732,-2101.79 2673.39,-2025.67 2666.63,-2000.99 2618,-1918.14 2603.61,-1893.62 2578.53,-1823.59 2556,-1806.26"/>
<path fill="none" stroke="black" d="M2556,-1804.26C2475.01,-1749.38 2361.93,-1726.62 2291.65,-1717.36"/>
<polygon fill="#10f9f9" stroke="black" points="2292.18,-1713.9 2281.83,-1716.13 2291.31,-1720.85 2292.18,-1713.9"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge223" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3208.54,-2856.33C3082.78,-2816.79 2713.7,-2701.15 2406,-2608.77 2335.4,-2587.57 2253.9,-2563.92 2200.56,-2548.56"/>
<polygon fill="#10f9f9" stroke="black" points="2201.66,-2545.23 2191.08,-2545.83 2199.72,-2551.96 2201.66,-2545.23"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_builder -->
<g id="edge224" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3239.1,-2839.49C3225.81,-2810.46 3204.92,-2765.94 3185,-2728.21 3165.94,-2692.1 2460,-1490.62 2460,-1449.79 2460,-1449.79 2460,-1449.79 2460,-1214.95 2460,-1144.97 2513.64,-944.98 2460,-900.04"/>
<path fill="none" stroke="black" d="M2460,-899.04C2341.41,-836.28 1373.65,-921.73 1253,-863.04 1240.81,-857.11 1230.55,-846.56 1222.61,-836.03"/>
<polygon fill="#10f9f9" stroke="black" points="1225.7,-834.34 1217.12,-828.13 1219.96,-838.34 1225.7,-834.34"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_events -->
<g id="edge225" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3295.16,-2853.68C3328.2,-2839.77 3374.67,-2818.29 3412,-2793.61 3469.13,-2755.86 3477.63,-2738.6 3528,-2692.21 3567.17,-2656.13 3571.12,-2640.35 3614,-2608.77 3640.9,-2588.95 3662.11,-2600.99 3680,-2572.77 3728.9,-2495.63 3704,-2253.81 3704,-2162.48 3704,-2162.48 3704,-2162.48 3704,-1893.45 3704,-1824.86 3714.89,-1808.28 3718,-1739.76 3724.76,-1591.09 3742.27,-1542.24 3680,-1407.07 3654.77,-1352.31 3607.84,-1363.16 3590,-1305.57"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_plugin -->
<g id="edge226" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3267.77,-2839.96C3286.87,-2796.05 3314.3,-2713.35 3284,-2651.49"/>
<path fill="none" stroke="black" d="M3284,-2649.49C3259.21,-2519.34 3208.48,-2501.94 3141,-2387.92 3131.33,-2371.58 3126.05,-2369.12 3118,-2351.92 3080.57,-2271.93 3129.57,-2214.23 3058,-2162.48"/>
<path fill="none" stroke="black" d="M3058,-2160.48C3012.82,-2128.47 2953.01,-2105.84 2912.22,-2092.82"/>
<polygon fill="#10f9f9" stroke="black" points="2913.29,-2089.49 2902.7,-2089.86 2911.21,-2096.17 2913.29,-2089.49"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_api_service -->
<g id="edge227" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3298.88,-2858.83C3343.26,-2846.55 3411.47,-2824.58 3465,-2793.61 3647.1,-2688.28 3693.74,-2638.27 3794,-2453.33 3921.48,-2218.17 3698.51,-2829.51 4057,-1407.07 4068.41,-1361.81 4056.79,-1343.5 4084,-1305.57"/>
<path fill="none" stroke="black" d="M4084,-1303.57C4105.92,-1257.28 4149.7,-1267.12 4160,-1216.95"/>
<path fill="none" stroke="black" d="M4160,-1214.95C4188.97,-1073.89 3937.06,-1054.7 3829.84,-1052.86"/>
<polygon fill="#10f9f9" stroke="black" points="3829.97,-1049.37 3819.93,-1052.74 3829.88,-1056.36 3829.97,-1049.37"/>
</g>
<!-- plugginger_core_constants&#45;&gt;plugginger_stubgen -->
<g id="edge228" class="edge">
<title>plugginger_core_constants&#45;&gt;plugginger_stubgen</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3284,-2649.49C3274.42,-2577.45 3272.35,-2558.8 3251,-2489.33 3231.57,-2426.13 3211.51,-2415.68 3194,-2351.92 3164.25,-2243.62 3166.02,-2213.82 3158,-2101.79 3156.53,-2081.29 3155.91,-2075.99 3158,-2055.54 3161.13,-2024.98 3185.28,-2015.55 3172,-1987.84"/>
<path fill="none" stroke="black" d="M3172,-1985.84C3155.63,-1956.59 3125.58,-1933.48 3100.3,-1917.94"/>
<polygon fill="#10f9f9" stroke="black" points="3102.21,-1915.01 3091.83,-1912.93 3098.65,-1921.04 3102.21,-1915.01"/>
</g>
<!-- plugginger_core_docstring_convention -->
<g id="node57" class="node">
<title>plugginger_core_docstring_convention</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#46a4a4" stroke="black" cx="1625" cy="-3083.16" rx="85.03" ry="32.7"/>
<text text-anchor="middle" x="1625" y="-3092.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1625" y="-3080.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1625" y="-3067.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">docstring_convention</text>
</g>
<!-- plugginger_core_error_handler -->
<g id="node58" class="node">
<title>plugginger_core_error_handler</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3ab0b0" stroke="black" cx="505" cy="-3083.16" rx="57.45" ry="32.7"/>
<text text-anchor="middle" x="505" y="-3092.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="505" y="-3080.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="505" y="-3067.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">error_handler</text>
</g>
<!-- plugginger_core_error_handler&#45;&gt;plugginger_api_builder -->
<g id="edge229" class="edge">
<title>plugginger_core_error_handler&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M496.56,-3050.36C474.09,-2960.02 418.72,-2695.09 482,-2489.33 516.87,-2375.94 568.93,-2369.02 632,-2268.55 699.97,-2160.27 719.56,-2134.25 776,-2019.54 815.2,-1939.87 826.15,-1919.72 852,-1834.76 883.18,-1732.25 863.96,-1699.41 900,-1598.51 912.17,-1564.43 919.38,-1557.53 938,-1526.51 959.2,-1491.19 997.98,-1490.2 990,-1449.79"/>
<path fill="none" stroke="black" d="M990,-1447.79C974.03,-1377.93 920.78,-1376.92 914,-1305.57"/>
</g>
<!-- plugginger_core_exceptions -->
<g id="node59" class="node">
<title>plugginger_core_exceptions</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f9f9" stroke="black" cx="2030" cy="-3184.57" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="2030" y="-3194.2" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2030" y="-3181.45" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2030" y="-3168.7" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">exceptions</text>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge230" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.09,-3183.41C1839.87,-3181.89 1446.84,-3169.91 1358,-3084.16"/>
<path fill="none" stroke="black" d="M1358,-3082.16C1292.18,-3018.63 1272.22,-3006.46 1201,-2949.05 1180.51,-2932.54 1174.79,-2929.18 1154,-2913.05 1086.12,-2860.39 1062.93,-2854.19 1002,-2793.61 933.16,-2725.17 865.45,-2634.46 826.8,-2579.83"/>
<polygon fill="#10f9f9" stroke="black" points="829.88,-2578.12 821.26,-2571.95 824.16,-2582.15 829.88,-2578.12"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator -->
<g id="edge231" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_dependency_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1206,-2759.91C1156.79,-2651.15 1562.5,-1869.03 1637,-1775.76 1664.23,-1741.67 1693.49,-1751.22 1711,-1711.26"/>
<path fill="none" stroke="black" d="M1711,-1709.26C1777.09,-1466.45 2115.53,-1557.1 2108,-1305.57"/>
<path fill="none" stroke="black" d="M2108,-1303.57C2089.61,-1269.35 2066.42,-1273.8 2049,-1239.07 2028.86,-1198.93 2065.26,-1167.64 2030,-1139.82"/>
<path fill="none" stroke="black" d="M2030,-1137.82C1954.75,-1083.42 1686.38,-1063.23 1549.53,-1056.35"/>
<polygon fill="#10f9f9" stroke="black" points="1549.97,-1052.87 1539.81,-1055.87 1549.63,-1059.86 1549.97,-1052.87"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_interface_registrar -->
<g id="edge232" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_interface_registrar</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1512,-3082.16C1507.72,-3066.3 1518.08,-3060.61 1531,-3050.46 1573.45,-3017.11 1611.17,-3054.84 1647,-3014.46 1834.89,-2802.68 1627.03,-2636.79 1762,-2387.92 1824.46,-2272.75 1910.02,-2298.36 1976,-2185.17 2063.38,-2035.27 1977.86,-1953.28 2070,-1806.26"/>
<path fill="none" stroke="black" d="M2070,-1804.26C2081.45,-1785.95 2125.22,-1760.77 2163.65,-1741.23"/>
<polygon fill="#10f9f9" stroke="black" points="2165.08,-1744.43 2172.44,-1736.81 2161.94,-1738.17 2165.08,-1744.43"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge233" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1358,-3082.16C1327.05,-3050.63 1372.85,-3024.37 1358,-2982.76"/>
<path fill="none" stroke="black" d="M1358,-2980.76C1325.32,-2889.19 1239.46,-2938.26 1168,-2872.33"/>
<path fill="none" stroke="black" d="M1168,-2870.33C1152.56,-2856.09 1155.5,-2847.2 1144,-2829.61 1133.2,-2813.09 1126.04,-2811.64 1118,-2793.61 1099.21,-2751.5 1101.88,-2737.95 1096,-2692.21 1064.47,-2446.69 1002.32,-2337.18 1149,-2137.79 1181.07,-2094.2 1237.04,-2130.36 1256,-2079.67"/>
<path fill="none" stroke="black" d="M1256,-2077.67C1280.37,-2000.43 1223.09,-1969.45 1256,-1895.45"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_graph -->
<g id="edge234" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_graph</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1358,-2980.76C1304.78,-2829.48 1494.39,-2415.68 1610,-2304.55 1666.42,-2250.32 1730.61,-2309.6 1776,-2245.86"/>
<path fill="none" stroke="black" d="M1776,-2243.86C1803.16,-2205.24 1792.7,-2179.19 1770,-2137.79 1756.83,-2113.78 1735.51,-2124.36 1720,-2101.79 1705.74,-2081.05 1698.26,-2053.92 1694.33,-2031.33"/>
<polygon fill="#10f9f9" stroke="black" points="1697.8,-2030.84 1692.82,-2021.49 1690.88,-2031.91 1697.8,-2030.84"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy -->
<g id="edge235" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_proxy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1358,-2980.76C1344.29,-2942.33 1316.85,-2948.13 1296,-2913.05 1286.22,-2896.6 1289.22,-2890.06 1282,-2872.33"/>
<path fill="none" stroke="black" d="M1282,-2870.33C1259.81,-2815.83 1232.5,-2814.45 1206,-2761.91"/>
<path fill="none" stroke="black" d="M1206,-2759.91C1150.85,-2650.57 1091.49,-2594.63 1154,-2489.33 1171.06,-2460.6 1199.51,-2479.72 1220,-2453.33 1301.81,-2347.98 1310.1,-2294.89 1294,-2162.48"/>
<path fill="none" stroke="black" d="M1294,-2160.48C1284.82,-2125.69 1291.79,-2114.92 1299,-2079.67"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge236" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1856,-2980.76C1861.11,-2908.33 1873.1,-2886.67 1918,-2829.61 1959.74,-2776.57 2184.34,-2715.84 2164,-2651.49"/>
<path fill="none" stroke="black" d="M2164,-2649.49C2158.28,-2629.27 2154.5,-2623.97 2140,-2608.77 2119.83,-2587.63 2108.02,-2590.75 2085,-2572.77 2083.07,-2571.26 2081.12,-2569.68 2079.17,-2568.06"/>
<polygon fill="#10f9f9" stroke="black" points="2081.72,-2565.64 2071.86,-2561.76 2077.15,-2570.94 2081.72,-2565.64"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge237" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.45,-3181.35C1935.01,-3176.36 1870.06,-3161.16 1838,-3115.87 1803.51,-3067.14 1854.17,-3042.43 1856,-2982.76"/>
<path fill="none" stroke="black" d="M1856,-2980.76C1852.66,-2913.38 1808.34,-2888.08 1842,-2829.61 1857.9,-2802 1880.31,-2814.92 1904,-2793.61 1934.53,-2766.15 1961.65,-2728.38 1980.54,-2698.68"/>
<polygon fill="#10f9f9" stroke="black" points="1983.49,-2700.56 1985.82,-2690.23 1977.55,-2696.85 1983.49,-2700.56"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge238" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.17,-3182.96C1930.93,-3179.36 1857.92,-3165.34 1822,-3115.87 1697.81,-2944.85 1833.6,-2671.2 1891.46,-2570.69"/>
<polygon fill="#10f9f9" stroke="black" points="1894.36,-2572.67 1896.38,-2562.27 1888.32,-2569.14 1894.36,-2572.67"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_dependency_validation -->
<g id="edge239" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_dependency_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1282,-2870.33C1217.89,-2712.85 1427.06,-2289.93 1503,-2137.79 1539.91,-2063.84 1563.6,-2054.11 1613,-1987.84"/>
<path fill="none" stroke="black" d="M1613,-1985.84C1619.75,-1970.86 1621.93,-1967.12 1632,-1954.14 1654.48,-1925.16 1685.77,-1931.88 1690,-1895.45"/>
<path fill="none" stroke="black" d="M1690,-1893.45C1691.83,-1877.67 1695.54,-1860.63 1699.33,-1845.83"/>
<polygon fill="#10f9f9" stroke="black" points="1702.63,-1847.03 1701.83,-1836.47 1695.87,-1845.23 1702.63,-1847.03"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_manifest_loader -->
<g id="edge240" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_manifest_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1358,-3082.16C1341.85,-3065.71 1630.84,-2319.94 1648,-2304.55 1696.47,-2261.08 1748.8,-2318.96 1790,-2268.55 1852.76,-2191.75 1917.23,-2012.07 1785,-1775.76 1623.59,-1487.3 1148.49,-1701.39 1161,-1371.07"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_name_validation -->
<g id="edge241" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_name_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2012,-2980.76C2009.4,-2962.17 2011.32,-2941.84 2014.66,-2923.85"/>
<polygon fill="#10f9f9" stroke="black" points="2018.06,-2924.69 2016.66,-2914.19 2011.21,-2923.27 2018.06,-2924.69"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_plugin_validation -->
<g id="edge242" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger__internal_validation_plugin_validation</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2079.38,-3179.66C2334.93,-3159.25 3501.3,-3064.03 3655,-3014.46 3741.29,-2986.63 3774.65,-2983.27 3832,-2913.05 3885.61,-2847.42 4039.57,-2267.42 4060,-2185.17 4091.75,-2057.37 4122,-2027.13 4122,-1895.45 4122,-1895.45 4122,-1895.45 4122,-1804.26 4122,-1716.76 4180.21,-1708.31 4198,-1622.64"/>
<path fill="none" stroke="black" d="M4198,-1620.64C4203.22,-1577.71 4185.29,-1531.69 4167.55,-1498.24"/>
<polygon fill="#10f9f9" stroke="black" points="4170.69,-1496.68 4162.8,-1489.6 4164.55,-1500.05 4170.69,-1496.68"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_ai_json_validator -->
<g id="edge243" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_ai_json_validator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5041,-3082.16C5068.68,-3062.28 5136.3,-2826.57 5145,-2793.61 5201.27,-2580.37 5170.54,-2517.2 5229,-2304.55 5257.54,-2200.74 5240.3,-2153.12 5319,-2079.67"/>
<path fill="none" stroke="black" d="M5319,-2077.67C5376.44,-2018.62 5445.32,-2085.26 5495,-2019.54 5548.24,-1949.12 5512,-1710.92 5512,-1622.64 5512,-1622.64 5512,-1622.64 5512,-1447.79 5512,-989.71 5430.36,-879.96 5407,-422.47 5403.22,-348.4 5401.44,-329.08 5412,-255.66 5413.16,-247.56 5414.94,-238.93 5416.84,-230.88"/>
<polygon fill="#10f9f9" stroke="black" points="5420.17,-232 5419.19,-221.45 5413.38,-230.3 5420.17,-232"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_ai_llm_provider -->
<g id="edge244" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_ai_llm_provider</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2074.75,-3170.12C2111.8,-3158.18 2165.26,-3139.05 2209,-3115.87 2253.65,-3092.21 2254.22,-3066.91 2302,-3050.46 2452.21,-2998.73 2860.01,-3044.58 3016,-3014.46 3050.03,-3007.89 3290.87,-2941.26 3311,-2913.05 3397.74,-2791.51 3272.5,-2270.88 3231,-2055.54 3214.81,-1971.53 3208.65,-1950.25 3177,-1870.76 3053.54,-1560.68 3087.08,-1431.71 2854,-1192.82 2829.29,-1167.5 2804.14,-1185.19 2783,-1156.82 2711.71,-1061.14 2816.87,-947.87 2708,-899.04 2647.45,-871.88 1560.21,-910.67 1514,-863.04 1422.95,-769.2 1561.12,-704.8 1571,-574.43"/>
<path fill="none" stroke="black" d="M1571,-572.43C1568.41,-473.33 1551.48,-418.85 1629,-357.06 1942.38,-107.3 4841.09,-303.31 5233,-219.66 5234.64,-219.31 5236.29,-218.91 5237.94,-218.48"/>
<polygon fill="#10f9f9" stroke="black" points="5238.86,-221.86 5247.41,-215.6 5236.82,-215.16 5238.86,-221.86"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_ai_plugin_generator -->
<g id="edge245" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_ai_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2079.21,-3179.31C2162.76,-3172.26 2337.74,-3158.32 2486,-3151.87 2627.86,-3145.69 4931.67,-3174.77 5041,-3084.16"/>
<path fill="none" stroke="black" d="M5041,-3082.16C5115.15,-3020.72 5089,-2747.79 5089,-2651.49 5089,-2651.49 5089,-2651.49 5089,-2419.63 5089,-2352.47 5087.44,-2335.7 5087,-2268.55 5085.9,-2102.72 5091,-2061.28 5091,-1895.45 5091,-1895.45 5091,-1895.45 5091,-1303.57 5091,-1079.11 5167,-1028.75 5167,-804.29 5167,-804.29 5167,-804.29 5167,-572.43 5167,-420.16 4823.42,-311.97 4974,-289.36"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_ai_wiring_analyzer -->
<g id="edge246" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_ai_wiring_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.5,-3181.34C1862.76,-3176.05 1556.68,-3162.39 1301,-3151.87 1198.01,-3147.63 464.07,-3165.99 374,-3115.87 361.99,-3109.18 94.12,-2696.53 92,-2692.21 83.57,-2675.03 81.57,-2670.29 78,-2651.49"/>
<path fill="none" stroke="black" d="M78,-2649.49C48.78,-2607.56 0,-2425.22 0,-2421.63 0,-2421.63 0,-2421.63 0,-1893.45 0,-1477.53 76,-1376.64 76,-960.73 76,-960.73 76,-960.73 76,-899.04 76,-675.64 124.98,-570.96 318,-458.47 506.81,-348.44 601.25,-469.75 805,-390.77"/>
<path fill="none" stroke="black" d="M805,-388.77C823.26,-381.37 819.41,-367.7 836,-357.06 911.11,-308.89 941.41,-320.57 1025,-289.36"/>
<path fill="none" stroke="black" d="M1025,-287.36C1042.61,-280.78 1035.92,-263.51 1053,-255.66 1157.25,-207.75 5103.26,-280.03 5183,-197.53"/>
<path fill="none" stroke="black" d="M5183,-195.53C5194.8,-183.32 5196.56,-165.25 5194.69,-148.51"/>
<polygon fill="#10f9f9" stroke="black" points="5198.16,-148.04 5193.13,-138.72 5191.25,-149.14 5198.16,-148.04"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app -->
<g id="edge247" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1168,-2870.33C1139.58,-2840.46 1121.84,-2733.03 1116,-2692.21 1103.24,-2602.95 1075.73,-2570.01 1116,-2489.33 1138.35,-2444.54 1203.84,-2471.63 1206,-2421.63"/>
<path fill="none" stroke="black" d="M1206,-2419.63C1206.81,-2376.05 1159.77,-2372.03 1168,-2329.23"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin -->
<g id="edge248" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2028.11,-3151.46C2025.54,-3111.72 2020.29,-3042.04 2012,-2982.76"/>
<path fill="none" stroke="black" d="M2012,-2980.76C2004.27,-2941 1966.76,-2950.37 1951,-2913.05 1936.57,-2878.89 1928.02,-2858.72 1951,-2829.61 1993.76,-2775.47 2044.32,-2831.47 2102,-2793.61 2149.09,-2762.71 2155.2,-2743.71 2178,-2692.21 2199.88,-2642.77 2195.48,-2626.54 2201,-2572.77 2216.95,-2417.3 2187.31,-2376.33 2206,-2221.17 2215.03,-2146.19 2216.4,-2125.85 2244,-2055.54 2248.78,-2043.36 2255.5,-2030.75 2262.01,-2019.76"/>
<polygon fill="#10f9f9" stroke="black" points="2264.85,-2021.82 2267.08,-2011.47 2258.88,-2018.17 2264.85,-2021.82"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_background -->
<g id="edge249" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_background</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2088,-3082.16C2112.69,-3063.5 2120.27,-3059.05 2150,-3050.46 2279.27,-3013.13 2684.09,-2992.2 2840.99,-2985.32"/>
<polygon fill="#10f9f9" stroke="black" points="2840.94,-2988.83 2850.78,-2984.9 2840.64,-2981.83 2840.94,-2988.83"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_builder -->
<g id="edge250" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.3,-3181.39C1781.73,-3172.57 1051.16,-3138.84 950,-3115.87 778.41,-3076.91 703.75,-3061.95 610,-2913.05 507.51,-2750.29 467.13,-2673.68 522,-2489.33 538.54,-2433.76 630.22,-2317.67 661,-2268.55 684.19,-2231.54 690.72,-2222.73 713,-2185.17 734.71,-2148.59 739.17,-2138.88 760,-2101.79 780.51,-2065.27 786.31,-2056.51 806,-2019.54 855.94,-1925.76 914,-1912.51 914,-1806.26 914,-1806.26 914,-1806.26 914,-1709.26 914,-1583.3 1000.38,-1572.68 1028,-1449.79"/>
<path fill="none" stroke="black" d="M1028,-1447.79C1039.88,-1367.66 927.12,-1385.51 914,-1305.57"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_depends -->
<g id="edge251" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_depends</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.54,-3182.22C1869.48,-3178.35 1604.26,-3164.17 1531,-3115.87 1517.29,-3106.83 1516.28,-3100.02 1512,-3084.16"/>
<path fill="none" stroke="black" d="M1512,-3082.16C1504.7,-3055.12 1524.07,-3030.21 1545.2,-3012.09"/>
<polygon fill="#10f9f9" stroke="black" points="1547.21,-3014.96 1552.79,-3005.95 1542.81,-3009.52 1547.21,-3014.96"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_events -->
<g id="edge252" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2079.35,-3180.66C2158.23,-3174.87 2316.78,-3158.56 2444,-3115.87 2503.07,-3096.05 2507.84,-3066.67 2568,-3050.46 2756.77,-2999.6 3264.2,-3083.76 3447,-3014.46 3592.28,-2959.38 3627.37,-2919.82 3718,-2793.61 3778.55,-2709.3 3778.08,-2675.34 3794,-2572.77 3823.66,-2381.73 3799.58,-2331.04 3794,-2137.79 3790.89,-2029.95 3780,-2003.34 3780,-1895.45 3780,-1895.45 3780,-1895.45 3780,-1804.26 3780,-1744.96 3601.67,-1363.71 3590,-1305.57"/>
<path fill="none" stroke="black" d="M3590,-1303.57C3583.7,-1265.59 3606.2,-1251.87 3590,-1216.95"/>
<path fill="none" stroke="black" d="M3590,-1214.95C3566.31,-1163.86 3523.62,-1114.31 3494.33,-1083.79"/>
<polygon fill="#10f9f9" stroke="black" points="3496.99,-1081.5 3487.5,-1076.78 3491.97,-1086.39 3496.99,-1081.5"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_plugin -->
<g id="edge253" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2061.51,-3159.19C2104.51,-3127.49 2185.27,-3073.46 2264,-3050.46 2338.86,-3028.59 2901.77,-3060.12 2965,-3014.46 3193.91,-2849.17 3152.81,-2657.85 3070,-2387.92 3034.95,-2273.67 2942.94,-2162.52 2895.39,-2110.42"/>
<polygon fill="#10f9f9" stroke="black" points="2897.98,-2108.07 2888.63,-2103.09 2892.84,-2112.82 2897.98,-2108.07"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_api_service -->
<g id="edge254" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2079.24,-3179.9C2162.84,-3173.73 2337.88,-3161.04 2486,-3151.87 2780.09,-3133.65 2855.22,-3149.11 3148,-3115.87 3316.97,-3096.68 3357.35,-3078.97 3525,-3050.46 3620.95,-3034.14 3652.35,-3054.62 3741,-3014.46 3805.85,-2985.08 3821.87,-2969.7 3865,-2913.05 3899.05,-2868.34 4274,-2044.04 4274,-1987.84 4274,-1987.84 4274,-1987.84 4274,-1543.51 4274,-1389.78 4148.7,-1370.26 4160,-1216.95"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_config_typed_config -->
<g id="edge255" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_config_typed_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.14,-3183.04C1812.75,-3179.91 1257.27,-3158.59 828,-3014.46 823.32,-3012.89 818.55,-3011.05 813.82,-3009.07"/>
<polygon fill="#10f9f9" stroke="black" points="815.53,-3006 804.97,-3005.15 812.69,-3012.4 815.53,-3006"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_core_docstring_convention -->
<g id="edge256" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_core_docstring_convention</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1982.49,-3174.47C1922.05,-3162.57 1814.61,-3140.34 1724,-3115.87 1715.94,-3113.69 1707.57,-3111.27 1699.25,-3108.76"/>
<polygon fill="#10f9f9" stroke="black" points="1700.42,-3105.45 1689.83,-3105.86 1698.36,-3112.14 1700.42,-3105.45"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_core_error_handler -->
<g id="edge257" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_core_error_handler</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.52,-3181.36C1733.97,-3170.35 648.42,-3121.61 614,-3115.87 597.73,-3113.15 580.45,-3108.82 564.56,-3104.23"/>
<polygon fill="#10f9f9" stroke="black" points="565.84,-3100.96 555.26,-3101.45 563.84,-3107.67 565.84,-3100.96"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_discovery_discovery -->
<g id="edge258" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_discovery_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1980.5,-3181.39C1862.75,-3176.2 1556.67,-3162.77 1301,-3151.87 1205.23,-3147.78 523.29,-3161.52 439,-3115.87 375.09,-3081.26 382.93,-3044.13 344,-2982.76"/>
<path fill="none" stroke="black" d="M344,-2980.76C293.28,-2893.68 272.51,-2877.04 216,-2793.61 160.52,-2711.71 151.3,-2687.95 92,-2608.77 79.52,-2592.1 69.09,-2592.35 62,-2572.77 49.38,-2537.9 45.44,-2522.51 62,-2489.33 84.35,-2444.54 151.85,-2471.68 152,-2421.63"/>
<path fill="none" stroke="black" d="M152,-2419.63C151.04,-2388.91 143.35,-2382.18 138,-2351.92 97.57,-2123.15 78.93,-2066.46 62,-1834.76 60.09,-1808.61 53.27,-1800.49 62,-1775.76 74.25,-1741.04 108.12,-1747.61 114,-1711.26"/>
<path fill="none" stroke="black" d="M114,-1709.26C121.76,-1636.9 114,-1618.29 114,-1545.51 114,-1545.51 114,-1545.51 114,-899.04 114,-829.41 90.02,-798.31 133,-743.54 185.51,-676.63 291.49,-758.38 312,-675.83"/>
<path fill="none" stroke="black" d="M312,-673.83C317.2,-652.15 313.94,-627.27 309.38,-607.82"/>
<polygon fill="#10f9f9" stroke="black" points="312.8,-607.05 306.9,-598.25 306.02,-608.8 312.8,-607.05"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_container -->
<g id="edge259" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2079.23,-3179.59C2162.8,-3172.96 2337.8,-3159.61 2486,-3151.87 2942.99,-3127.99 3062.09,-3181.28 3515,-3115.87 3693.96,-3090.02 3745.08,-3090.79 3909,-3014.46 4033.85,-2956.32 4160.52,-2852.8 4223.02,-2798.1"/>
<polygon fill="#10f9f9" stroke="black" points="4225.08,-2800.95 4230.27,-2791.71 4220.45,-2795.7 4225.08,-2800.95"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_events -->
<g id="edge260" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2079.21,-3179.37C2162.77,-3172.41 2337.75,-3158.59 2486,-3151.87 3236.95,-3117.79 3426.75,-3162.89 4177,-3115.87 4312.58,-3107.37 4346.7,-3104.61 4481,-3084.16"/>
<path fill="none" stroke="black" d="M4481,-3082.16C4498.47,-3079.5 4490.09,-3059.94 4505,-3050.46 4626.94,-2972.93 4736.56,-3098.55 4823,-2982.76"/>
<path fill="none" stroke="black" d="M4823,-2980.76C4824.4,-2978.88 4747.84,-2653.68 4747,-2651.49"/>
<path fill="none" stroke="black" d="M4747,-2649.49C4721.73,-2592.69 4694.97,-2589.41 4671,-2532.05"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_implementations_services -->
<g id="edge261" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2079.22,-3179.51C2162.79,-3172.76 2337.78,-3159.25 2486,-3151.87 3017.58,-3125.4 3153.25,-3167.28 3683,-3115.87 3700.23,-3114.19 4285.23,-3026.15 4298,-3014.46 4383.5,-2936.21 4305.71,-2862.66 4363,-2761.91"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_manifest_converter -->
<g id="edge262" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_manifest_converter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M344,-2980.76C316.19,-2933.74 314.84,-2541.81 330,-2489.33 395.97,-2260.92 462.59,-2210.37 643,-2055.54 678.61,-2024.99 706.82,-2031.5 724,-1987.84"/>
<path fill="none" stroke="black" d="M724,-1985.84C746.05,-1908.95 700.69,-1885.45 712,-1806.26"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="edge263" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2079.5,-3181.48C2147.67,-3176.91 2272.81,-3162.34 2368,-3115.87 2408.05,-3096.32 2402.55,-3066.82 2444,-3050.46 2623.98,-2979.41 3128.75,-3070.37 3314,-3014.46 3389.87,-2991.56 3550.98,-2870.29 3619,-2829.61 3646.02,-2813.46 3662.7,-2819.92 3680,-2793.61 3788.39,-2628.78 3723.03,-2549.2 3724,-2351.92 3724.29,-2293.8 3732.4,-2278.17 3721,-2221.17 3685.01,-2041.3 3627.16,-2010.91 3576,-1834.76 3528.51,-1671.25 3449.34,-1252.36 3419,-1084.82 3414.03,-1057.37 3421.38,-978.65 3400,-960.73"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin -->
<g id="edge264" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2077.41,-3174.78C2130.7,-3164.12 2219.27,-3144.01 2292,-3115.87 2350.11,-3093.39 2355.86,-3066.74 2416,-3050.46 2594.85,-3002.06 3079.28,-3088.8 3249,-3014.46 3364.69,-2963.79 3569.03,-2770.38 3642,-2572.77 3654.85,-2537.98 3647.9,-2525.94 3642,-2489.33 3604.63,-2257.54 3476,-2222.62 3476,-1987.84 3476,-1987.84 3476,-1987.84 3476,-1893.45 3476,-1599.3 3172,-1599.72 3172,-1305.57 3172,-1305.57 3172,-1305.57 3172,-1214.95 3172,-1143.02 3150.46,-1121.74 3172,-1053.12"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="edge265" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2078.87,-3178.03C2167.23,-3167.75 2359.3,-3144.16 2520,-3115.87 2656.54,-3091.83 2687.55,-3068.6 2825,-3050.46 2979.86,-3030.02 3379.35,-3065.43 3527,-3014.46 3600.99,-2988.92 3616.14,-2968.89 3671,-2913.05 3817.54,-2763.91 3970,-2358.73 3970,-2329.23 3970,-2329.23 3970,-2329.23 3970,-2243.86 3970,-2076.43 3942.01,-2036.45 3918,-1870.76 3907.72,-1799.81 3894,-1782.95 3894,-1711.26 3894,-1711.26 3894,-1711.26 3894,-1543.51 3894,-1219.64 3371.97,-1169.26 3552,-900.04"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_plugins_core_loader -->
<g id="edge266" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_plugins_core_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2040.8,-3152.17C2050,-3129.99 2065.37,-3101.28 2088,-3084.16"/>
<path fill="none" stroke="black" d="M2088,-3082.16C2151.28,-3034.31 2296,-2436.13 2296,-2329.23 2296,-2329.23 2296,-2329.23 2296,-2160.48 2296,-2096.35 2315.25,-2082.59 2327,-2019.54 2337.22,-1964.69 2346,-1951.24 2346,-1895.45 2346,-1895.45 2346,-1895.45 2346,-1804.26 2346,-1500.32 2278.52,-1410.66 2370,-1120.82 2374.27,-1107.3 2504.36,-912.71 2498,-900.04"/>
<path fill="none" stroke="black" d="M2498,-899.04C2460.57,-847.22 1399.23,-901.27 1348,-863.04 1277.76,-810.62 1300.25,-762.31 1286,-675.83"/>
<path fill="none" stroke="black" d="M1286,-673.83C1281.03,-655.31 1276.14,-634.67 1272.17,-617.02"/>
<polygon fill="#10f9f9" stroke="black" points="1275.61,-616.38 1270.02,-607.38 1268.78,-617.9 1275.61,-616.38"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin -->
<g id="edge267" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4823,-2980.76C4931.99,-2855.98 4819.69,-2773.14 4799,-2608.77 4779.79,-2456.11 4755.58,-2421.03 4735,-2268.55 4675.75,-1829.48 4700.46,-1715.57 4653,-1275.07 4646.5,-1214.78 4607.04,-1193.63 4635,-1139.82"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin -->
<g id="edge268" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4823,-2980.76C4835.44,-2968.2 4835.58,-2962.54 4847,-2949.05 4861.22,-2932.25 4872.87,-2933.5 4881,-2913.05 4894.7,-2878.59 4884.62,-2866.52 4881,-2829.61 4866.26,-2679.21 4749,-2313.61 4749,-2162.48 4749,-2162.48 4749,-2162.48 4749,-1709.26 4749,-1582.44 4737.26,-1256.48 4787,-1139.82"/>
</g>
<!-- plugginger_core_exceptions&#45;&gt;plugginger_plugins_internal_loader -->
<g id="edge269" class="edge">
<title>plugginger_core_exceptions&#45;&gt;plugginger_plugins_internal_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4481,-3082.16C4498.47,-3079.5 4489.62,-3059.17 4505,-3050.46 4634.14,-2977.3 4711.94,-3094.38 4837,-3014.46 5162.96,-2806.16 5015,-2549.31 5015,-2162.48 5015,-2162.48 5015,-2162.48 5015,-1985.84 5015,-1862.64 4977,-1834.46 4977,-1711.26 4977,-1711.26 4977,-1711.26 4977,-1447.79 4977,-1271.57 5015,-1229.34 5015,-1053.12 5015,-1053.12 5015,-1053.12 5015,-958.73 5015,-890.09 5004.3,-872.09 5015,-804.29"/>
</g>
<!-- plugginger_core_types -->
<g id="node60" class="node">
<title>plugginger_core_types</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f9f9" stroke="black" cx="4240" cy="-2981.76" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="4240" y="-2991.38" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4240" y="-2978.63" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="4240" y="-2965.88" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">types</text>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher -->
<g id="edge270" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_dispatcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4193.32,-2970.39C4157.79,-2963 4107.57,-2953.58 4063,-2949.05 3906.09,-2933.1 2794.26,-2960.99 2644,-2913.05 2580.72,-2892.86 2578.34,-2861.38 2520,-2829.61 2484.33,-2810.19 2471.38,-2813.56 2436,-2793.61 2313.12,-2724.35 2302.09,-2675.84 2178,-2608.77 2139.01,-2587.69 2122.6,-2596.24 2085,-2572.77 2082.61,-2571.27 2080.22,-2569.64 2077.88,-2567.91"/>
<polygon fill="#10f9f9" stroke="black" points="2080.26,-2565.33 2070.26,-2561.82 2075.89,-2570.8 2080.26,-2565.33"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors -->
<g id="edge271" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_executors</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4193.31,-2970.45C4157.78,-2963.08 4107.56,-2953.68 4063,-2949.05 3907.49,-2932.9 2764.63,-2986.67 2658,-2872.33"/>
<path fill="none" stroke="black" d="M2658,-2870.33C2564.45,-2770.03 2488.27,-2801.95 2406,-2692.21 2394.52,-2676.89 2404.73,-2665.78 2392,-2651.49"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy -->
<g id="edge272" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_fault_policy</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4193.32,-2970.37C4157.79,-2962.96 4107.57,-2953.54 4063,-2949.05 3723.95,-2914.91 2863.32,-2983.87 2530,-2913.05 2435.79,-2893.04 2419.04,-2866.34 2330,-2829.61 2256.34,-2799.23 2231.89,-2803.63 2164,-2761.91"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge273" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4193.32,-2970.34C4157.8,-2962.91 4107.58,-2953.48 4063,-2949.05 3967.17,-2939.53 2417.68,-2952.88 2330,-2913.05 2284.33,-2892.31 2295.89,-2857.21 2254,-2829.61 2213.16,-2802.7 2195,-2812.8 2150,-2793.61 2121.53,-2781.48 2114.13,-2778.49 2088,-2761.91"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge274" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2658,-2870.33C2566.22,-2786.27 2639.98,-2702.41 2558,-2608.77 2546.09,-2595.16 2286.88,-2463.6 2272,-2453.33 2235.33,-2428.01 2235.39,-2408.76 2196,-2387.92 2141.49,-2359.09 2120.69,-2370.85 2062,-2351.92 2061.19,-2351.66 2060.37,-2351.39 2059.54,-2351.12"/>
<polygon fill="#10f9f9" stroke="black" points="2060.71,-2347.82 2050.11,-2347.89 2058.44,-2354.44 2060.71,-2347.82"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_ai_event_matcher -->
<g id="edge275" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_ai_event_matcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4289.03,-2975.77C4407.02,-2963.04 4703.11,-2926.45 4785,-2872.33"/>
<path fill="none" stroke="black" d="M4785,-2870.33C4801.4,-2859.49 4787,-2182.14 4787,-2162.48 4787,-2162.48 4787,-2162.48 4787,-1985.84 4787,-1862.64 4825,-1834.46 4825,-1711.26 4825,-1711.26 4825,-1711.26 4825,-1620.64 4825,-1233.47 5129,-1191.45 5129,-804.29 5129,-804.29 5129,-804.29 5129,-572.43 5129,-489.94 5198.68,-466.93 5167,-390.77"/>
<path fill="none" stroke="black" d="M5167,-388.77C5153.79,-362.84 5131.17,-339.74 5110.58,-322.35"/>
<polygon fill="#10f9f9" stroke="black" points="5113.04,-319.84 5103.09,-316.22 5108.61,-325.26 5113.04,-319.84"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_ai_service_matcher -->
<g id="edge276" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_ai_service_matcher</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4785,-2870.33C5055.49,-2695.72 4939,-2484.44 4939,-2162.48 4939,-2162.48 4939,-2162.48 4939,-1985.84 4939,-1586.29 5243,-1539.37 5243,-1139.82 5243,-1139.82 5243,-1139.82 5243,-480.6 5243,-429.14 5232.95,-370.7 5224.89,-332.12"/>
<polygon fill="#10f9f9" stroke="black" points="5228.41,-331.82 5222.89,-322.78 5221.57,-333.29 5228.41,-331.82"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_app_plugin -->
<g id="edge277" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2658,-2870.33C2591.34,-2817.65 2651.3,-2570.44 2626,-2489.33 2563.51,-2288.95 2499.97,-2259.07 2391,-2079.67"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_events -->
<g id="edge278" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4248.54,-2949.25C4253.9,-2927.76 4260.3,-2898.56 4263,-2872.33"/>
<path fill="none" stroke="black" d="M4263,-2870.33C4268.08,-2821.01 4201.59,-2839.16 4182,-2793.61 4170.51,-2766.91 4176.36,-2756.72 4182,-2728.21 4196.5,-2654.92 4218.1,-2642.07 4246,-2572.77 4267.3,-2519.85 4279.63,-2508.79 4293,-2453.33 4309.7,-2384.06 4364,-2264.29 4364,-1622.64 4364,-1622.64 4364,-1622.64 4364,-1543.51 4364,-1454.37 4291.6,-1455.87 4217,-1407.07 4128.37,-1349.1 3870.74,-1271.55 3780,-1216.95"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_api_service -->
<g id="edge279" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_api_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4289.31,-2976.6C4377.4,-2968.39 4557.41,-2947.8 4609,-2913.05 4661.18,-2877.9 4666.61,-2853.79 4685,-2793.61 4729.21,-2648.93 4692,-2604.61 4694,-2453.33 4694.38,-2424.26 4695.2,-2416.97 4694,-2387.92 4692.65,-2355.31 4617.53,-1242.32 4597,-1216.95"/>
<path fill="none" stroke="black" d="M4597,-1214.95C4483.51,-1109.38 3985.86,-1066.95 3829.91,-1056.1"/>
<polygon fill="#10f9f9" stroke="black" points="3830.15,-1052.61 3819.94,-1055.42 3829.67,-1059.59 3830.15,-1052.61"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_container -->
<g id="edge280" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_container</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4263,-2870.33C4265.2,-2848.99 4265.44,-2825.16 4265.03,-2805.28"/>
<polygon fill="#10f9f9" stroke="black" points="4268.53,-2805.2 4264.76,-2795.3 4261.53,-2805.39 4268.53,-2805.2"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_events -->
<g id="edge281" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4595,-2870.33C4637.15,-2829.26 4661.29,-2819.95 4671,-2761.91"/>
<path fill="none" stroke="black" d="M4671,-2759.91C4678.95,-2712.38 4662.95,-2699 4671,-2651.49"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_implementations_services -->
<g id="edge282" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4263,-2870.33C4268.08,-2821.01 4312.97,-2832.29 4344,-2793.61 4354.28,-2780.8 4354.88,-2776.19 4363,-2761.91"/>
<path fill="none" stroke="black" d="M4363,-2759.91C4371.12,-2745.63 4372.81,-2741.82 4382,-2728.21 4405.91,-2692.77 4440,-2694.23 4440,-2651.49"/>
<path fill="none" stroke="black" d="M4440,-2649.49C4440,-2585.19 4444.02,-2510.8 4446.97,-2464.97"/>
<polygon fill="#10f9f9" stroke="black" points="4450.44,-2465.4 4447.61,-2455.19 4443.46,-2464.94 4450.44,-2465.4"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_events -->
<g id="edge283" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4277.17,-2959.67C4285.47,-2955.64 4294.37,-2951.82 4303,-2949.05 4430.76,-2908.02 4498.9,-2965.98 4595,-2872.33"/>
<path fill="none" stroke="black" d="M4595,-2870.33C4612.07,-2853.7 4612.01,-2827 4607.59,-2804.26"/>
<polygon fill="#10f9f9" stroke="black" points="4611.02,-2803.58 4605.38,-2794.61 4604.2,-2805.14 4611.02,-2803.58"/>
</g>
<!-- plugginger_core_types&#45;&gt;plugginger_interfaces_services -->
<g id="edge284" class="edge">
<title>plugginger_core_types&#45;&gt;plugginger_interfaces_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4278.29,-2960.53C4286.36,-2956.54 4294.89,-2952.52 4303,-2949.05 4396.68,-2909.03 4504.77,-2973.21 4519,-2872.33"/>
<path fill="none" stroke="black" d="M4519,-2870.33C4523.25,-2840.2 4502.58,-2812.58 4481.33,-2792.76"/>
<polygon fill="#10f9f9" stroke="black" points="4483.83,-2790.3 4474.02,-2786.3 4479.19,-2795.55 4483.83,-2790.3"/>
</g>
<!-- plugginger_discovery -->
<g id="node61" class="node">
<title>plugginger_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="397,-499.6 327,-499.6 327,-463.6 397,-463.6 397,-499.6"/>
<text text-anchor="middle" x="362" y="-484.85" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="362" y="-472.1" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">discovery</text>
</g>
<!-- plugginger_discovery&#45;&gt;plugginger_api_builder -->
<g id="edge285" class="edge">
<title>plugginger_discovery&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M366.59,-500.03C379,-543.39 417.64,-656.32 495,-707.54 501.98,-712.16 1001.23,-776.35 1157.66,-796.37"/>
<polygon fill="blue" stroke="black" points="1156.89,-799.8 1167.25,-797.59 1157.77,-792.85 1156.89,-799.8"/>
</g>
<!-- plugginger_discovery_discovery&#45;&gt;plugginger_discovery -->
<g id="edge287" class="edge">
<title>plugginger_discovery_discovery&#45;&gt;plugginger_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M314.57,-550.22C323.23,-537.88 334.05,-522.44 343.18,-509.43"/>
<polygon fill="blue" stroke="black" points="346.02,-511.48 348.89,-501.29 340.28,-507.47 346.02,-511.48"/>
</g>
<!-- plugginger_discovery_models -->
<g id="node63" class="node">
<title>plugginger_discovery_models</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="396,-982.85 326,-982.85 326,-936.6 396,-936.6 396,-982.85"/>
<text text-anchor="middle" x="361" y="-969.35" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="361" y="-956.6" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">discovery.</text>
<text text-anchor="middle" x="361" y="-943.85" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">models</text>
</g>
<!-- plugginger_discovery_models&#45;&gt;plugginger_api_builder -->
<g id="edge288" class="edge">
<title>plugginger_discovery_models&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M396.42,-952.24C530.42,-927.69 1006.47,-840.47 1157.6,-812.79"/>
<polygon fill="blue" stroke="black" points="1158.04,-816.26 1167.25,-811.02 1156.78,-809.38 1158.04,-816.26"/>
</g>
<!-- plugginger_discovery_models&#45;&gt;plugginger_discovery -->
<g id="edge289" class="edge">
<title>plugginger_discovery_models&#45;&gt;plugginger_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M368.99,-936.34C372.24,-925.26 375.2,-911.6 375,-899.04 372.73,-753.33 365.98,-579.05 363.21,-511.41"/>
<polygon fill="blue" stroke="black" points="366.71,-511.41 362.8,-501.56 359.72,-511.7 366.71,-511.41"/>
</g>
<!-- plugginger_discovery_models&#45;&gt;plugginger_discovery_discovery -->
<g id="edge290" class="edge">
<title>plugginger_discovery_models&#45;&gt;plugginger_discovery_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M363.95,-936.28C364.68,-925.16 364.43,-911.67 361,-900.04"/>
</g>
<!-- plugginger_implementations -->
<g id="node64" class="node">
<title>plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#35a1a1" stroke="black" cx="4450" cy="-2328.23" rx="70.18" ry="23.69"/>
<text text-anchor="middle" x="4450" y="-2331.48" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4450" y="-2318.73" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">implementations</text>
</g>
<!-- plugginger_implementations_container&#45;&gt;plugginger_implementations -->
<g id="edge291" class="edge">
<title>plugginger_implementations_container&#45;&gt;plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4292.15,-2730.84C4301.82,-2719.63 4311.71,-2706.15 4318,-2692.21 4374.43,-2567.23 4296.75,-2504.47 4369,-2387.92 4377.25,-2374.62 4389.73,-2363.41 4402.43,-2354.47"/>
<polygon fill="#38a8a8" stroke="black" points="4404,-2357.63 4410.41,-2349.19 4400.14,-2351.79 4404,-2357.63"/>
</g>
<!-- plugginger_implementations_events&#45;&gt;plugginger_implementations -->
<g id="edge292" class="edge">
<title>plugginger_implementations_events&#45;&gt;plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4568.03,-2394.69C4545.56,-2382.22 4518.41,-2367.17 4495.82,-2354.65"/>
<polygon fill="#309191" stroke="black" points="4497.56,-2351.61 4487.12,-2349.82 4494.17,-2357.73 4497.56,-2351.61"/>
</g>
<!-- plugginger_implementations_services&#45;&gt;plugginger_implementations -->
<g id="edge293" class="edge">
<title>plugginger_implementations_services&#45;&gt;plugginger_implementations</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4450,-2387.5C4450,-2379.75 4450,-2371.44 4450,-2363.59"/>
<polygon fill="#339999" stroke="black" points="4453.5,-2363.68 4450,-2353.68 4446.5,-2363.68 4453.5,-2363.68"/>
</g>
<!-- plugginger_interfaces -->
<g id="node68" class="node">
<title>plugginger_interfaces</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#21bfbf" stroke="black" cx="4594" cy="-2650.49" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="4594" y="-2653.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4594" y="-2640.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">interfaces</text>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_events -->
<g id="edge294" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4604,-2530.05C4598.64,-2508.93 4599.93,-2484.83 4602.98,-2464.72"/>
<polygon fill="#21bfbf" stroke="black" points="4606.42,-2465.34 4604.68,-2454.89 4599.53,-2464.14 4606.42,-2465.34"/>
</g>
<!-- plugginger_interfaces&#45;&gt;plugginger_implementations_services -->
<g id="edge295" class="edge">
<title>plugginger_interfaces&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4600.36,-2626.61C4605.99,-2602.67 4612.16,-2564.18 4604,-2532.05"/>
<path fill="none" stroke="black" d="M4604,-2530.05C4598.09,-2506.77 4548.26,-2474.51 4506.86,-2451.24"/>
<polygon fill="#21bfbf" stroke="black" points="4508.59,-2448.2 4498.14,-2446.41 4505.19,-2454.32 4508.59,-2448.2"/>
</g>
<!-- plugginger_interfaces_events&#45;&gt;plugginger_implementations_events -->
<g id="edge296" class="edge">
<title>plugginger_interfaces_events&#45;&gt;plugginger_implementations_events</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4622.54,-2734.21C4642.11,-2714.06 4665.49,-2684.04 4671,-2651.49"/>
<path fill="none" stroke="black" d="M4671,-2649.49C4679.71,-2598.02 4691.13,-2580.21 4671,-2532.05"/>
<path fill="none" stroke="black" d="M4671,-2530.05C4661.4,-2507.07 4648.5,-2482.52 4637.31,-2462.6"/>
<polygon fill="#2fbcbc" stroke="black" points="4640.37,-2460.91 4632.39,-2453.95 4634.29,-2464.37 4640.37,-2460.91"/>
</g>
<!-- plugginger_interfaces_events&#45;&gt;plugginger_interfaces -->
<g id="edge297" class="edge">
<title>plugginger_interfaces_events&#45;&gt;plugginger_interfaces</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4594,-2727.97C4594,-2714.73 4594,-2699.35 4594,-2685.86"/>
<polygon fill="#2fbcbc" stroke="black" points="4597.5,-2686.12 4594,-2676.12 4590.5,-2686.12 4597.5,-2686.12"/>
</g>
<!-- plugginger_interfaces_services&#45;&gt;plugginger_implementations_services -->
<g id="edge298" class="edge">
<title>plugginger_interfaces_services&#45;&gt;plugginger_implementations_services</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4440,-2728.12C4440,-2706.51 4440,-2677.28 4440,-2651.49"/>
</g>
<!-- plugginger_interfaces_services&#45;&gt;plugginger_interfaces -->
<g id="edge299" class="edge">
<title>plugginger_interfaces_services&#45;&gt;plugginger_interfaces</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4473.42,-2736.38C4498.2,-2718.93 4531.98,-2695.16 4557.47,-2677.2"/>
<polygon fill="#2fbcbc" stroke="black" points="4559.41,-2680.12 4565.57,-2671.5 4555.38,-2674.4 4559.41,-2680.12"/>
</g>
<!-- plugginger_logging -->
<g id="node71" class="node">
<title>plugginger_logging</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3db8b8" stroke="black" cx="1191" cy="-959.73" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1191" y="-962.98" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1191" y="-950.23" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">logging</text>
</g>
<!-- plugginger_logging&#45;&gt;plugginger_api_builder -->
<g id="edge300" class="edge">
<title>plugginger_logging&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1231.99,-945.89C1255.33,-936.09 1278.09,-920.73 1268,-900.04"/>
</g>
<!-- plugginger_logging_analyzer -->
<g id="node72" class="node">
<title>plugginger_logging_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="1191" cy="-1052.12" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="1191" y="-1061.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1191" y="-1048.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">logging.</text>
<text text-anchor="middle" x="1191" y="-1036.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">analyzer</text>
</g>
<!-- plugginger_logging_analyzer&#45;&gt;plugginger_logging -->
<g id="edge301" class="edge">
<title>plugginger_logging_analyzer&#45;&gt;plugginger_logging</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1191,-1018.99C1191,-1011.24 1191,-1002.93 1191,-995.08"/>
<polygon fill="#47c2c2" stroke="black" points="1194.5,-995.18 1191,-985.18 1187.5,-995.18 1194.5,-995.18"/>
</g>
<!-- plugginger_logging_structured_logger -->
<g id="node73" class="node">
<title>plugginger_logging_structured_logger</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="1052" cy="-1052.12" rx="71.77" ry="32.7"/>
<text text-anchor="middle" x="1052" y="-1061.74" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1052" y="-1048.99" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">logging.</text>
<text text-anchor="middle" x="1052" y="-1036.24" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">structured_logger</text>
</g>
<!-- plugginger_logging_structured_logger&#45;&gt;plugginger_logging -->
<g id="edge302" class="edge">
<title>plugginger_logging_structured_logger&#45;&gt;plugginger_logging</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1092.58,-1024.73C1111.68,-1012.3 1134.34,-997.57 1153.1,-985.37"/>
<polygon fill="#47c2c2" stroke="black" points="1154.77,-988.46 1161.24,-980.08 1150.95,-982.6 1154.77,-988.46"/>
</g>
<!-- plugginger_manifest -->
<g id="node74" class="node">
<title>plugginger_manifest</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="734,-1562.51 664,-1562.51 664,-1526.51 734,-1526.51 734,-1562.51"/>
<text text-anchor="middle" x="699" y="-1547.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="699" y="-1535.01" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">manifest</text>
</g>
<!-- plugginger_manifest&#45;&gt;plugginger__internal_validation_manifest_loader -->
<g id="edge303" class="edge">
<title>plugginger_manifest&#45;&gt;plugginger__internal_validation_manifest_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M715,-1447.79C749.2,-1380.73 973.81,-1334.05 1082.83,-1315.06"/>
<polygon fill="blue" stroke="black" points="1083.2,-1318.55 1092.47,-1313.41 1082.02,-1311.65 1083.2,-1318.55"/>
</g>
<!-- plugginger_manifest&#45;&gt;plugginger_discovery_discovery -->
<g id="edge304" class="edge">
<title>plugginger_manifest&#45;&gt;plugginger_discovery_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M699.4,-1526.18C700.42,-1506.79 703.84,-1474.81 715,-1449.79"/>
<path fill="none" stroke="black" d="M715,-1447.79C723.85,-1427.96 700.14,-1425.07 688,-1407.07 623.42,-1311.34 625.11,-1271.95 541,-1192.82 518.35,-1171.51 500.58,-1180.98 481,-1156.82 415.67,-1076.19 464.31,-1021.2 405,-936.04 390.56,-915.3 368.16,-924.27 361,-900.04"/>
</g>
<!-- plugginger_manifest_converter&#45;&gt;plugginger__internal_validation_manifest_loader -->
<g id="edge305" class="edge">
<title>plugginger_manifest_converter&#45;&gt;plugginger__internal_validation_manifest_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M658.49,-1604.82C682.69,-1593.7 715.25,-1578.09 743,-1562.51 872.35,-1489.89 1017.12,-1392.32 1091.64,-1340.82"/>
<polygon fill="blue" stroke="black" points="1093.33,-1343.91 1099.56,-1335.33 1089.35,-1338.15 1093.33,-1343.91"/>
</g>
<!-- plugginger_manifest_converter&#45;&gt;plugginger_discovery_discovery -->
<g id="edge306" class="edge">
<title>plugginger_manifest_converter&#45;&gt;plugginger_discovery_discovery</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M604.37,-1598.03C566.31,-1551.08 478.38,-1438.67 421,-1334.07 342.81,-1191.55 337.18,-1144.71 317,-983.41 314.39,-962.52 307.98,-955.07 317,-936.04 327.82,-913.2 365.13,-924.96 361,-900.04"/>
<path fill="none" stroke="black" d="M361,-899.04C339.78,-799.71 286.91,-774.25 312,-675.83"/>
</g>
<!-- plugginger_manifest_converter&#45;&gt;plugginger_manifest -->
<g id="edge307" class="edge">
<title>plugginger_manifest_converter&#45;&gt;plugginger_manifest</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M645.8,-1598.1C654.64,-1589.36 664.79,-1579.33 673.84,-1570.38"/>
<polygon fill="blue" stroke="black" points="676.04,-1573.12 680.7,-1563.6 671.12,-1568.14 676.04,-1573.12"/>
</g>
<!-- plugginger_plugins -->
<g id="node76" class="node">
<title>plugginger_plugins</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#10f9f9" stroke="black" cx="1648" cy="-573.43" rx="49.5" ry="23.69"/>
<text text-anchor="middle" x="1648" y="-576.68" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1648" y="-563.93" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins</text>
</g>
<!-- plugginger_plugins&#45;&gt;plugginger_cli_cmd_new -->
<g id="edge308" class="edge">
<title>plugginger_plugins&#45;&gt;plugginger_cli_cmd_new</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1680.59,-555.12C1707.3,-538.42 1738.64,-511.48 1725,-482.6"/>
<path fill="none" stroke="black" d="M1725,-480.6C1717.6,-464.92 1710.04,-447.42 1703.63,-432.06"/>
<polygon fill="#10f9f9" stroke="black" points="1707.03,-431.11 1699.97,-423.21 1700.56,-433.79 1707.03,-431.11"/>
</g>
<!-- plugginger_plugins_core_json_validator -->
<g id="node77" class="node">
<title>plugginger_plugins_core_json_validator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2898.5,-832.79 2813.5,-832.79 2813.5,-773.79 2898.5,-773.79 2898.5,-832.79"/>
<text text-anchor="middle" x="2856" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2856" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="2856" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">core.</text>
<text text-anchor="middle" x="2856" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">json_validator</text>
</g>
<!-- plugginger_plugins_core_json_validator_json_validator_plugin&#45;&gt;plugginger_plugins_core_json_validator -->
<g id="edge309" class="edge">
<title>plugginger_plugins_core_json_validator_json_validator_plugin&#45;&gt;plugginger_plugins_core_json_validator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2795.5,-803.29C2797.65,-803.29 2799.8,-803.29 2801.95,-803.29"/>
<polygon fill="blue" stroke="black" points="2801.95,-806.79 2811.95,-803.29 2801.95,-799.79 2801.95,-806.79"/>
</g>
<!-- plugginger_plugins_core_json_validator_services -->
<g id="node79" class="node">
<title>plugginger_plugins_core_json_validator_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="2302" cy="-803.29" rx="61.7" ry="50.73"/>
<text text-anchor="middle" x="2302" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2302" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="2302" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2302" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">json_validator.</text>
<text text-anchor="middle" x="2302" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_plugins_core_json_validator_services&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="edge310" class="edge">
<title>plugginger_plugins_core_json_validator_services&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2334.42,-846.59C2347.24,-860.29 2363.19,-873.74 2381,-881.04 2437.76,-904.28 2600.32,-904.46 2657,-881.04 2674.12,-873.96 2689.46,-861.1 2701.9,-847.86"/>
<polygon fill="#47c2c2" stroke="black" points="2704.21,-850.52 2708.26,-840.73 2698.99,-845.86 2704.21,-850.52"/>
</g>
<!-- plugginger_plugins_core_json_validator_services_ebnf_service -->
<g id="node80" class="node">
<title>plugginger_plugins_core_json_validator_services_ebnf_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="2443" cy="-803.29" rx="61.7" ry="59.75"/>
<text text-anchor="middle" x="2443" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2443" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="2443" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2443" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">json_validator.</text>
<text text-anchor="middle" x="2443" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="2443" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">ebnf_service</text>
</g>
<!-- plugginger_plugins_core_json_validator_services_ebnf_service&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="edge311" class="edge">
<title>plugginger_plugins_core_json_validator_services_ebnf_service&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2480.33,-851.1C2492.49,-863.18 2507.03,-874.56 2523,-881.04 2578.18,-903.44 2601.96,-903.78 2657,-881.04 2674.12,-873.96 2689.46,-861.1 2701.9,-847.86"/>
<polygon fill="#47c2c2" stroke="black" points="2704.21,-850.52 2708.26,-840.73 2698.99,-845.86 2704.21,-850.52"/>
</g>
<!-- plugginger_plugins_core_json_validator_services_schema_service -->
<g id="node81" class="node">
<title>plugginger_plugins_core_json_validator_services_schema_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="2590" cy="-803.29" rx="67" ry="59.75"/>
<text text-anchor="middle" x="2590" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2590" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="2590" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2590" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">json_validator.</text>
<text text-anchor="middle" x="2590" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="2590" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">schema_service</text>
</g>
<!-- plugginger_plugins_core_json_validator_services_schema_service&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="edge312" class="edge">
<title>plugginger_plugins_core_json_validator_services_schema_service&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2657.4,-803.29C2659.26,-803.29 2661.12,-803.29 2662.97,-803.29"/>
<polygon fill="#47c2c2" stroke="black" points="2662.86,-806.79 2672.86,-803.29 2662.86,-799.79 2662.86,-806.79"/>
</g>
<!-- plugginger_plugins_core_json_validator_services_validation_service&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin -->
<g id="edge313" class="edge">
<title>plugginger_plugins_core_json_validator_services_validation_service&#45;&gt;plugginger_plugins_core_json_validator_json_validator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M728.67,-817.83C827.72,-836.84 1010.91,-869.13 1169,-881.04 1251.43,-887.25 2580.6,-912.61 2657,-881.04 2674.12,-873.96 2689.46,-861.1 2701.9,-847.86"/>
<polygon fill="#40bfc0" stroke="black" points="2704.21,-850.52 2708.26,-840.73 2698.99,-845.86 2704.21,-850.52"/>
</g>
<!-- plugginger_plugins_core_llm_provider -->
<g id="node83" class="node">
<title>plugginger_plugins_core_llm_provider</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="2222.75,-832.79 2145.25,-832.79 2145.25,-773.79 2222.75,-773.79 2222.75,-832.79"/>
<text text-anchor="middle" x="2184" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="2184" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="2184" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">core.</text>
<text text-anchor="middle" x="2184" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">llm_provider</text>
</g>
<!-- plugginger_plugins_core_llm_provider_llm_provider_plugin&#45;&gt;plugginger_plugins_core_llm_provider -->
<g id="edge314" class="edge">
<title>plugginger_plugins_core_llm_provider_llm_provider_plugin&#45;&gt;plugginger_plugins_core_llm_provider</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2127.5,-803.29C2129.62,-803.29 2131.73,-803.29 2133.85,-803.29"/>
<polygon fill="blue" stroke="black" points="2133.64,-806.79 2143.64,-803.29 2133.64,-799.79 2133.64,-806.79"/>
</g>
<!-- plugginger_plugins_core_llm_provider_services -->
<g id="node85" class="node">
<title>plugginger_plugins_core_llm_provider_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="1773" cy="-803.29" rx="56.39" ry="50.73"/>
<text text-anchor="middle" x="1773" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1773" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="1773" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1773" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">llm_provider.</text>
<text text-anchor="middle" x="1773" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_plugins_core_llm_provider_services&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin -->
<g id="edge315" class="edge">
<title>plugginger_plugins_core_llm_provider_services&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1803.38,-846.55C1815.52,-860.24 1830.74,-873.71 1848,-881.04 1878.27,-893.9 1965.73,-893.9 1996,-881.04 2012.57,-874 2027.25,-861.31 2039.14,-848.2"/>
<polygon fill="#47c2c2" stroke="black" points="2041.7,-850.59 2045.56,-840.72 2036.39,-846.03 2041.7,-850.59"/>
</g>
<!-- plugginger_plugins_core_llm_provider_services_provider_service&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin -->
<g id="edge316" class="edge">
<title>plugginger_plugins_core_llm_provider_services_provider_service&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1642.72,-843.8C1664.31,-858.57 1690.58,-873.49 1717,-881.04 1776.62,-898.07 1938.94,-905.28 1996,-881.04 2012.57,-874 2027.25,-861.31 2039.14,-848.2"/>
<polygon fill="#40bfc0" stroke="black" points="2041.7,-850.59 2045.56,-840.72 2036.39,-846.03 2041.7,-850.59"/>
</g>
<!-- plugginger_plugins_core_llm_provider_services_validation_service -->
<g id="node87" class="node">
<title>plugginger_plugins_core_llm_provider_services_validation_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="1922" cy="-803.29" rx="74.42" ry="59.75"/>
<text text-anchor="middle" x="1922" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="1922" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="1922" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="1922" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">llm_provider.</text>
<text text-anchor="middle" x="1922" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="1922" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">validation_service</text>
</g>
<!-- plugginger_plugins_core_llm_provider_services_validation_service&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin -->
<g id="edge317" class="edge">
<title>plugginger_plugins_core_llm_provider_services_validation_service&#45;&gt;plugginger_plugins_core_llm_provider_llm_provider_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1996.79,-803.29C1998.78,-803.29 2000.78,-803.29 2002.77,-803.29"/>
<polygon fill="#47c2c2" stroke="black" points="2002.67,-806.79 2012.67,-803.29 2002.67,-799.79 2002.67,-806.79"/>
</g>
<!-- plugginger_plugins_core_wiring_analyzer -->
<g id="node88" class="node">
<title>plugginger_plugins_core_wiring_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="3804.62,-832.79 3711.38,-832.79 3711.38,-773.79 3804.62,-773.79 3804.62,-832.79"/>
<text text-anchor="middle" x="3758" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="3758" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="3758" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">core.</text>
<text text-anchor="middle" x="3758" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">wiring_analyzer</text>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_services -->
<g id="node89" class="node">
<title>plugginger_plugins_core_wiring_analyzer_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="3324" cy="-803.29" rx="67.53" ry="50.73"/>
<text text-anchor="middle" x="3324" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="3324" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="3324" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="3324" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">wiring_analyzer.</text>
<text text-anchor="middle" x="3324" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_services&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="edge318" class="edge">
<title>plugginger_plugins_core_wiring_analyzer_services&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3359.45,-846.65C3373.31,-860.35 3390.36,-873.8 3409,-881.04 3466.17,-903.24 3490.03,-903.75 3547,-881.04 3564.66,-874 3580.69,-861.15 3593.81,-847.9"/>
<polygon fill="#47c2c2" stroke="black" points="3596.24,-850.42 3600.55,-840.74 3591.14,-845.62 3596.24,-850.42"/>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_services_analysis_service -->
<g id="node90" class="node">
<title>plugginger_plugins_core_wiring_analyzer_services_analysis_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="3478" cy="-803.29" rx="68.59" ry="59.75"/>
<text text-anchor="middle" x="3478" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="3478" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="3478" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="3478" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">wiring_analyzer.</text>
<text text-anchor="middle" x="3478" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="3478" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">analysis_service</text>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_services_analysis_service&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="edge319" class="edge">
<title>plugginger_plugins_core_wiring_analyzer_services_analysis_service&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3547.01,-803.29C3548.99,-803.29 3550.98,-803.29 3552.96,-803.29"/>
<polygon fill="#47c2c2" stroke="black" points="3552.79,-806.79 3562.79,-803.29 3552.79,-799.79 3552.79,-806.79"/>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_services_suggestion_service -->
<g id="node91" class="node">
<title>plugginger_plugins_core_wiring_analyzer_services_suggestion_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="2994" cy="-803.29" rx="77.6" ry="59.75"/>
<text text-anchor="middle" x="2994" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="2994" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="2994" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="2994" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">wiring_analyzer.</text>
<text text-anchor="middle" x="2994" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="2994" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">suggestion_service</text>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_services_suggestion_service&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="edge320" class="edge">
<title>plugginger_plugins_core_wiring_analyzer_services_suggestion_service&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3040.29,-851.24C3054.92,-863.33 3072.01,-874.67 3090,-881.04 3137.86,-897.99 3499.83,-899.84 3547,-881.04 3564.66,-874 3580.69,-861.15 3593.81,-847.9"/>
<polygon fill="#47c2c2" stroke="black" points="3596.24,-850.42 3600.55,-840.74 3591.14,-845.62 3596.24,-850.42"/>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_services_validation_service -->
<g id="node92" class="node">
<title>plugginger_plugins_core_wiring_analyzer_services_validation_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="3164" cy="-803.29" rx="74.42" ry="59.75"/>
<text text-anchor="middle" x="3164" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="3164" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="3164" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">core.</text>
<text text-anchor="middle" x="3164" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">wiring_analyzer.</text>
<text text-anchor="middle" x="3164" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="3164" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">validation_service</text>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_services_validation_service&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin -->
<g id="edge321" class="edge">
<title>plugginger_plugins_core_wiring_analyzer_services_validation_service&#45;&gt;plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3208.53,-851.62C3222.45,-863.55 3238.72,-874.71 3256,-881.04 3316.73,-903.26 3486.93,-904.99 3547,-881.04 3564.66,-874 3580.69,-861.15 3593.81,-847.9"/>
<polygon fill="#47c2c2" stroke="black" points="3596.24,-850.42 3600.55,-840.74 3591.14,-845.62 3596.24,-850.42"/>
</g>
<!-- plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin&#45;&gt;plugginger_plugins_core_wiring_analyzer -->
<g id="edge322" class="edge">
<title>plugginger_plugins_core_wiring_analyzer_wiring_analyzer_plugin&#45;&gt;plugginger_plugins_core_wiring_analyzer</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3693.5,-803.29C3695.49,-803.29 3697.48,-803.29 3699.48,-803.29"/>
<polygon fill="blue" stroke="black" points="3699.37,-806.79 3709.37,-803.29 3699.37,-799.79 3699.37,-806.79"/>
</g>
<!-- plugginger_plugins_internal_ai_orchestrator -->
<g id="node95" class="node">
<title>plugginger_plugins_internal_ai_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="4558.75,-832.79 4469.25,-832.79 4469.25,-773.79 4558.75,-773.79 4558.75,-832.79"/>
<text text-anchor="middle" x="4514" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="4514" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="4514" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">internal.</text>
<text text-anchor="middle" x="4514" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">ai_orchestrator</text>
</g>
<!-- plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin&#45;&gt;plugginger_plugins_internal_ai_orchestrator -->
<g id="edge323" class="edge">
<title>plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin&#45;&gt;plugginger_plugins_internal_ai_orchestrator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4451.5,-803.29C4453.61,-803.29 4455.71,-803.29 4457.82,-803.29"/>
<polygon fill="blue" stroke="black" points="4457.56,-806.79 4467.56,-803.29 4457.56,-799.79 4457.56,-806.79"/>
</g>
<!-- plugginger_plugins_internal_ai_orchestrator_services -->
<g id="node97" class="node">
<title>plugginger_plugins_internal_ai_orchestrator_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="3888" cy="-803.29" rx="64.88" ry="50.73"/>
<text text-anchor="middle" x="3888" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="3888" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="3888" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">internal.</text>
<text text-anchor="middle" x="3888" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">ai_orchestrator.</text>
<text text-anchor="middle" x="3888" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_plugins_internal_ai_orchestrator_services&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin -->
<g id="edge324" class="edge">
<title>plugginger_plugins_internal_ai_orchestrator_services&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3921.94,-846.62C3935.28,-860.32 3951.78,-873.77 3970,-881.04 4004.88,-894.95 4273.16,-895.05 4308,-881.04 4325.52,-873.99 4341.38,-861.14 4354.33,-847.89"/>
<polygon fill="#47c2c2" stroke="black" points="4356.73,-850.45 4360.98,-840.74 4351.6,-845.68 4356.73,-850.45"/>
</g>
<!-- plugginger_plugins_internal_ai_orchestrator_services_orchestration_service -->
<g id="node98" class="node">
<title>plugginger_plugins_internal_ai_orchestrator_services_orchestration_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="4056" cy="-803.29" rx="85.56" ry="59.75"/>
<text text-anchor="middle" x="4056" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4056" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="4056" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">internal.</text>
<text text-anchor="middle" x="4056" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">ai_orchestrator.</text>
<text text-anchor="middle" x="4056" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="4056" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">orchestration_service</text>
</g>
<!-- plugginger_plugins_internal_ai_orchestrator_services_orchestration_service&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin -->
<g id="edge325" class="edge">
<title>plugginger_plugins_internal_ai_orchestrator_services_orchestration_service&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4106.99,-851.5C4122.8,-863.51 4141.07,-874.74 4160,-881.04 4222.42,-901.79 4246.97,-905.59 4308,-881.04 4325.52,-873.99 4341.38,-861.14 4354.33,-847.89"/>
<polygon fill="#47c2c2" stroke="black" points="4356.73,-850.45 4360.98,-840.74 4351.6,-845.68 4356.73,-850.45"/>
</g>
<!-- plugginger_plugins_internal_ai_orchestrator_services_validation_service -->
<g id="node99" class="node">
<title>plugginger_plugins_internal_ai_orchestrator_services_validation_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="4234" cy="-803.29" rx="74.42" ry="59.75"/>
<text text-anchor="middle" x="4234" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4234" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="4234" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">internal.</text>
<text text-anchor="middle" x="4234" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">ai_orchestrator.</text>
<text text-anchor="middle" x="4234" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="4234" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">validation_service</text>
</g>
<!-- plugginger_plugins_internal_ai_orchestrator_services_validation_service&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin -->
<g id="edge326" class="edge">
<title>plugginger_plugins_internal_ai_orchestrator_services_validation_service&#45;&gt;plugginger_plugins_internal_ai_orchestrator_ai_orchestrator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4308.78,-803.29C4310.88,-803.29 4312.98,-803.29 4315.08,-803.29"/>
<polygon fill="#47c2c2" stroke="black" points="4314.81,-806.79 4324.81,-803.29 4314.81,-799.79 4314.81,-806.79"/>
</g>
<!-- plugginger_plugins_internal_plugin_generator -->
<g id="node100" class="node">
<title>plugginger_plugins_internal_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="4987.25,-832.79 4888.75,-832.79 4888.75,-773.79 4987.25,-773.79 4987.25,-832.79"/>
<text text-anchor="middle" x="4938" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="4938" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugins.</text>
<text text-anchor="middle" x="4938" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">internal.</text>
<text text-anchor="middle" x="4938" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugin_generator</text>
</g>
<!-- plugginger_plugins_internal_plugin_generator_plugin_generator_plugin&#45;&gt;plugginger_plugins_internal_plugin_generator -->
<g id="edge327" class="edge">
<title>plugginger_plugins_internal_plugin_generator_plugin_generator_plugin&#45;&gt;plugginger_plugins_internal_plugin_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4871.26,-803.29C4873.23,-803.29 4875.2,-803.29 4877.18,-803.29"/>
<polygon fill="blue" stroke="black" points="4876.95,-806.79 4886.95,-803.29 4876.95,-799.79 4876.95,-806.79"/>
</g>
<!-- plugginger_plugins_internal_plugin_generator_services -->
<g id="node102" class="node">
<title>plugginger_plugins_internal_plugin_generator_services</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#47c2c2" stroke="black" cx="4648" cy="-803.29" rx="71.24" ry="50.73"/>
<text text-anchor="middle" x="4648" y="-825.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="4648" y="-812.91" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="4648" y="-800.16" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">internal.</text>
<text text-anchor="middle" x="4648" y="-787.41" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugin_generator.</text>
<text text-anchor="middle" x="4648" y="-774.66" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services</text>
</g>
<!-- plugginger_plugins_internal_plugin_generator_services&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin -->
<g id="edge328" class="edge">
<title>plugginger_plugins_internal_plugin_generator_services&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4719.6,-803.29C4721.57,-803.29 4723.53,-803.29 4725.49,-803.29"/>
<polygon fill="#47c2c2" stroke="black" points="4725.23,-806.79 4735.23,-803.29 4725.23,-799.79 4725.23,-806.79"/>
</g>
<!-- plugginger_plugins_internal_plugin_generator_services_generation_service -->
<g id="node103" class="node">
<title>plugginger_plugins_internal_plugin_generator_services_generation_service</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#40bfc0" stroke="black" cx="219" cy="-803.29" rx="77.07" ry="59.75"/>
<text text-anchor="middle" x="219" y="-832.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="219" y="-819.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugins.</text>
<text text-anchor="middle" x="219" y="-806.54" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">internal.</text>
<text text-anchor="middle" x="219" y="-793.79" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugin_generator.</text>
<text text-anchor="middle" x="219" y="-781.04" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">services.</text>
<text text-anchor="middle" x="219" y="-768.29" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">generation_service</text>
</g>
<!-- plugginger_plugins_internal_plugin_generator_services_generation_service&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin -->
<g id="edge329" class="edge">
<title>plugginger_plugins_internal_plugin_generator_services_generation_service&#45;&gt;plugginger_plugins_internal_plugin_generator_plugin_generator_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M285.05,-834.77C325.35,-852.21 378.47,-872.12 428,-881.04 486.65,-891.59 4663.45,-902.61 4719,-881.04 4737.39,-873.9 4754.23,-860.71 4767.98,-847.21"/>
<polygon fill="#40bfc0" stroke="black" points="4770.19,-849.96 4774.65,-840.35 4765.17,-845.07 4770.19,-849.96"/>
</g>
<!-- plugginger_plugins_internal_loader&#45;&gt;plugginger_cli_cmd_new -->
<g id="edge330" class="edge">
<title>plugginger_plugins_internal_loader&#45;&gt;plugginger_cli_cmd_new</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1779.14,-546.4C1759.53,-530.4 1737.04,-508.09 1725,-482.6"/>
</g>
<!-- plugginger_schemas -->
<g id="node105" class="node">
<title>plugginger_schemas</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="810,-1823.26 740,-1823.26 740,-1787.26 810,-1787.26 810,-1823.26"/>
<text text-anchor="middle" x="775" y="-1808.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="775" y="-1795.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">schemas</text>
</g>
<!-- plugginger_schemas&#45;&gt;plugginger__internal_validation_manifest_loader -->
<g id="edge331" class="edge">
<title>plugginger_schemas&#45;&gt;plugginger__internal_validation_manifest_loader</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M775,-1709.26C792.81,-1556.89 928.38,-1593.59 1042,-1490.51 1045.47,-1487.36 1161.1,-1375.75 1161,-1371.07"/>
<path fill="none" stroke="black" d="M1161,-1370.07C1161.31,-1361.9 1160.03,-1353.38 1158,-1345.31"/>
<polygon fill="blue" stroke="black" points="1161.36,-1344.32 1155.17,-1335.73 1154.65,-1346.31 1161.36,-1344.32"/>
</g>
<!-- plugginger_schemas&#45;&gt;plugginger_api_app -->
<g id="edge332" class="edge">
<title>plugginger_schemas&#45;&gt;plugginger_api_app</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M795.49,-1823.62C862.18,-1880.14 1073.59,-2059.29 1157.84,-2130.69"/>
<polygon fill="blue" stroke="black" points="1155.49,-2133.28 1165.38,-2137.08 1160.01,-2127.94 1155.49,-2133.28"/>
</g>
<!-- plugginger_schemas&#45;&gt;plugginger_api_builder -->
<g id="edge333" class="edge">
<title>plugginger_schemas&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M775,-1620.64C760.34,-1541.68 952,-1385.88 952,-1305.57 952,-1305.57 952,-1305.57 952,-1214.95 952,-1070.83 1140.84,-1180.06 1249,-1084.82 1261.33,-1073.97 1259.52,-1067.19 1268,-1053.12"/>
<path fill="none" stroke="black" d="M1268,-1051.12C1296.29,-990.22 1297.44,-960.39 1268,-900.04"/>
</g>
<!-- plugginger_schemas&#45;&gt;plugginger_cli_cmd_inspect -->
<g id="edge334" class="edge">
<title>plugginger_schemas&#45;&gt;plugginger_cli_cmd_inspect</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M636,-1543.51C598.83,-1490.12 609.72,-1466.39 583,-1407.07 531.61,-1292.98 490.65,-1276.46 454,-1156.82 427,-1068.69 389.87,-830.98 419,-743.54 436.8,-690.1 452.36,-678.93 495,-642.13 542.83,-600.85 569.57,-612.48 620,-574.43"/>
<path fill="none" stroke="black" d="M620,-572.43C659.94,-538.03 715.33,-513.44 755.08,-498.77"/>
<polygon fill="blue" stroke="black" points="756.2,-502.09 764.42,-495.41 753.83,-495.5 756.2,-502.09"/>
</g>
<!-- plugginger_schemas&#45;&gt;plugginger_cli_cmd_schema -->
<g id="edge335" class="edge">
<title>plugginger_schemas&#45;&gt;plugginger_cli_cmd_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M775,-1620.64C751.97,-1530.8 949,-897.03 949,-804.29 949,-804.29 949,-804.29 949,-673.83 949,-649.81 977.05,-480.72 968,-458.47 962.83,-445.76 954,-434.13 944.45,-424.23"/>
<polygon fill="blue" stroke="black" points="947.02,-421.85 937.41,-417.4 942.14,-426.87 947.02,-421.85"/>
</g>
<!-- plugginger_schemas&#45;&gt;plugginger_discovery_models -->
<g id="edge336" class="edge">
<title>plugginger_schemas&#45;&gt;plugginger_discovery_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M775,-1709.26C767.46,-1671.5 782.03,-1660.49 775,-1622.64"/>
<path fill="none" stroke="black" d="M775,-1620.64C762.18,-1551.59 671.63,-1606.02 636,-1545.51"/>
<path fill="none" stroke="black" d="M636,-1543.51C510.63,-1330.57 355.29,-1299.52 374,-1053.12"/>
</g>
<!-- plugginger_schemas&#45;&gt;plugginger_manifest_converter -->
<g id="edge337" class="edge">
<title>plugginger_schemas&#45;&gt;plugginger_manifest_converter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M773.81,-1787.05C772.79,-1768.33 771.93,-1737.56 775,-1711.26"/>
<path fill="none" stroke="black" d="M775,-1709.26C780.8,-1659.65 716.43,-1638.03 669.65,-1628.91"/>
<polygon fill="blue" stroke="black" points="670.43,-1625.5 659.97,-1627.17 669.19,-1632.39 670.43,-1625.5"/>
</g>
<!-- plugginger_schemas_generator&#45;&gt;plugginger_schemas -->
<g id="edge338" class="edge">
<title>plugginger_schemas_generator&#45;&gt;plugginger_schemas</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M2843.81,-1891.95C2755.85,-1888.22 2519.29,-1878.28 2322,-1870.76 1870.93,-1853.57 1757.95,-1854.83 1307,-1834.76 1127.74,-1826.78 914.66,-1814.53 821.96,-1809.06"/>
<polygon fill="blue" stroke="black" points="822.18,-1805.57 811.99,-1808.47 821.76,-1812.56 822.18,-1805.57"/>
</g>
<!-- plugginger_schemas_json -->
<g id="node107" class="node">
<title>plugginger_schemas_json</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#21bfbf" stroke="black" cx="553" cy="-674.83" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="553" y="-684.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="553" y="-671.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">schemas.</text>
<text text-anchor="middle" x="553" y="-658.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">json</text>
</g>
<!-- plugginger_schemas_json&#45;&gt;plugginger_cli_cmd_inspect -->
<g id="edge339" class="edge">
<title>plugginger_schemas_json&#45;&gt;plugginger_cli_cmd_inspect</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M566.75,-643.31C578.12,-621.27 596.24,-592.35 620,-574.43"/>
</g>
<!-- plugginger_schemas_json&#45;&gt;plugginger_cli_cmd_schema -->
<g id="edge340" class="edge">
<title>plugginger_schemas_json&#45;&gt;plugginger_cli_cmd_schema</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M559.16,-642.06C566.21,-612.83 580.25,-569.75 606,-540.72 639.95,-502.44 769.32,-445.08 845.33,-413.55"/>
<polygon fill="#21bfbf" stroke="black" points="846.37,-416.91 854.28,-409.86 843.7,-410.44 846.37,-416.91"/>
</g>
<!-- plugginger_schemas_json_app_graph&#45;&gt;plugginger_cli_cmd_inspect -->
<g id="edge341" class="edge">
<title>plugginger_schemas_json_app_graph&#45;&gt;plugginger_cli_cmd_inspect</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M507.87,-770.24C517.66,-761.01 528.84,-751.33 540,-743.54 581.03,-714.87 628.4,-725.86 630,-675.83"/>
<path fill="none" stroke="black" d="M630,-673.83C631.42,-629.45 584.56,-601.17 620,-574.43"/>
</g>
<!-- plugginger_schemas_json_app_graph&#45;&gt;plugginger_schemas_json -->
<g id="edge342" class="edge">
<title>plugginger_schemas_json_app_graph&#45;&gt;plugginger_schemas_json</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M498.98,-765.71C508.49,-749.89 519.64,-731.33 529.34,-715.21"/>
<polygon fill="#33cccc" stroke="black" points="532.22,-717.19 534.38,-706.82 526.22,-713.59 532.22,-717.19"/>
</g>
<!-- plugginger_schemas_manifest -->
<g id="node109" class="node">
<title>plugginger_schemas_manifest</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#19e6e6" stroke="black" cx="609" cy="-1986.84" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="609" y="-1996.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="609" y="-1983.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">schemas.</text>
<text text-anchor="middle" x="609" y="-1970.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">manifest</text>
</g>
<!-- plugginger_schemas_manifest&#45;&gt;plugginger_discovery_models -->
<g id="edge343" class="edge">
<title>plugginger_schemas_manifest&#45;&gt;plugginger_discovery_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M616.23,-1954.05C626.73,-1902.37 642.91,-1797.29 623,-1711.26"/>
<path fill="none" stroke="black" d="M623,-1709.26C613.26,-1678.5 450,-1517.29 432,-1490.51 415.43,-1465.86 379.05,-1399.92 372,-1371.07 338.44,-1233.8 359.45,-1193.68 374,-1053.12"/>
<path fill="none" stroke="black" d="M374,-1051.12C373.18,-1032.2 370.29,-1011.26 367.45,-994.3"/>
<polygon fill="#19e6e6" stroke="black" points="370.95,-994 365.77,-984.76 364.05,-995.21 370.95,-994"/>
</g>
<!-- plugginger_schemas_manifest&#45;&gt;plugginger_manifest_converter -->
<g id="edge344" class="edge">
<title>plugginger_schemas_manifest&#45;&gt;plugginger_manifest_converter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M623,-1709.26C619.02,-1692.05 618.69,-1672.39 619.5,-1656.15"/>
<polygon fill="#19e6e6" stroke="black" points="622.97,-1656.75 620.16,-1646.53 615.98,-1656.27 622.97,-1656.75"/>
</g>
<!-- plugginger_schemas_manifest&#45;&gt;plugginger_schemas -->
<g id="edge345" class="edge">
<title>plugginger_schemas_manifest&#45;&gt;plugginger_schemas</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M634.21,-1958.57C666.02,-1924.16 720.3,-1865.44 751.35,-1831.85"/>
<polygon fill="#19e6e6" stroke="black" points="753.84,-1834.31 758.05,-1824.59 748.7,-1829.56 753.84,-1834.31"/>
</g>
<!-- plugginger_schemas_manifest&#45;&gt;plugginger_schemas_generator -->
<g id="edge346" class="edge">
<title>plugginger_schemas_manifest&#45;&gt;plugginger_schemas_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M658.7,-1983.25C769.38,-1977.53 1044.55,-1963.58 1275,-1954.14 1740.22,-1935.07 1856.72,-1935.61 2322,-1918.14 2511.4,-1911.02 2736.97,-1901.52 2832.54,-1897.44"/>
<polygon fill="#19e6e6" stroke="black" points="2832.46,-1900.95 2842.3,-1897.02 2832.16,-1893.95 2832.46,-1900.95"/>
</g>
<!-- plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate -->
<g id="edge347" class="edge">
<title>plugginger_stubgen&#45;&gt;plugginger_cli_cmd_stubs_generate</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M3049.28,-1870.8C3020.32,-1785.53 2915.89,-1475.54 2840,-1216.95"/>
</g>
<!-- plugginger_testing -->
<g id="node111" class="node">
<title>plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<polygon fill="blue" stroke="black" points="5400,-1823.26 5330,-1823.26 5330,-1787.26 5400,-1787.26 5400,-1823.26"/>
<text text-anchor="middle" x="5365" y="-1808.51" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">plugginger.</text>
<text text-anchor="middle" x="5365" y="-1795.76" font-family="Helvetica,sans-Serif" font-size="10.00" fill="white">testing</text>
</g>
<!-- plugginger_testing_collectors -->
<g id="node112" class="node">
<title>plugginger_testing_collectors</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3bcece" stroke="black" cx="5320" cy="-1986.84" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="5320" y="-1996.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="5320" y="-1983.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="5320" y="-1970.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">collectors</text>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing -->
<g id="edge348" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5359.21,-1966.53C5384.45,-1951.03 5412.17,-1926.71 5410,-1895.45"/>
<path fill="none" stroke="black" d="M5410,-1893.45C5408.46,-1871.29 5396.81,-1849.08 5385.75,-1832.62"/>
<polygon fill="#3bcece" stroke="black" points="5388.9,-1831.01 5380.27,-1824.88 5383.19,-1835.06 5388.9,-1831.01"/>
</g>
<!-- plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers -->
<g id="edge349" class="edge">
<title>plugginger_testing_collectors&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5320,-1953.72C5320,-1945.74 5320,-1937.17 5320,-1929.12"/>
<polygon fill="#3bcece" stroke="black" points="5323.5,-1929.34 5320,-1919.34 5316.5,-1929.34 5323.5,-1929.34"/>
</g>
<!-- plugginger_testing_helpers&#45;&gt;plugginger_testing -->
<g id="edge350" class="edge">
<title>plugginger_testing_helpers&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5331.59,-1871C5337.47,-1859.6 5344.66,-1845.66 5350.87,-1833.64"/>
<polygon fill="blue" stroke="black" points="5353.84,-1835.52 5355.31,-1825.03 5347.62,-1832.31 5353.84,-1835.52"/>
</g>
<!-- plugginger_testing_mock_app -->
<g id="node114" class="node">
<title>plugginger_testing_mock_app</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#3bcece" stroke="black" cx="5437" cy="-1986.84" rx="49.5" ry="32.7"/>
<text text-anchor="middle" x="5437" y="-1996.46" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">plugginger.</text>
<text text-anchor="middle" x="5437" y="-1983.71" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">testing.</text>
<text text-anchor="middle" x="5437" y="-1970.96" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#000000">mock_app</text>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing -->
<g id="edge351" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5424.14,-1955.1C5417.89,-1937.9 5411.42,-1915.87 5410,-1895.45"/>
</g>
<!-- plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers -->
<g id="edge352" class="edge">
<title>plugginger_testing_mock_app&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5405.34,-1961.38C5390.74,-1950.1 5373.3,-1936.63 5358,-1924.81"/>
<polygon fill="#3bcece" stroke="black" points="5360.53,-1922.34 5350.48,-1918.99 5356.25,-1927.88 5360.53,-1922.34"/>
</g>
<!-- pydantic -->
<g id="node115" class="node">
<title>pydantic</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#0606f9" stroke="black" cx="1723" cy="-3271.27" rx="35.49" ry="18"/>
<text text-anchor="middle" x="1723" y="-3268.15" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">pydantic</text>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_builder_phases_app_config_resolver -->
<g id="edge353" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_builder_phases_app_config_resolver</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M306,-3082.16C273.52,-3021.04 663,-2658.65 711,-2608.77 721.84,-2597.5 733.97,-2585.82 745.54,-2575.07"/>
<polygon fill="#0606f9" stroke="black" points="747.74,-2577.8 752.72,-2568.45 743,-2572.65 747.74,-2577.8"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator -->
<g id="edge354" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_builder_phases_plugin_instantiator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M590,-3183.57C545.96,-3129.18 1172.73,-3051.27 1187,-2982.76"/>
<path fill="none" stroke="black" d="M1187,-2980.76C1199.09,-2934.36 1127.71,-2954.15 1103,-2913.05 1075.14,-2866.72 1090.09,-2846.72 1080,-2793.61 1071.44,-2748.57 1066.1,-2737.78 1061,-2692.21 1041.84,-2520.95 1032.68,-2475.54 1054,-2304.55 1079.08,-2103.33 1165.49,-2076.91 1256,-1895.45"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_lifecycle -->
<g id="edge355" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_lifecycle</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1687.62,-3268.24C1609.55,-3261.63 1423.19,-3233.64 1344,-3115.87 1302.63,-3054.34 1310.42,-3015.15 1344,-2949.05 1457.77,-2725.08 1754.54,-2592.71 1869.02,-2548.62"/>
<polygon fill="#0606f9" stroke="black" points="1870,-2551.99 1878.11,-2545.16 1867.52,-2545.45 1870,-2551.99"/>
</g>
<!-- pydantic&#45;&gt;plugginger__internal_runtime_facade -->
<g id="edge356" class="edge">
<title>pydantic&#45;&gt;plugginger__internal_runtime_facade</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1187,-2980.76C1256.43,-2626.55 1437.79,-2547.71 1776,-2421.63"/>
<path fill="none" stroke="black" d="M1776,-2419.63C1833.31,-2395.95 1899.17,-2369.72 1944.96,-2351.64"/>
<polygon fill="#0606f9" stroke="black" points="1946.14,-2354.94 1954.16,-2348.01 1943.57,-2348.43 1946.14,-2354.94"/>
</g>
<!-- pydantic&#45;&gt;plugginger_ai_json_validator -->
<g id="edge357" class="edge">
<title>pydantic&#45;&gt;plugginger_ai_json_validator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5552,-480.6C5526.66,-383.52 5587.88,-339.65 5533,-255.66 5520.5,-236.52 5499.46,-223.18 5479.33,-214.15"/>
<polygon fill="#0606f9" stroke="black" points="5480.91,-211.02 5470.34,-210.4 5478.22,-217.48 5480.91,-211.02"/>
</g>
<!-- pydantic&#45;&gt;plugginger_ai_types -->
<g id="edge358" class="edge">
<title>pydantic&#45;&gt;plugginger_ai_types</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M5357,-2530.05C5309.29,-2238.87 5552,-2190.51 5552,-1895.45 5552,-1895.45 5552,-1895.45 5552,-673.83 5552,-588.84 5570.65,-565.52 5552,-482.6"/>
<path fill="none" stroke="black" d="M5552,-480.6C5545.64,-456.23 5527.18,-435.5 5508.89,-420.11"/>
<polygon fill="#0606f9" stroke="black" points="5511.16,-417.44 5501.16,-413.94 5506.79,-422.91 5511.16,-417.44"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_app_plugin -->
<g id="edge359" class="edge">
<title>pydantic&#45;&gt;plugginger_api_app_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1758.65,-3269.46C1840.14,-3266.92 2036.97,-3256.75 2088,-3217.27 2261.35,-3083.19 2258,-2465.01 2258,-2245.86 2258,-2245.86 2258,-2245.86 2258,-2160.48 2258,-2111.64 2267.92,-2055.82 2275.26,-2021.2"/>
<polygon fill="#0606f9" stroke="black" points="2278.6,-2022.31 2277.31,-2011.79 2271.77,-2020.82 2278.6,-2022.31"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_builder -->
<g id="edge360" class="edge">
<title>pydantic&#45;&gt;plugginger_api_builder</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M306,-2870.33C236.4,-2708 365.06,-2657.52 419,-2489.33 433.56,-2443.93 431.26,-2429.83 454,-2387.92 500.57,-2302.11 521.52,-2283.1 597,-2221.17 620.46,-2201.93 634.54,-2207.58 655,-2185.17 682.76,-2154.77 720.51,-2056.81 738,-2019.54 776.1,-1938.37 792.9,-1920.55 819,-1834.76 853.78,-1720.46 916.68,-1425.02 914,-1305.57"/>
<path fill="none" stroke="black" d="M914,-1303.57C892.06,-1193.14 939.91,-1165.05 952,-1053.12"/>
<path fill="none" stroke="black" d="M952,-1051.12C959.16,-968.48 1068.84,-960.77 1133,-936.04 1190.94,-913.7 1306.2,-948.99 1268,-900.04"/>
<path fill="none" stroke="black" d="M1268,-899.04C1252.33,-878.83 1236.22,-854.96 1224.08,-836.23"/>
<polygon fill="#0606f9" stroke="black" points="1227.12,-834.48 1218.77,-827.96 1221.23,-838.27 1227.12,-834.48"/>
</g>
<!-- pydantic&#45;&gt;plugginger_api_plugin -->
<g id="edge361" class="edge">
<title>pydantic&#45;&gt;plugginger_api_plugin</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1758.71,-3269.87C2087.01,-3266.1 4569.95,-3235.91 4626,-3185.57"/>
<path fill="none" stroke="black" d="M4626,-3183.57C4782.59,-3042.95 4322.07,-3221.98 4163,-3084.16"/>
<path fill="none" stroke="black" d="M4163,-3082.16C4146.91,-3068.23 4082.49,-2932.1 4073,-2913.05 4032.59,-2831.95 4047.94,-2795.26 3987,-2728.21 3764.81,-2483.75 3619.19,-2537.66 3346,-2351.92 3218.19,-2265.03 3209.85,-2207.68 3072,-2137.79 3020.54,-2111.71 2955.72,-2095.88 2912.68,-2087.47"/>
<polygon fill="#0606f9" stroke="black" points="2913.38,-2084.04 2902.91,-2085.62 2912.08,-2090.92 2913.38,-2084.04"/>
</g>
<!-- pydantic&#45;&gt;plugginger_cli_cmd_project_run -->
<g id="edge362" class="edge">
<title>pydantic&#45;&gt;plugginger_cli_cmd_project_run</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M1687.22,-3270.34C1503.16,-3270.32 668.53,-3265.78 590,-3185.57"/>
<path fill="none" stroke="black" d="M590,-3183.57C506.46,-3098.25 429.3,-3182.18 330,-3115.87 315.3,-3106.05 318.14,-3097.01 306,-3084.16"/>
<path fill="none" stroke="black" d="M306,-3082.16C183.24,-2952.3 93.14,-2959.22 26,-2793.61 15.08,-2766.67 11.16,-2753.2 26,-2728.21 43.06,-2699.47 70.84,-2718.06 92,-2692.21 162.4,-2606.2 149.37,-2563.22 166,-2453.33 179.83,-2361.95 152,-2338.28 152,-2245.86 152,-2245.86 152,-2245.86 152,-2077.67 152,-1758.65 225.57,-1668.78 408,-1407.07 520.77,-1245.3 598.17,-1245.14 713,-1084.82 830.48,-920.79 882.69,-875.74 910,-675.83"/>
</g>
<!-- pydantic&#45;&gt;plugginger_config_models -->
<g id="edge363" class="edge">
<title>pydantic&#45;&gt;plugginger_config_models</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M590,-3183.57C566.54,-3164.61 749.14,-3058.79 838.57,-3008.85"/>
<polygon fill="#0606f9" stroke="black" points="840.12,-3011.99 847.15,-3004.07 836.71,-3005.88 840.12,-3011.99"/>
</g>
<!-- pydantic&#45;&gt;plugginger_config_typed_config -->
<g id="edge364" class="edge">
<title>pydantic&#45;&gt;plugginger_config_typed_config</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M590,-3183.57C540.27,-3129.39 478.98,-3177.6 439,-3115.87 423.2,-3091.47 420.67,-3073.02 439,-3050.46 455.27,-3030.44 608.13,-3005.19 697.96,-2991.9"/>
<polygon fill="#0606f9" stroke="black" points="698.24,-2995.4 707.62,-2990.49 697.22,-2988.47 698.24,-2995.4"/>
</g>
<!-- pydantic&#45;&gt;plugginger_manifest_converter -->
<g id="edge365" class="edge">
<title>pydantic&#45;&gt;plugginger_manifest_converter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M40,-2759.91C34.69,-2680.82 148.39,-2747.7 205,-2692.21 310.27,-2589.01 276.43,-2522.32 337,-2387.92 390.93,-2268.26 402.32,-2231.72 494,-2137.79 532.31,-2098.55 539.94,-2085.32 586,-2055.54 619.08,-2034.16 642.22,-2050.17 667,-2019.54 727.94,-1944.23 697.56,-1902.06 712,-1806.26"/>
<path fill="none" stroke="black" d="M712,-1804.26C711.31,-1746.81 675.15,-1688.61 649.14,-1654.08"/>
<polygon fill="#0606f9" stroke="black" points="651.92,-1651.95 643.03,-1646.18 646.38,-1656.24 651.92,-1651.95"/>
</g>
<!-- pydantic&#45;&gt;plugginger_schemas_generator -->
<g id="edge366" class="edge">
<title>pydantic&#45;&gt;plugginger_schemas_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4163,-3082.16C4117.95,-3039.52 4142.12,-3007.38 4121,-2949.05 4061.03,-2783.47 4056.68,-2717.3 3918,-2608.77 3893.37,-2589.49 3439.99,-2373.13 3417,-2351.92 3366.75,-2305.58 3381.45,-2272.24 3336,-2221.17 3240.18,-2113.5 3182.74,-2122.62 3082,-2019.54 3069.65,-2006.9 3073.03,-1997.13 3058,-1987.84"/>
</g>
<!-- pydantic&#45;&gt;plugginger_schemas_manifest -->
<g id="edge367" class="edge">
<title>pydantic&#45;&gt;plugginger_schemas_manifest</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M306,-3082.16C242.71,-3013.67 345.63,-2956.75 306,-2872.33"/>
<path fill="none" stroke="black" d="M306,-2870.33C255.7,-2753 48.48,-2889.29 40,-2761.91"/>
<path fill="none" stroke="black" d="M40,-2759.91C35.72,-2695.7 130.39,-2744.43 168,-2692.21 263.54,-2559.51 228,-2492.74 228,-2329.23 228,-2329.23 228,-2329.23 228,-2160.48 228,-2021.75 440.17,-1993.93 547.85,-1988.75"/>
<polygon fill="#0606f9" stroke="black" points="547.84,-1992.25 557.68,-1988.33 547.54,-1985.26 547.84,-1992.25"/>
</g>
<!-- pydantic&#45;&gt;plugginger_testing_helpers -->
<g id="edge368" class="edge">
<title>pydantic&#45;&gt;plugginger_testing_helpers</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M4626,-3183.57C4782.04,-3043.44 4907.85,-3214.38 5093,-3115.87 5344.4,-2982.1 5403.98,-2812.92 5357,-2532.05"/>
<path fill="none" stroke="black" d="M5357,-2530.05C5336.27,-2436.9 5281,-2424.66 5281,-2329.23 5281,-2329.23 5281,-2329.23 5281,-2243.86 5281,-2114.82 5212.97,-2073.5 5262,-1954.14 5266.33,-1943.6 5273.56,-1933.95 5281.48,-1925.65"/>
<polygon fill="#0606f9" stroke="black" points="5283.72,-1928.35 5288.45,-1918.87 5278.84,-1923.33 5283.72,-1928.35"/>
</g>
<!-- yaml -->
<g id="node116" class="node">
<title>yaml</title><style>.edge>path:hover{stroke-width:8}</style>
<ellipse fill="#ce3bce" stroke="black" cx="619" cy="-2161.48" rx="27" ry="18"/>
<text text-anchor="middle" x="619" y="-2158.36" font-family="Helvetica,sans-Serif" font-size="10.00" fill="#ffffff">yaml</text>
</g>
<!-- yaml&#45;&gt;plugginger_manifest_converter -->
<g id="edge369" class="edge">
<title>yaml&#45;&gt;plugginger_manifest_converter</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M626.15,-2143.95C635.55,-2122.81 653.11,-2085.38 672,-2055.54 682.84,-2038.42 687.66,-2035.62 700,-2019.54 710.76,-2005.52 717.35,-2004.21 724,-1987.84"/>
</g>
<!-- yaml&#45;&gt;plugginger_plugins_internal_plugin_generator_services_generation_service -->
<g id="edge370" class="edge">
<title>yaml&#45;&gt;plugginger_plugins_internal_plugin_generator_services_generation_service</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M600.42,-2148.02C516.86,-2090.37 180,-1839.68 180,-1545.51 180,-1545.51 180,-1545.51 180,-958.73 180,-929.68 186.56,-898.47 194.31,-871.77"/>
<polygon fill="#ce3bce" stroke="black" points="197.58,-873.04 197.12,-862.45 190.88,-871.01 197.58,-873.04"/>
</g>
<!-- yaml&#45;&gt;plugginger_schemas_generator -->
<g id="edge371" class="edge">
<title>yaml&#45;&gt;plugginger_schemas_generator</title><style>.edge>path:hover{stroke-width:8}</style>
<path fill="none" stroke="black" d="M646.21,-2159.22C806.31,-2151.75 1620.47,-2113.35 1672,-2101.79 1725.35,-2089.83 1733.81,-2071.85 1786,-2055.54 1982.93,-1994.02 2035.18,-1986.16 2239,-1954.14 2459.72,-1919.46 2726.72,-1903.12 2832.56,-1897.65"/>
<polygon fill="#ce3bce" stroke="black" points="2832.45,-1901.16 2842.26,-1897.16 2832.09,-1894.17 2832.45,-1901.16"/>
</g>
</g>
</svg>
