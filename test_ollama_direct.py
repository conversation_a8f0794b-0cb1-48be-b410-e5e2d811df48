#!/usr/bin/env python3
"""
Direct Ollama API Test - Framework Independent
"""

import asyncio
import aiohttp
import json
import time


async def test_ollama_simple():
    """Test simple text generation with Ollama."""
    print("🔄 Testing Ollama Simple Text Generation...")
    
    url = "http://localhost:11434/api/generate"
    data = {
        "model": "granite3-dense:2b",
        "prompt": "Hello, how are you?",
        "options": {
            "num_predict": 20,
            "temperature": 0.1
        },
        "stream": False
    }
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    result = await response.json()
                    elapsed = time.time() - start_time
                    
                    print(f"✅ Success! ({elapsed:.2f}s)")
                    print(f"   Response: {result.get('response', '')[:100]}...")
                    print(f"   Model: {result.get('model', 'unknown')}")
                    print(f"   Done: {result.get('done', False)}")
                    print(f"   Tokens: {result.get('eval_count', 0)}")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Exception after {elapsed:.2f}s: {e}")
        return False


async def test_ollama_json():
    """Test JSON generation with Ollama."""
    print("\n🔄 Testing Ollama JSON Generation...")
    
    url = "http://localhost:11434/api/generate"
    data = {
        "model": "granite3-dense:2b",
        "prompt": "Create a JSON object with name and description for a hello world plugin. Respond only with JSON.",
        "options": {
            "num_predict": 50,
            "temperature": 0.1
        },
        "format": "json",
        "stream": False
    }
    
    start_time = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, timeout=aiohttp.ClientTimeout(total=30)) as response:
                if response.status == 200:
                    result = await response.json()
                    elapsed = time.time() - start_time
                    
                    content = result.get('response', '')
                    print(f"✅ Success! ({elapsed:.2f}s)")
                    print(f"   Raw Response: {content}")
                    
                    # Try to parse JSON
                    try:
                        parsed = json.loads(content)
                        print(f"   ✅ Valid JSON: {parsed}")
                        return True
                    except json.JSONDecodeError as e:
                        print(f"   ❌ Invalid JSON: {e}")
                        return False
                        
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Exception after {elapsed:.2f}s: {e}")
        return False


async def test_ollama_models():
    """Test available models in Ollama."""
    print("\n🔄 Testing Ollama Available Models...")
    
    url = "http://localhost:11434/api/tags"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    result = await response.json()
                    models = result.get('models', [])
                    
                    print(f"✅ Found {len(models)} models:")
                    for model in models:
                        name = model.get('name', 'unknown')
                        size = model.get('size', 0) / (1024*1024*1024)  # GB
                        print(f"   - {name} ({size:.1f}GB)")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP Error {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


async def main():
    """Run all Ollama tests."""
    print("🚀 Starting Ollama Direct API Tests\n")
    
    results = []
    
    # Test 1: Available models
    results.append(await test_ollama_models())
    
    # Test 2: Simple text generation
    results.append(await test_ollama_simple())
    
    # Test 3: JSON generation
    results.append(await test_ollama_json())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All Ollama tests passed!")
    else:
        print("⚠️  Some Ollama tests failed!")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
