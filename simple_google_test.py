#!/usr/bin/env python3
"""
Simple Google API test using only HTTP requests.
"""

import json
import urllib.request
import urllib.parse

def test_google_api():
    """Test Google API with direct HTTP request."""
    api_key = "AIzaSyBiLblWg2Cl5Q62Oap0TWg7DEgby5wSR0s"
    
    # Google Generative AI API endpoint
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    # Request payload
    payload = {
        "contents": [{
            "parts": [{
                "text": "Say hello in exactly 3 words"
            }]
        }],
        "generationConfig": {
            "maxOutputTokens": 10,
            "temperature": 0.1
        }
    }
    
    try:
        # Create request
        data = json.dumps(payload).encode('utf-8')
        req = urllib.request.Request(
            url,
            data=data,
            headers={
                'Content-Type': 'application/json',
                'Content-Length': str(len(data))
            }
        )
        
        print(f"Testing Google API Key: {api_key[:20]}...")
        print(f"URL: {url[:80]}...")
        
        # Make request
        with urllib.request.urlopen(req, timeout=15) as response:
            response_data = json.loads(response.read().decode('utf-8'))
            
            if response.status == 200:
                if "candidates" in response_data and response_data["candidates"]:
                    text = response_data["candidates"][0]["content"]["parts"][0]["text"]
                    print(f"✅ SUCCESS: API key is valid!")
                    print(f"Response: {text}")
                    return True
                else:
                    print(f"❌ No content in response: {response_data}")
                    return False
            else:
                print(f"❌ HTTP {response.status}")
                return False
                
    except urllib.error.HTTPError as e:
        error_data = e.read().decode('utf-8')
        print(f"❌ HTTP Error {e.code}: {error_data}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_google_api()
    if success:
        print("\n🎉 Google API Key is working!")
        exit(0)
    else:
        print("\n💥 Google API Key test failed!")
        exit(1)
